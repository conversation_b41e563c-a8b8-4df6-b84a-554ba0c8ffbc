import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
import { xundaUtils } from '@/utils/xunda';
const { t } = useI18n();

// 基础Api
export const courseApi = '/api/ea/course';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: courseApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: courseApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: courseApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: courseApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: courseApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: courseApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: courseApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: courseApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: courseApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: courseApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: courseApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: courseApi + `/exportExceptionData`, data });
}

// 获取下拉列表
export function getCourseTypeForSelect() {
  return getDictionaryDataSelector('721717097394245');
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'no',
    label: t('课程号'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
  {
    field: 'name',
    label: t('课程名(中)'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('课程类型'),
    dataIndex: 'courseType',
    width: 120,
  },
  {
    title: t('课程号'),
    dataIndex: 'no',
    width: 120,
  },
  {
    title: t('课程名(中)'),
    dataIndex: 'name',
    width: 120,
  },
  {
    title: t('课程名(英)'),
    dataIndex: 'englishName',
    width: 120,
  },
  {
    title: t('是否免费'),
    dataIndex: 'freeFlag',
    width: 120,
    customRender: xundaUtils.customRenderBoolean,
  },
  {
    title: t('说明'),
    dataIndex: 'description',
    width: 120,
  },
];
