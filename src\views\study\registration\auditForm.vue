<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    :cancelText="t('common.closeText')"
    @cancel="handleClose"
    :default-fullscreen="true"
    class="audit-modal"
    destroyOnClose>
    <template #title>
      <div class="steps-wrapper">
        <a-steps v-model:current="currentStep" type="navigation" size="small" :key="key">
          <a-step v-for="(item, index) in getStepList" :key="item" :title="item" :disabled="!isAuditPass" />
        </a-steps>
      </div>
    </template>
    <template #footer>
      <a-space :size="12" class="footer-buttons">
        <a-button @click="handleReload" class="reload-btn">
          <template #icon><sync-outlined /></template>
          {{ t('重置') }}
        </a-button>
        <a-button @click="handlePrev" :loading="prevLoading" :disabled="currentStep <= 0" class="nav-btn prev-btn">
          <template #icon><left-outlined /></template>
          {{ t('common.prev') }}
        </a-button>
        <a-button @click="handleNext" :loading="nextLoading" :disabled="currentStep >= getStepList.length - 1" class="nav-btn next-btn">
          {{ t('common.next') }}
          <template #icon><right-outlined /></template>
        </a-button>
        <a-button type="success" v-if="isAuditPass || isAuditReject" @click="handleAuditNext" class="nav-btn">
          {{ t('审核下一个') }}
          <template #icon><FastForwardOutlined /></template>
        </a-button>
        <a-button danger @click="handleClose" class="close-btn">
          <template #icon><close-outlined /></template>
          {{ t('common.closeText') }}
        </a-button>
      </a-space>
    </template>
    <!-- 使用动态组件替代多个条件渲染 -->

    <div class="form-layout">
      <div class="component-scroll-container">
        <component
          :is="currentComponent?.component"
          ref="currentFormRef"
          v-model:registrationId="registrationId"
          v-model:auditState="auditState"
          v-model:semesterCatogry="semesterCatogry"
          @changeLoading="changeLoading"
          @prev="handlePrev"
          @next="handleNext"
          @finish="handleFinish" />
      </div>
      <div class="toggle-btn" @click="toggleSidebar">
        <span>{{ sidebarCollapsed ? '展开审核页' : '收起审核页' }}</span>
      </div>
      <div
        class="sidebar-container"
        :class="{ collapsed: sidebarCollapsed }"
        :style="{
          boxShadow: sidebarCollapsed ? 'none' : '0 0 10px rgba(0, 0, 0, 0.1)',
          border: sidebarCollapsed ? 'none' : '1px solid #e8e8e8',
          borderRadius: '4px',
        }">
        <NewAuditSubmitForm
          v-show="!sidebarCollapsed"
          ref="auditSubmitFormRef"
          :registrationId="registrationId"
          v-model:auditState="auditState"
          v-model:semesterCatogry="semesterCatogry"
          @changeLoading="changeLoading"
          :canAudit="canAudit" />
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, reactive, watch, nextTick, computed, toRefs, onMounted } from 'vue';
  import { LeftOutlined, RightOutlined, SyncOutlined, CloseOutlined, FastForwardOutlined } from '@ant-design/icons-vue';
  import { BasicModal, useModalInner, useModal } from '@/components/Modal';
  import IdCardInfoForm from './components/idCardForm/index.vue';
  import ApplicationForm from './components/applicationForm/index.vue';
  import WorkExperienceForm from './components/workExperienceForm/index.vue';
  import BasicInfoForm from './components/basicInfoForm/index.vue';
  import MaterialInfoForm from '@/views/study/registration/components/materialInfoForm/index.vue';
  import AuditSubmitForm from './components/auditSubmitForm/index.vue';
  import PersonalElectronicDocuments from '@/views/study/registration/components/personalElectronicDocuments/index.vue';
  import FeeForm from '@/views/study/registration/components/feeConfigForm/index.vue';
  import { useI18n } from '@/hooks/web/useI18n';
  import { getDetailInfo, getNextForAuditAsync, getSemesterTypeAsync } from '.';

  import NewAuditSubmitForm from './components/newAuditSubmitForm/index.vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { error } from '../../../utils/log';
  const { t } = useI18n();
  const emit = defineEmits(['reload']);
  const [registerModal, { closeModal, changeLoading }] = useModalInner(init);
  const registrationParams = ref<any>({});
  const { createConfirm } = useMessage();

  const currentStep = ref(0);
  const title = ref('报名审核');
  const auditSubmitFormRef = ref();
  const sidebarCollapsed = ref(true);

  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value;
  };

  const formState = reactive({
    record: {
      id: '',
      state: '待审核',
    },
    basic: {},
    material: {},
    result: {},
    key: +new Date(),
  });
  const { key } = toRefs(formState);

  // 审核通过
  const isAuditPass = computed(() => {
    return auditState.value === '完成' || auditState.value === '审核通过' || auditState.value === '待支付';
  });
  // 审核驳回
  const isAuditReject = computed(() => {
    return auditState.value === '审核驳回' || auditState.value === '审核不通过' || auditState.value === '退回修改';
  });

  const getStepList = computed(() => {
    let base = ['身份证信息', '个人申请表', '材料信息'];
    if (semesterCatogry.value === '0001') {
      base = ['身份证信息', '个人申请表', '年限说明', '材料信息'];
    }
    if (semesterCatogry.value === '0002') {
      base = ['身份证信息', '个人申请表', '材料信息'];
    }
    if (auditState.value === '完成' || auditState.value === '审核通过' || auditState.value === '待支付') {
      base = [...base, '资料留存', '缴费信息'];
    }
    return [...base];
  });
  const auditState = ref('填写资料'); // 审核状态
  watch(
    () => currentStep.value,
    (newVal, oldVal) => {
      console.log(newVal, oldVal, getStepList.value[newVal]);
      if ('材料信息' === getStepList.value[newVal] && auditState.value !== '审核通过') {
        sidebarCollapsed.value = false;
      }
      handleStepChange(newVal);
    },
  );
  const canAudit = computed(() => {
    if ('材料信息' === getStepList.value[currentStep.value]) {
      return true;
    }
    return false;
  });
  const currentFormRef = ref();
  // 组件配置映射
  const componentMap = {
    身份证信息: {
      component: IdCardInfoForm,
      span: 14,
      offset: 5,
    },
    个人申请表: {
      component: ApplicationForm,
      span: 24,
      offset: -1,
    },
    年限说明: {
      component: WorkExperienceForm,
      span: 24,
      offset: 0,
    },
    基本信息: {
      component: BasicInfoForm,
      span: 14,
      offset: 5,
    },
    材料信息: {
      component: MaterialInfoForm,
      span: 16,
      offset: 4,
    },
    审核结果: {
      component: AuditSubmitForm,
      span: 14,
      offset: 5,
    },
    资料留存: {
      component: PersonalElectronicDocuments,
      span: 14,
      offset: 5,
    },
    缴费信息: {
      component: FeeForm,
      span: 22,
      offset: 1,
    },
  };

  // 当前渲染的组件配置
  const currentComponent = computed(() => {
    const currentStepName = getStepList.value[currentStep.value];
    return componentMap[currentStepName];
  });
  // 根据activeStep的值进行初始化
  function handleStepChange(activeStep) {
    nextTick(() => {
      if (currentFormRef.value?.init) {
        currentFormRef.value.init(formState.record);
      }
      prevLoading.value = false;
      nextLoading.value = false;
    });
  }

  const registrationId = ref('');
  const semesterCatogry = ref('');

  function init(data) {
    registrationParams.value = data.registrationParams;
    formState.record = data.record;
    registrationId.value = data.record.id;
    auditState.value = formState.record.state;
    changeLoading(false);
    nextTick(() => {
      if (data.step) {
        currentStep.value = getStepList.value.findIndex(item => item === data.step);
      }
      if (currentFormRef.value?.init) {
        currentFormRef.value.init(formState.record);
      }
      auditSubmitFormRef.value?.init({});
    });
  }

  function initData() {
    changeLoading(true);
    getDetailInfo(formState.record.id)
      .then(res => {
        formState.record = res.data;
        auditState.value = formState.record.state;
        changeLoading(false);
      })
      .catch(() => {
        changeLoading(false);
      });
  }

  const nextLoading = ref(false);
  const handleNext = () => {
    nextLoading.value = true;
    currentStep.value++;
  };

  const prevLoading = ref(false);
  const handlePrev = () => {
    prevLoading.value = true;
    currentStep.value--;
  };
  const handleReload = () => {
    handleStepChange(currentStep.value);
  };
  function handleAuditNext() {
    createConfirm({
      iconType: 'warning',
      title: '审核通过',
      content: '确定审核下一个报名数据吗？',
      onOk: () => {
        changeLoading(true);
        try {
          getNextForAuditAsync({ ...registrationParams.value, ...{ id: formState.record.id } })
            .then(res => {
              if (res.data === null || res.data === undefined) {
                changeLoading(false);
                createConfirm({
                  iconType: 'warning',
                  title: '审核失败',
                  content: '没有下一个报名数据了',
                  onOk: () => changeLoading(false),
                });
                return;
              }
              registrationId.value = res.data.id;
              formState.record = res.data;
              auditState.value = formState.record.state;
              nextTick(() => {
                if (res.data.step) {
                  currentStep.value = getStepList.value.findIndex(item => item === res.data.step);
                } else {
                  currentStep.value = 0;
                }
                if (currentFormRef.value?.init) {
                  currentFormRef.value.init(formState.record);
                }
                auditSubmitFormRef.value?.init({});
              });
              changeLoading(false);
            })
            .catch(error => {
              createConfirm({
                iconType: 'warning',
                title: error.message || '审核失败',
                content: '没有下一个报名数据了',
                onOk: () => changeLoading(false),
              });
              changeLoading(false);
            });
        } catch (error) {}
      },
    });
  }

  const handleFinish = () => {
    initData();
    // TODO: 处理审核完成逻辑
    emit('reload');
  };

  function handleClose() {
    closeModal();
    currentStep.value = 0;
    emit('reload');
  }
</script>

<style lang="less" scoped>
  .audit-form {
    padding: 24px;
    display: flex;
    flex-direction: column;
    min-height: 500px;

    .steps-wrapper {
      margin-bottom: 40px;
      overflow: hidden;
    }

    .step-content {
      flex: 1;
      min-height: 400px;
      overflow: auto;
    }
  }

  .form-layout {
    display: flex;
    width: 100%;
    height: calc(100vh - 220px);
  }

  .component-scroll-container {
    flex: 1;
    height: 100%;
    overflow-y: auto;
    padding: 16px 24px;
    transition: all 0.3s ease;
    background-color: #fff;
    border-radius: 8px 0 0 8px;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.02);
    scrollbar-width: thin;
    scrollbar-color: #d0d0d0 #f5f5f5;

    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f5f5f5;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #d0d0d0;
      border-radius: 4px;
      border: 2px solid #f5f5f5;
    }
  }
  .toggle-btn {
    writing-mode: vertical-lr;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(to top, #1890ff, #40a9ff);
    border-radius: 6px 0 0 6px;
    cursor: pointer;
    user-select: none;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    color: white;
    margin-right: 5px;
    transition: all 0.3s ease;
    box-shadow: -3px 0 10px rgba(24, 144, 255, 0.3);
    position: relative;
    overflow: hidden;
    z-index: 100;
    padding: 0 5px;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 0;
      background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), transparent);
      transition: height 0.3s ease;
    }

    &:hover {
      transform: translateX(2px);
      box-shadow: -4px 0 12px rgba(24, 144, 255, 0.5);

      &::before {
        height: 100%;
      }
    }
  }

  .sidebar-container {
    position: relative;
    width: 40%;
    height: 100%;
    overflow-y: auto;
    transition: all 0.3s ease;
    background-color: #fafafa;

    &.collapsed {
      width: 24px;
    }

    .sidebar-toggle {
      position: absolute;
      top: 50%;
      left: 0;
      width: 24px;
      height: 48px;
      background-color: var(--primary-color);
      color: white;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      border-radius: 0 4px 4px 0;
      transform: translateY(-50%);
      z-index: 1;

      .toggle-icon {
        font-weight: bold;
      }
    }
  }

  .data-interface-popup {
    .steps-wrapper {
      overflow: auto;
      padding: 0 20px;
      margin: 10px 0;
      border-radius: 18px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;

      .ant-steps-item {
        flex: 1;
        position: relative;
        padding: 8px 14px;
        transition: all 0.3s ease-in-out;

        &:hover {
          transform: translateY(-2px);
        }

        &-title {
          font-weight: 500;
          background: linear-gradient(90deg, var(--primary-color) 0%, #8c67f6 100%);
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
          background-size: 200% auto;
          animation: gradient 3s ease infinite;
        }

        &-active {
          .ant-steps-item-title {
            font-weight: 600;
            color: var(--primary-color);
          }

          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 30%;
            height: 3px;
            background: var(--primary-color);
            border-radius: 2px;
            transition: width 0.3s ease;
          }

          &:hover::after {
            width: 60%;
          }
        }
      }

      @keyframes gradient {
        0% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0% 50%;
        }
      }

      @media (max-width: 768px) {
        padding: 0 10px;

        .ant-steps-item {
          padding: 6px 2px;

          &-title {
            font-size: 12px;
          }
        }
      }
    }

    .page-explain {
      cursor: pointer;
      float: right;
      color: @text-color-label;

      &:hover {
        color: @primary-color;
      }
    }

    .config {
      flex: 1;
      padding: 10px;
      display: flex;
      justify-content: space-between;
      overflow: hidden;

      .left-pane {
        flex-shrink: 0;
        width: 350px;
        margin-right: 10px;
        .box {
          margin-top: 8px;
          border-radius: 4px;
          height: calc(100% - 40px);
          border: 1px solid @border-color-base;
          overflow: hidden;

          .search-box {
            padding: 10px;
          }
          & > .scroll-container {
            height: calc(100% - 52px) !important;
          }
          .tree-box {
            overflow: hidden;
            overflow-x: hidden;
          }
        }
      }

      .middle-pane {
        border: 1px solid @border-color-base;
        border-radius: 4px;
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .title {
          border-top: 1px solid @border-color-base;
        }

        .title-box {
          height: 36px;
          line-height: 36px;
          display: flex;
          justify-content: space-between;
          color: @text-color-label;
          font-size: 14px;
          padding: 0 10px;
          flex-shrink: 0;
          border-bottom: 1px solid @border-color-base;
        }

        .tabs-box {
          overflow: unset;

          :deep(.ant-tabs-tab:first-child) {
            margin-left: 20px;
          }
        }

        .table-actions {
          flex-shrink: 0;
          border-top: 1px dashed @border-color-base;
          text-align: center;
        }

        .top-box {
          display: flex;

          .main-box {
            flex: 1;
            margin-right: 18px;
          }
        }
      }

      .right-pane {
        width: 350px;
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        height: calc(100% + 9px);
        overflow: hidden;
        margin-left: 10px;

        .right-pane-btn {
          flex-shrink: 0;
        }
      }

      .static-right-pane {
        flex: unset;
        width: 350px;
        margin-left: 10px;
        margin-bottom: unset;
      }
    }

    .jsStaticData {
      flex: 1;
      display: flex;
      overflow: hidden;
      flex-direction: column;
      padding: 10px;
      height: 100%;

      .json-box {
        flex: 1;
      }

      .jsTips {
        flex-shrink: 0;
        padding: 8px 16px;
        background-color: @primary-1;
        border-radius: 4px;
        border-left: 5px solid @primary-color;
        margin-top: 10px;

        p {
          line-height: 24px;
          color: @text-color-help-dark;
        }
      }
    }

    .icon-ym-btn-edit {
      color: @primary-color;
      cursor: pointer;
      font-size: 16px;
    }

    .icon-ym-delete {
      color: @error-color;
      cursor: pointer;
      font-size: 16px;
    }

    .ant-select {
      width: 100% !important;
    }
  }

  /* 自定义页脚按钮样式 */
  .footer-buttons {
    button {
      min-width: 90px;
      transition: all 0.3s ease;
      border-radius: 4px;

      &:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      &:active:not(:disabled) {
        transform: translateY(0);
      }

      &:disabled {
        background: #f5f5f5 !important;
        color: rgba(0, 0, 0, 0.25) !important;
        border: 1px solid #d9d9d9 !important;
        cursor: not-allowed;
        box-shadow: none;
        transform: none;
      }
    }

    .nav-btn {
      font-weight: 500;
    }

    .next-btn {
      color: #ff4d4f;
      border-color: #ff4d4f;
    }

    .prev-btn {
      color: #ff4d4f;
      border-color: #ff4d4f;
    }

    .reload-btn {
      color: #1890ff;
      border-color: #1890ff;

      &:hover:not(:disabled) {
        color: #40a9ff;
        border-color: #40a9ff;
        background-color: rgba(24, 144, 255, 0.05);
      }
    }

    .close-btn {
      border-color: #ff4d4f;
      color: #ff4d4f;

      &:hover:not(:disabled) {
        color: #ff7875;
        border-color: #ff7875;
        background-color: rgba(255, 77, 79, 0.05);
      }
    }
  }

  /* 自定义模态框样式 */
  :deep(.audit-modal) {
    .ant-modal-content {
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .ant-modal-header {
      background-color: #fafafa;
      border-bottom: 1px solid #f0f0f0;
      padding: 16px 24px;
    }

    .ant-modal-footer {
      border-top: 1px solid #f0f0f0;
      padding: 12px 24px;
      display: flex;
      justify-content: flex-end;

      .ant-space {
        width: 100%;
        justify-content: flex-end;
      }
    }
  }
</style>
