<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" destroyOnClose :title="'考试配置'">
    <template #centerToolbar>
      <a-space :size="10"> </a-space>
    </template>
    <div class="xunda-content-wrapper">
      <div class="xunda-content-wrapper-center"> </div>
    </div>
  </BasicPopup>
</template>

<script lang="ts" setup>
  import { BasicPopup, usePopup } from '@/components/Popup';
  import { ref, onMounted, nextTick } from 'vue';

  const [registerPopup, { openPopup }] = usePopup();
  const emit = defineEmits(['reload']);
  defineExpose({ init });

  const semesterId = ref('');

  function init(data) {
    semesterId.value = data.id;
    openPopup();
    nextTick(() => {});
  }

  // 设置查询表单
  function setSearchSchema() {}

  onMounted(() => {
    setSearchSchema();
  });
</script>
