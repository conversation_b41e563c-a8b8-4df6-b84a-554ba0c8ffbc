<template>
  <div class="file-viewer-container">
    <div class="file-container" ref="fileContainer">
      <div v-if="loading" class="loading-overlay">
        <a-spin :tip="loadingTip" />
      </div>

      <!-- 文件信息展示 -->
      <div class="file-info">
        <span class="file-name" v-if="showTitle">
          <component :is="fileIcon" class="file-icon" />
          <span class="file-name-text" :title="getFileName">{{ getFileName }}</span>
          <span class="file-size">{{ formatFileSize(file.fileSize) }}</span>
          <span v-if="fileType" class="file-type-tag">{{ getFileTypeDisplayName(fileType) }}</span>
        </span>

        <!-- 图标操作模式 -->
        <div class="action-icons" v-if="operationMode === 'icon'">
          <template v-if="!props.previewMode">
            <a-tooltip title="预览文件">
              <EyeOutlined class="action-icon preview" @click="handlePreviewAction" />
            </a-tooltip>
          </template>
          <a-tooltip title="下载文件">
            <DownloadOutlined class="action-icon download" @click="handleDownload" />
          </a-tooltip>
          <a-tooltip title="删除文件" v-if="props.allowDelete">
            <DeleteOutlined class="action-icon danger" @click="handleDelete" />
          </a-tooltip>
        </div>

        <!-- 按钮操作模式 -->
        <div class="action-buttons" v-if="operationMode === 'button'">
          <template v-if="!props.previewMode">
            <a-button type="primary" @click="handlePreviewAction">
              <template #icon><EyeOutlined /></template>
              预览
            </a-button>
          </template>

          <a-button type="primary" class="download-btn" @click="handleDownload">
            <template #icon><DownloadOutlined /></template>
            下载
          </a-button>
          <a-button danger @click="handleDelete" v-if="props.allowDelete">
            <template #icon><DeleteOutlined /></template>
            删除
          </a-button>
        </div>
      </div>

      <!-- 内联预览区域 -->
      <div v-if="props.previewMode" class="inline-preview">
        <iframe :key="previewUrl" :src="iframeSrc" class="pdf-iframe" @load="handleIframeLoad"></iframe>
      </div>

      <!-- 弹出式预览区域 -->
      <div v-show="previewVisible" class="preview-content">
        <div class="pdf-fullscreen-preview">
          <div class="preview-header">
            <span class="file-name">{{ getFileName }}</span>
            <div>
              <a-button type="text" @click="closePdfPreview">
                <template #icon><CloseOutlined /></template>
              </a-button>
            </div>
          </div>
          <iframe :key="previewUrl" :src="iframeSrc" class="pdf-iframe" ref="fileIframe" @load="handleIframeLoad"></iframe>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, onMounted, watch } from 'vue';
  import {
    DownloadOutlined,
    EyeOutlined,
    CloseOutlined,
    FilePdfOutlined,
    FileImageOutlined,
    FileTextOutlined,
    FileWordOutlined,
    FileExcelOutlined,
    FilePptOutlined,
    PlaySquareOutlined,
    CustomerServiceOutlined,
    DeleteOutlined,
    FileExclamationOutlined,
  } from '@ant-design/icons-vue';
  import { message as antMessage, Modal } from 'ant-design-vue';
  import { useLoading } from '@/components/Loading';
  import { useGlobSetting } from '@/hooks/setting';
  import { FileType, getFileTypeFromUrl, getFileTypeDisplayName, getFileTypeFromExtension } from './utils/filetype';
  import { getDownloadUrl, previewFile } from '@/api/basic/common';
  import { downloadByUrl } from '@/utils/file/download';
  import { fileItem } from '@/components/Xunda/Upload/src/props';
  import { getToken } from '@/utils/auth';

  // 自定义消息提示
  const message = {
    success: (content, duration = 3) => antMessage.success({ content, duration }),
    error: (content, duration = 4) => antMessage.error({ content, duration }),
    warning: (content, duration = 3) => antMessage.warning({ content, duration }),
  };

  const props = defineProps({
    type: { type: String, default: 'annex' },
    file: {
      type: Object as PropType<fileItem>,
      default: () => ({}),
    },
    showTitle: {
      type: Boolean,
      default: true,
    },
    allowDelete: {
      type: Boolean,
      default: false,
    },
    previewMode: {
      type: Boolean,
      default: false,
    },
    operationMode: {
      type: String,
      default: 'button',
    },
    height: {
      type: String,
      default: '100%',
    },
  });

  // 状态变量
  const previewVisible = ref(false);
  const pdfPreviewVisible = ref(false);
  const fileType = ref<FileType>(FileType.UNKNOWN);
  const loading = ref(false);
  const fileContainer = ref<HTMLElement | null>(null);
  const fileIframe = ref<HTMLIFrameElement | null>(null);
  const previewUrl = ref('');

  // 全局设置和加载
  const globSetting = useGlobSetting();
  const apiUrl = computed(() => globSetting.apiUrl);
  const [openFullLoading, closeFullLoading] = useLoading({
    tip: 'Loading...',
  });
  const loadingTip = ref('文件加载中...');

  // 计算属性
  const getFileName = computed(() => {
    if (!props.file || !props.file.name) return '';
    return props.file.name.split('/').pop() || '未知文件';
  });

  // 文件类型判断
  const isPDF = computed(() => fileType.value === FileType.PDF);
  const isImage = computed(() => fileType.value === FileType.IMAGE);
  const isText = computed(() => fileType.value === FileType.TEXT);
  const isVideo = computed(() => fileType.value === FileType.VIDEO);
  const isAudio = computed(() => fileType.value === FileType.AUDIO);
  const isOffice = computed(() => fileType.value === FileType.OFFICE);
  const canPreview = computed(() => isPDF.value || isImage.value || isText.value || isVideo.value || isAudio.value || isOffice.value);

  // 文件图标
  const fileIcon = computed(() => {
    if (isPDF.value) return FilePdfOutlined;
    if (isImage.value) return FileImageOutlined;
    if (isText.value) return FileTextOutlined;
    if (isVideo.value) return PlaySquareOutlined;
    if (isAudio.value) return CustomerServiceOutlined;
    if (isOffice.value) {
      const ext = props.file.name?.toLowerCase().split('.').pop();
      switch (ext) {
        case 'doc':
        case 'docx':
          return FileWordOutlined;
        case 'xls':
        case 'xlsx':
          return FileExcelOutlined;
        case 'ppt':
        case 'pptx':
          return FilePptOutlined;
        default:
          return FileTextOutlined;
      }
    }
    return FileExclamationOutlined;
  });

  // iframe 源地址
  const iframeSrc = computed(() => previewUrl.value);

  // 检测文件类型
  const detectFileType = async () => {
    if (!props.file || !props.file.name) {
      fileType.value = FileType.UNKNOWN;
      return;
    }
    loading.value = true;

    try {
      // 先尝试从文件名判断类型
      const fileTypeFromName = getFileTypeFromExtension(props.file.name);
      if (fileTypeFromName !== FileType.UNKNOWN) {
        fileType.value = fileTypeFromName;
        return;
      }

      // 如果文件名判断失败，尝试从URL获取
      if (props.file.url) {
        fileType.value = await getFileTypeFromUrl(props.file.url);
      } else {
        fileType.value = FileType.UNKNOWN;
      }
    } finally {
      loading.value = false;
    }
  };

  // 显示预览
  const showPdfPreview = () => {
    if (!canPreview.value) {
      message.warning('该文件类型暂不支持预览，您可以下载后查看');
      return;
    }
    pdfPreviewVisible.value = true;
    getFilePrivewUrl(props.file);
  };

  // 关闭预览
  const closePdfPreview = () => {
    pdfPreviewVisible.value = false;
    previewVisible.value = false;
    previewUrl.value = '';
  };

  // 处理文件加载完成
  const handleIframeLoad = () => {
    loading.value = false;
  };

  // 下载功能
  const handleDownload = () => {
    if (!props.file || !props.file.fileId || !props.file.url) return;

    openFullLoading();

    let fileId = props.file.fileId;
    if (!fileId.includes(',')) {
      fileId = props.file.url?.substring(props.file.url?.lastIndexOf('/') + 1);
    }
    const path = props.file.url?.substring(0, props.file.url?.lastIndexOf('/'));
    const fileType = props.type ?? path?.substring(path.lastIndexOf('/') + 1);

    getDownloadUrl(fileType, fileId)
      .then(res => {
        downloadByUrl({ url: res.data.url, fileName: props.file.name });
        closeFullLoading();
      })
      .catch(() => {
        message.error('下载失败，请稍后重试');
        closeFullLoading();
      });
  };

  // 定义emit
  const emit = defineEmits(['delete']);

  // 删除功能
  const handleDelete = () => {
    if (!props.file || !props.file.fileId) return;

    Modal.confirm({
      title: '确认删除',
      content: `确定要删除文件 "${getFileName.value}" 吗？`,
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => {
        emit('delete', props.file);
      },
    });
  };

  // 预览操作
  const handlePreviewAction = () => {
    if (!canPreview.value) {
      message.warning('该文件类型暂不支持预览，请尝试下载后查看');
      return;
    }

    previewVisible.value = true;
    showPdfPreview();
  };

  // 格式化文件大小
  function formatFileSize(size) {
    if (!size) return '0 B';

    const units = ['B', 'KB', 'MB', 'GB'];
    let i = 0;
    while (size >= 1024 && i < units.length - 1) {
      size /= 1024;
      i++;
    }
    return `${size.toFixed(2)} ${units[i]}`;
  }

  function getFilePrivewUrl(file) {
    const previewModel = {
      fileName: file.url?.substring(file.url.lastIndexOf('/') + 1),
      fileDownloadUrl: file.url,
      fileVersionId: file.fileId,
    };

    previewFile(previewModel)
      .then(res => {
        if (res.data) {
          previewUrl.value = res.data + '&token=' + getToken();
        } else {
          message.warning('文件不存在');
        }
      })
      .catch(() => {})
      .finally(() => {
        loading.value = false;
      });
  }

  // 监听URL变化
  watch(
    () => props.file,
    async newFile => {
      if (newFile && newFile.url) {
        loading.value = true;
        // 检测文件类型
        await detectFileType();
        if (props.previewMode) getFilePrivewUrl(newFile);
        setTimeout(() => {
          loading.value = false;
        }, 500);
      }
    },
    { immediate: true },
  );

  // 组件挂载时初始化
  onMounted(() => {
    // 初始化操作（如果需要）
  });
</script>

<style lang="less" scoped>
  .file-viewer-container {
    position: relative;
    background: #fff;

    .file-container {
      position: relative;

      .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.8);
        z-index: 10;
      }

      .pdf-iframe {
        width: 100%;
        height: 100%;
        border: none;
        min-height: 400px;
      }
    }
  }

  // 文件信息样式
  .file-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 0;
    margin-bottom: 12px;

    .file-name {
      font-size: 14px;
      color: #333;
      margin-right: 16px;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: flex;
      align-items: center;
      padding: 8px 12px;
      background: #f5f7fa;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;

      &:hover {
        background: #e6f7ff;
      }

      .file-icon {
        margin-right: 10px;
        font-size: 18px;
        color: #1890ff;
      }

      .file-name-text {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-weight: 500;
      }

      .file-size {
        margin: 0 10px;
        color: #666;
        font-size: 12px;
        background: #f0f0f0;
        padding: 2px 6px;
        border-radius: 4px;
      }

      .file-type-tag {
        font-size: 12px;
        padding: 2px 8px;
        border-radius: 4px;
        background: #1890ff;
        color: white;
        margin-left: 6px;
      }
    }

    .action-buttons {
      display: flex;
      gap: 8px;
      flex-shrink: 0;

      .ant-btn {
        transition: all 0.3s;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
      }

      .download-btn {
        background-color: #52c41a;
        border-color: #52c41a;

        &:hover {
          background-color: #73d13d;
          border-color: #73d13d;
        }
      }
    }

    .action-icons {
      display: flex;
      gap: 16px;
      flex-shrink: 0;
      padding: 4px 8px;
      background: #f9f9f9;
      border-radius: 4px;

      .action-icon {
        font-size: 18px;
        cursor: pointer;
        transition: all 0.3s;
        color: #666;
        padding: 4px;
        border-radius: 50%;

        &.preview {
          color: #1890ff;

          &:hover {
            background-color: rgba(24, 144, 255, 0.1);
          }
        }

        &.download {
          color: #52c41a;

          &:hover {
            background-color: rgba(82, 196, 26, 0.1);
            color: #73d13d;
          }
        }

        &.danger {
          color: #ff4d4f;

          &:hover {
            color: #ff7875;
            background-color: rgba(255, 77, 79, 0.1);
          }
        }

        &:hover {
          transform: scale(1.2);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.09);
        }
      }
    }
  }

  // 预览样式
  .pdf-fullscreen-preview {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: #fff;
    z-index: 9999;
    display: flex;
    flex-direction: column;

    .preview-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      background: #f5f5f5;
      border-bottom: 1px solid #e8e8e8;

      .file-name {
        font-size: 16px;
        color: #333;
        margin-right: 16px;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .pdf-iframe {
      flex: 1;
      width: 100%;
      height: calc(100vh - 52px);
      border: none;
    }
  }

  // 内联预览样式
  .inline-preview {
    margin-top: 16px;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    overflow: hidden;
    height: 400px;

    .pdf-iframe {
      width: 100%;
      height: 100%;
      min-height: 400px;
      border: none;
    }
  }
</style>
