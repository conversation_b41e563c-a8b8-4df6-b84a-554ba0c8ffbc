import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
import { getForSelect as getSemesterForSelectAsync } from '@/views/plan/semester/index';
import { xundaUtils } from '@/utils/xunda';
import { ref } from 'vue';

const { t } = useI18n();

// 基础Api
export const planFeeConfigApi = '/api/plan/feeConfig';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: planFeeConfigApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: planFeeConfigApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: planFeeConfigApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: planFeeConfigApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: planFeeConfigApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: planFeeConfigApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: planFeeConfigApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: planFeeConfigApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: planFeeConfigApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: planFeeConfigApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: planFeeConfigApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: planFeeConfigApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: planFeeConfigApi + `/getForSelect`, data });
}

const feeOptionsObj = ref([]);
const planOptionsObj = ref([]);

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'semesterName',
    label: t('计划名称'),
    component: 'Select',
    componentProps: {
      submitOnPressEnter: true,
      showSearch: true,
      options: planOptionsObj.value,
      fieldNames: {
        label: 'name',
        value: 'id',
      },
    },
  },
  {
    field: 'feeType',
    label: t('费用类型'),
    component: 'Select',
    componentProps: {
      submitOnPressEnter: true,
      options: feeOptionsObj.value,
      fieldNames: {
        label: 'fullName',
        value: 'enCode',
      },
    },
  },
];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  // {
  //   title: t('计划名称'),
  //   dataIndex: 'semesterName',
  // },
  {
    title: t('费用类型'),
    dataIndex: 'feeType',
    width: 220,
  },
  {
    title: t('原价'),
    dataIndex: 'price',
    width: 120,
  },
  {
    title: t('说明'),
    dataIndex: 'description',
    // width: 200,
  },
];
export function getSemesterForSelect(params, callBack) {
  getSemesterForSelectAsync(params).then(res => {
    callBack && callBack(res);
  });
}

export function getCurrencyType(callBack) {
  getDictionaryDataSelector('currencyType').then(res => {
    callBack && callBack(res);
  });
}

export function getFeeType(callBack) {
  getDictionaryDataSelector('feeType').then(res => {
    callBack && callBack(res);
  });
}
