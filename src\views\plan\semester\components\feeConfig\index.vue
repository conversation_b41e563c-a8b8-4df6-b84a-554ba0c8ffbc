<template>
  <div class="xunda-content-wrapper">
    <div class="xunda-content-wrapper-center">
      <div class="xunda-content-wrapper-content bg-white">
        <BasicTable @register="registerTable" ref="tableRef">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="HandleAdd()" v-if="!disabled"> {{ t('common.add2Text', '新增') }}</a-button>
            <a-button type="link" preIcon="icon-ym icon-ym-btn-clearn" @click="handelBatchRemove()" v-if="!disabled">
              {{ t('common.batchDelText', '批量删除') }}</a-button
            >
          </template>
          <template #toolbar>
            <p>计划名称：{{ semesterName }}</p>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'feeType'">
              <p>{{ xundaUtils.optionText(record.feeType, optionsObj.feeTypeOptions, optionsObj.defaultProps) }}</p>
            </template>
            <template v-if="column.dataIndex === 'currency'">
              <p>{{ xundaUtils.optionText(record.currency, optionsObj.currencyOptions, optionsObj.defaultProps) }}</p>
            </template>
            <template v-if="column.dataIndex === 'price'">
              <xunda-input-number v-model:value="record['price']" :precision="2" thousands detailed />
            </template>
            <template v-if="column.key === 'action'">
              <TableAction
                :actions="[
                  {
                    label: t('common.editText', '编辑'),
                    onClick: handleEdit.bind(null, record),
                  },
                  {
                    label: t('common.delText', '删除'),
                    color: 'error',
                    modelConfirm: {
                      onOk: handleDelete.bind(null, record.id),
                    },
                  },
                ]" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form ref="formRef" @reload="reload" />
  </div>
</template>

<script lang="ts" setup>
  import { getList, batchDelete, columns, getCurrencyType, getFeeType } from './index';
  import { ref, reactive } from 'vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useForm } from '@/components/Form';
  import { BasicTable, useTable, TableAction, TableActionType } from '@/components/Table';
  import Form from './form.vue';
  import { useRoute } from 'vue-router';
  import { cloneDeep } from 'lodash-es';
  import { useUserStore } from '@/store/modules/user';
  import { usePermission } from '@/hooks/web/usePermission';
  import { xundaUtils } from '@/utils/xunda';

  defineOptions({ name: 'FeeConfig' });
  const emit = defineEmits([]);
  const props = defineProps({
    semesterId: {
      type: String,
      required: true,
    },
    semesterName: {
      type: String,
    },
    disabled: { type: Boolean, default: false },
  });

  const optionsObj = ref<any>({
    //选项配置
    defaultProps: { label: 'fullName', value: 'enCode' }, // 默认下拉选择键值
  });

  const userStore = useUserStore();
  const { hasFormP } = usePermission();
  const route = useRoute();
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const formRef = ref<any>(null);
  const tableRef = ref<Nullable<TableActionType>>(null);
  const cacheList = ref<any>([]);
  const defaultSearchInfo = {
    menuId: route.meta.modelId as string,
    moduleId: '***********', //模块ID
    superQueryJson: '',
    dataType: 0,
  };

  const searchInfo = reactive({
    ...cloneDeep(defaultSearchInfo),
  });

  const [registerSearchForm] = useForm({
    baseColProps: { span: 6 },
    showActionButtonGroup: true,
    showAdvancedButton: true,
    compact: true,
  });
  const [registerTable, { reload, setLoading, getSelectRowKeys, clearSelectedRowKeys }] = useTable({
    api: getList,
    columns: columns,
    immediate: false,
    canResize: true,
    searchInfo: searchInfo,
    beforeFetch: params => {
      params.semesterId = props.semesterId;
      return params;
    },
    clickToRowSelect: false,
    afterFetch: data => {
      const list = data.map(o => ({
        ...o,
      }));
      cacheList.value = cloneDeep(list);
      return list;
    },
    actionColumn: {
      width: 200,
      title: t('common.actionText', '操作'),
      dataIndex: 'action',
      fixed: 'right',
      ifShow: !props.disabled,
    },
    rowSelection: props.disabled
      ? undefined
      : {
          type: 'checkbox',
          getCheckboxProps: record => ({
            disabled: record.top,
          }),
        },
  });

  // 编辑
  function handleEdit(record) {
    // 不带工作流
    const data = {
      id: record.id,
      menuId: searchInfo.menuId,
      allList: cacheList.value,
    };
    formRef.value?.init(data);
  }

  // 删除
  function handleDelete(id) {
    const query = { ids: [id] };
    batchDelete(query).then(res => {
      createMessage.success(res.msg);
      clearSelectedRowKeys();
      reload();
    });
  }

  // 新增
  function HandleAdd() {
    // 不带流程新增
    const data = {
      id: '',
      menuId: searchInfo.menuId,
      allList: cacheList.value,
      semesterId: props.semesterId,
    };
    formRef.value?.init(data);
  }

  // 批量删除
  function handelBatchRemove() {
    const ids = getSelectRowKeys();
    if (!ids.length) return createMessage.error('请选择一条数据');
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要删除这些数据吗, 是否继续?',
      onOk: () => {
        const query = { ids: ids };
        setLoading(true);
        batchDelete(query).then(res => {
          createMessage.success(res.msg);
          clearSelectedRowKeys();
          reload();
        });
      },
    });
  }

  function initData(data) {
    getFeeType(res => {
      optionsObj.value.feeTypeOptions = res.data.list;
    });
    getCurrencyType(res => {
      optionsObj.value.currencyOptions = res.data.list;
    });
    reload();
  }

  defineExpose({
    initData,
  });
</script>
