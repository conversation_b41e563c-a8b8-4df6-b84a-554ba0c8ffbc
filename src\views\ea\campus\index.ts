import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';

// 基础Api
export const campusApi = '/api/ea/campus';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: campusApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: campusApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: campusApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: campusApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: campusApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: campusApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: campusApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: campusApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: campusApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: campusApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: campusApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: campusApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: campusApi + `/getForSelect`, data });
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'name',
    label: t('校区名'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
  {
    field: 'no',
    label: t('编号'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('学校'),
    dataIndex: 'schoolName',
    width: 120,
  },
  {
    title: t('校区名'),
    dataIndex: 'name',
    width: 120,
  },
  {
    title: t('编号'),
    dataIndex: 'no',
    width: 120,
  },
  {
    title: t('邮箱'),
    dataIndex: 'mail',
    width: 120,
  },
  {
    title: t('地址'),
    dataIndex: 'address',
    width: 120,
  },
  {
    title: t('联系人'),
    dataIndex: 'contact',
    width: 120,
  },
  {
    title: t('联系电话'),
    dataIndex: 'phone',
    width: 120,
  },
  {
    title: t('线上课程'),
    dataIndex: 'onlineFlag',
    width: 120,
    customRender: xundaUtils.customRenderBoolean,
  },
  {
    title: t('说明'),
    dataIndex: 'description',
    width: 120,
  },
  {
    title: t('拓展配置'),
    dataIndex: 'extraConfig',
    width: 120,
  },
];
