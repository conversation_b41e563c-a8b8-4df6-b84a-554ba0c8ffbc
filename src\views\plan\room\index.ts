import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
import { getForSelect as getHotelForSelect } from '../hotel';

const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';
import { ref } from 'vue';

// 基础Api
export const planRoomApi = '/api/plan/room';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: planRoomApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: planRoomApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: planRoomApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: planRoomApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: planRoomApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: planRoomApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: planRoomApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: planRoomApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: planRoomApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: planRoomApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: planRoomApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: planRoomApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: planRoomApi + `/getForSelect`, data });
}

const hotelNameList = ref([]);
/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'name',
    label: t('名称'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
  {
    field: 'hotelName',
    label: t('酒店'),
    component: 'Select',
    componentProps: {
      submitOnPressEnter: true,
      options: hotelNameList.value,
      fieldNames: {
        label: 'name',
        value: 'id',
      },
    },
  },
];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('酒店'),
    dataIndex: 'hotelName',
    sorter: true,
    width: 120,
  },
  {
    title: t('名称'),
    dataIndex: 'name',
    sorter: true,
    width: 120,
  },
  {
    title: t('币种'),
    dataIndex: 'currency',
    width: 120,
  },
  {
    title: t('参考价格'),
    dataIndex: 'price',
    sorter: true,
    width: 120,
  },
  {
    title: t('房型'),
    dataIndex: 'roomType',
    sorter: true,
    width: 120,
  },
  {
    title: t('描述'),
    dataIndex: 'description',
    width: 120,
  },
  {
    title: t('图片'),
    dataIndex: 'picture',
    width: 120,
  },
];

export async function getHotelOption(updateSchema) {
  getHotelForSelect({
    dataType: 1,
  }).then(res => {
    hotelNameList.value = res.data.list;
    updateSchema({
      field: 'hotelName',
      label: t('计划名称'),
      component: 'Select',
      componentProps: {
        submitOnPressEnter: true,
        showSearch: true,
        options: hotelNameList.value,
        fieldNames: {
          label: 'name',
          value: 'id',
        },
      },
    });
  });
}

export async function getRoomType() {
  const res = await getDictionaryDataSelector('roomType');
  return res.data.list;
}
