import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';
import { stripHtml, parseOptions, parseFillBlankAnswer } from '../utils';

// 基础Api
export const questionApi = '/api/exam/question';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: questionApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: questionApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: questionApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: questionApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: questionApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: questionApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: questionApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: questionApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: questionApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: questionApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: questionApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: questionApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: questionApi + `/getForSelect`, data });
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  // {
  //   title: t('主键'),
  //   dataIndex: 'id',
  //   width: 120,
  // },
  {
    title: t('题序'),
    dataIndex: 'displayOrder',
    width: 60,
  },
  {
    title: t('课程'),
    dataIndex: 'courseName',
    width: 120,
  },
  {
    title: t('题型'),
    dataIndex: 'questionTypeName',
    width: 80,
  },
  {
    title: t('父题目'),
    dataIndex: 'parentId',
    width: 60,
  },
  // {
  //   title: t('解析'),
  //   dataIndex: 'analysis',
  //   width: 120,
  // },
  {
    title: t('题干'),
    dataIndex: 'content',
    width: 200,
    customRender: ({ record }) => {
      const content = stripHtml(record.content || '');
      return content;
    },
  },
  {
    title: t('选项'),
    dataIndex: 'option',
    width: 200,
    customRender: ({ record }) => {
      const options = parseOptions(record.option || '');
      return options;
    },
  },
  {
    title: t('答案'),
    dataIndex: 'answer',
    width: 120,
    customRender: ({ text, record }) => {
      if (record.questionTypeName === '填空题') {
        return parseFillBlankAnswer(text || '');
      }
      return text;
    },
  },
  {
    title: t('难度系数'),
    dataIndex: 'difficulty',
    width: 120,
  },
  {
    title: t('知识点'),
    dataIndex: 'knowledgeTag',
    width: 120,
  },
];
