import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
import { defHttp } from '@/utils/http/axios';
import { ref } from 'vue';
import { xundaUtils } from '@/utils/xunda';
const { t } = useI18n();

// 基础Api
export const semesterApi = '/api/plan/semester';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: semesterApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: semesterApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: semesterApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: semesterApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: semesterApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: semesterApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: semesterApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: semesterApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: semesterApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: semesterApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: semesterApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: semesterApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: semesterApi + `/getForSelect`, data });
}

// 考评人员
export function saveTeacherJson(data) {
  return defHttp.post({ url: semesterApi + `/saveTeacherJson`, data });
}

// 发布计划
export function getTeachers(id) {
  return defHttp.post({ url: semesterApi + `/getTeachers/` + id });
}

export const courseApi = '/api/ea/course';

// 获取列表
export function getAllCourse(data) {
  return defHttp.post({ url: semesterApi + `/getCoursesForSelect`, data });
}

// 获取列表
export function getSelectCourse(data) {
  return defHttp.post({ url: semesterApi + `/getCourses`, data });
}

// 获取列表
export function setSelectCourse(data) {
  return defHttp.post({ url: semesterApi + `/setCourses`, data });
}

const planTypeSelect = ref([]);

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'name',
    label: t('计划名称'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
  {
    field: 'year',
    label: t('学年'),
    component: 'DatePicker',
    componentProps: ({ formModel }) => {
      return {
        submitOnPressEnter: true,
        format: 'yyyy',
        valueFormat: 'yyyy', // 确保值以年份格式存储
      };
    },
    // componentProps: {
    //   submitOnPressEnter: true,
    //   format: 'yyyy',
    //   valueFormat: 'yyyy', // 确保值以年份格式存储
    //   onChange: (date: any, dateString: string) => {
    //     const yearNumber = parseInt(dateString); // 直接获取年份数字
    //     console.log('年份数字:', date, dateString);
    //   },
    // },
  },
  {
    field: 'category',
    label: t('计划类别'),
    component: 'Select',
    componentProps: {
      submitOnPressEnter: true,
      options: planTypeSelect.value,
      fieldNames: {
        label: 'fullName',
        value: 'enCode',
      },
    },
  },
  {
    field: 'startTime',
    label: t('开始日期'),
    component: 'DateRange',
    componentProps: {
      submitOnPressEnter: true,
      format: 'YYYY-MM-DD',
    },
  },
  {
    field: 'endTime',
    label: t('结束日期'),
    component: 'DateRange',
    componentProps: {
      submitOnPressEnter: true,
      format: 'YYYY-MM-DD',
    },
  },
];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('计划名称'),
    dataIndex: 'name',
    width: 120,
  },
  {
    title: t('学年'),
    dataIndex: 'year',
    width: 120,
  },
  {
    title: t('期次'),
    dataIndex: 'term',
    width: 120,
  },
  {
    title: t('计划类别'),
    dataIndex: 'category',
    width: 120,
  },
  {
    title: t('级别'),
    dataIndex: 'planLevel',
    width: 120,
  },
  {
    title: t('描述'),
    dataIndex: 'description',
    width: 120,
  },
  {
    title: t('开始日期'),
    dataIndex: 'startTime',
    width: 120,
    customRender: ({ record }) => {
      return xundaUtils.toDateString(record.startTime);
    },
  },
  {
    title: t('结束日期'),
    dataIndex: 'endTime',
    width: 120,
    customRender: ({ record }) => {
      return xundaUtils.toDateString(record.endTime);
    },
  },
  {
    title: t('最小开班人数'),
    dataIndex: 'minNumber',
    width: 120,
  },
  {
    title: t('最大班级人数'),
    dataIndex: 'maxNumber',
    width: 120,
  },
  {
    title: t('已选人数'),
    dataIndex: 'currentNumber',
    width: 120,
  },
  {
    title: t('剩余空位'),
    dataIndex: 'remainNumber',
    width: 120,
  },
  {
    title: t('周数'),
    dataIndex: 'weeks',
    width: 120,
  },
  {
    title: t('是否免费'),
    dataIndex: 'freeFlag',
    width: 120,
    customRender: xundaUtils.customRenderBoolean,
  },
  {
    title: t('流程'),
    dataIndex: 'flowId',
    width: 120,
  },
  {
    title: t('流程实例'),
    dataIndex: 'flowTaskId',
    width: 120,
  },
  {
    title: t('报名配置'),
    dataIndex: 'planConfigValue',
    width: 120,
  },
  {
    title: t('报名表单配置'),
    dataIndex: 'formConfigValue',
    width: 120,
  },
  {
    title: t('附加信息模板'),
    dataIndex: 'planTemplateId',
    width: 120,
  },
  {
    title: t('需要考试'),
    dataIndex: 'examFlag',
    width: 120,
    customRender: xundaUtils.customRenderBoolean,
  },
  {
    title: t('提供住宿'),
    dataIndex: 'accommodationFlag',
    width: 120,
    customRender: xundaUtils.customRenderBoolean,
  },
  {
    title: t('版本号'),
    dataIndex: 'version',
    width: 120,
  },
  {
    title: t('允许选课'),
    dataIndex: 'allowSelectCourse',
    width: 120,
    customRender: xundaUtils.customRenderBoolean,
  },
  {
    title: t('状态'),
    dataIndex: 'publishFlag',
    width: 120,
  },
];

export async function getPlanType(updateSchema?) {
  const res = await getDictionaryDataSelector('planType');
  planTypeSelect.value = res.data.list;
  updateSchema({
    field: 'category',
    label: t('计划类别'),
    component: 'Select',
    componentProps: {
      submitOnPressEnter: true,
      showSearch: true,
      options: planTypeSelect.value,
      fieldNames: {
        label: 'fullName',
        value: 'enCode',
      },
    },
  });
  return res.data.list;
}
