<template>
  <div class="xunda-content-wrapper">
    <div class="xunda-content-wrapper-center">
      <div class="xunda-content-wrapper-search-box">
        <BasicForm
          @register="registerSearchForm"
          :schemas="searchSchemas"
          @advanced-change="redoHeight"
          @submit="handleSearchSubmit"
          @reset="handleSearchReset"
          class="search-form">
        </BasicForm>
      </div>
      <div class="xunda-content-wrapper-content bg-white">
        <BasicTable @register="registerTable" ref="tableRef">
          <template #toolbar>
            <a-button type="primary" hidden @click="handleGenFromSemester">从计划生成</a-button>
          </template>

          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="HandleAdd()"> {{ t('common.add2Text', '新增') }}</a-button>
            <a-button type="primary" @click="handleSendNotice" v-if="false">通知缴费</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'name'">
              <p>{{ xundaUtils.optionText(record.name, feeTypeOptions) }}</p>
            </template>
            <template v-if="column.dataIndex === 'currency'">
              <p>{{ xundaUtils.optionText(record.currency, currencyOptions) }}</p>
            </template>
            <template v-if="column.dataIndex === 'originalPrice'">
              <p>{{ xundaUtils.numtoAmount(record.originalPrice) }}</p>
            </template>
            <template v-if="column.dataIndex === 'systemDiscount'">
              <p>{{ xundaUtils.numtoAmount(record.systemDiscount) }}</p>
            </template>
            <template v-if="column.dataIndex === 'manualAdjustment'">
              <p>{{ xundaUtils.numtoAmount(record.manualAdjustment) }}</p>
            </template>
            <template v-if="column.dataIndex === 'realPrice'">
              <p>{{ xundaUtils.numtoAmount(record.realPrice) }}</p>
            </template>
            <template v-if="column.dataIndex === 'outstandingAmount'">
              <p>{{ xundaUtils.numtoAmount(record.outstandingAmount) }}</p>
            </template>
            <template v-if="column.dataIndex === 'payState'">
              <a-tag v-if="record.payState === '已支付'" :color="record.payState === '已支付' ? 'success' : ''">{{ record.payState }}</a-tag>
              <a-tag v-if="record.payState === '待缴费'" :color="record.payState === '待缴费' ? 'warning' : ''">{{ record.payState }}</a-tag>
            </template>
            <template v-if="column.key === 'action'">
              <TableAction
                :actions="[
                  {
                    label: t('调整'),
                    onClick: handleAdjust.bind(null, record),
                    ifShow: showAdjustButton,
                  },
                  {
                    label: t('common.editText', '编辑'),
                    onClick: handleEdit.bind(null, record),
                    ifShow: showEditButton,
                  },
                  {
                    label: t('common.delText', '删除'),
                    color: 'error',
                    ifShow: showEditButton,
                    modelConfirm: {
                      onOk: handleDelete.bind(null, record.id),
                    },
                  },
                ]" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form ref="formRef" @reload="reload" />
  </div>
</template>

<script lang="ts" setup>
  import { getList, batchDelete, columns, searchSchemas } from './index';
  import { ref, reactive, computed } from 'vue';
  import { useModal } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { BasicForm, useForm } from '@/components/Form';
  import { BasicTable, useTable, TableAction, TableActionType } from '@/components/Table';
  import { useRoute } from 'vue-router';
  import { cloneDeep } from 'lodash-es';
  import { useUserStore } from '@/store/modules/user';
  import { usePermission } from '@/hooks/web/usePermission';
  import { genFromSemesterAsync, sendNoticeAsync, remindPaymentAsync, sendMessageAsync } from '.';
  import { xundaUtils } from '@/utils/xunda';
  import Form from './form.vue';
  import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
  const userStore = useUserStore();
  const { hasFormP } = usePermission();
  const route = useRoute();
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const formRef = ref<any>(null);
  const tableRef = ref<Nullable<TableActionType>>(null);
  const cacheList = ref<any>([]);

  const props = defineProps({
    auditState: {
      type: String,
      default: '',
    },
  });

  const showAdjustButton = computed(() => {
    return props.auditState !== '1';
  });

  const showEditButton = computed(() => {
    return props.auditState === '1';
  });

  const registrationId = ref<string | number>('');

  const defaultSearchInfo = {
    menuId: route.meta.modelId as string,
    moduleId: '***********', //模块ID
    superQueryJson: '',
    dataType: 0,
  };
  const searchInfo = reactive({
    ...cloneDeep(defaultSearchInfo),
  });
  const [registerSearchForm, { updateSchema }] = useForm({
    baseColProps: { span: 6 },
    showActionButtonGroup: true,
    showAdvancedButton: true,
    compact: true,
  });
  const getTableList = params => {
    if (params.registrationId) {
      return getList(params);
    }
    return [];
  };
  const [registerTable, { reload, setLoading, redoHeight }] = useTable({
    api: getTableList,
    columns: columns,
    searchInfo: searchInfo,
    clickToRowSelect: false,
    summaryFunc: calculateSummary,
    showSummary: true,
    beforeFetch: params => {
      params.registrationId = registrationId.value;
      return params;
    },
    afterFetch: data => {
      const list = data.map(o => ({
        ...o,
      }));
      cacheList.value = cloneDeep(list);
      return list;
    },
    actionColumn: {
      width: 50,
      title: t('common.actionText', '操作'),
      dataIndex: 'action',
      fixed: 'right',
    },
    pagination: false,
    canResize: false,
    // resizeHeightOffset: -500,
    isCanResizeParent: true,
    // 已移除表格选项框
  });

  function handleSearchReset() {
    // 搜索重置
  }

  function handleSearchSubmit(data) {
    let obj = {
      ...defaultSearchInfo,
      superQueryJson: searchInfo.superQueryJson,
      ...data,
    };
    Object.keys(searchInfo).map(key => {
      delete searchInfo[key];
    });
    for (let [key, value] of Object.entries(obj)) {
      searchInfo[key.replaceAll('-', '_')] = value;
    }
    reload({ page: 1 });
  }

  // 编辑
  function handleEdit(record) {
    // 不带工作流
    const data = {
      id: record.id,
      menuId: searchInfo.menuId,
      allList: cacheList.value,
    };
    formRef.value?.init(data);
  }

  // 删除
  function handleDelete(id) {
    const query = { ids: [id] };
    batchDelete(query).then(res => {
      createMessage.success(res.msg);
      reload();
    });
  }

  function handleAdjust(record) {
    const data = {
      id: record.id,
      menuId: searchInfo.menuId,
      allList: cacheList.value,
      isAdjust: true,
    };
    formRef.value?.init(data);
  }

  // 新增
  function HandleAdd() {
    // 不带流程新增
    const data = {
      id: '',
      menuId: searchInfo.menuId,
      allList: cacheList.value,
    };
    formRef.value?.init(data);
  }

  // 批量删除功能已移除

  function handleGenFromSemester() {
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要从学期生成费用吗, 是否继续?',
      onOk: () => {
        genFromSemesterAsync(registrationId.value).then(() => {
          reload();
        });
      },
    });
  }

  function handleSendNotice() {
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要通知该学员缴纳费用吗, 是否继续?',
      onOk: () => {
        sendNoticeAsync(registrationId.value).then(res => {
          createMessage.success('已通知该学员缴费');
        });
        remindPaymentAsync(registrationId.value).then(res => {
          sendMessageAsync({ registrationId: registrationId.value, messageIds: res.data.messageIds }).then(() => {
            createMessage.success('已通知该学员缴费');
          });
        });
      },
    });
  }

  // 设置查询表单

  const currencyOptions = ref([]);
  const feeTypeOptions = ref([]);

  // 计算表格合计行
  function calculateSummary(tableData) {
    // 初始化合计对象
    const totalRow = {
      name: '合计',
      originalPrice: 0,
      systemDiscount: 0,
      manualAdjustment: 0,
      realPrice: 0,
    };

    // 累加所有行的值
    tableData.forEach(item => {
      totalRow.originalPrice += Number(item.originalPrice || 0);
      totalRow.systemDiscount += Number(item.systemDiscount || 0);
      totalRow.manualAdjustment += Number(item.manualAdjustment || 0);
      totalRow.realPrice += Number(item.realPrice || 0);
    });
    totalRow.originalPrice = xundaUtils.numtoAmount(totalRow.originalPrice);
    totalRow.systemDiscount = xundaUtils.numtoAmount(totalRow.systemDiscount);
    totalRow.manualAdjustment = xundaUtils.numtoAmount(totalRow.manualAdjustment);
    totalRow.realPrice = xundaUtils.numtoAmount(totalRow.realPrice);
    return [totalRow as any];
  }

  function init(data) {
    registrationId.value = data.id;
    getDictionaryDataSelector('currencyType').then(res => {
      currencyOptions.value = res.data.list;
    });
    getDictionaryDataSelector('feeType').then(res => {
      feeTypeOptions.value = res.data.list;
      updateSchema({
        field: 'name',
        componentProps: { options: feeTypeOptions.value, fieldNames: { label: 'fullName', value: 'enCode' } },
      });
    });
    reload();
  }

  defineExpose({
    init,
  });
</script>
