<template>
  <div class="xunda-content-wrapper">
    <div class="xunda-content-wrapper-center">
      <div class="xunda-content-wrapper-search-box">
        <BasicForm
          @register="registerSearchForm"
          :schemas="classesSearchSchemas"
          @advanced-change="redoHeight"
          @submit="handleSearchSubmit"
          @reset="handleSearchReset"
          class="search-form"
          v-if="false">
        </BasicForm>
      </div>
      <div class="xunda-content-wrapper-content bg-white">
        <BasicTable
          @register="registerTable"
          ref="tableRef"
          :contextMenuProps="{
            // 右键菜单
            width: 150,
            items: [
              {
                label: '编辑',
                icon: 'ant-design:edit-outlined',
                handler: record => handleEdit(record),
              },
              {
                label: '删除',
                icon: 'ant-design:delete-outlined',
                handler: record => {
                  Modal.confirm({
                    title: '确认删除?',
                    content: '确定要删除此项吗？',
                    onOk: () => {
                      // 执行删除操作
                      handleDelete(record.id);
                    },
                  });
                },
              },
            ],
          }">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="HandleAdd()"> {{ t('common.add2Text', '新增') }}</a-button>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form ref="formRef" @reload="reload" />
  </div>
</template>

<script lang="ts" setup>
  import { getClassesList, classesColumns, classesSearchSchemas, batchDeleteClasses } from './classesTable';
  import { ref, reactive, onMounted } from 'vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { BasicForm, useForm } from '@/components/Form';
  import { BasicTable, useTable, TableActionType } from '@/components/Table';
  import { useRoute } from 'vue-router';
  import { cloneDeep } from 'lodash-es';
  import { useUserStore } from '@/store/modules/user';
  import { usePermission } from '@/hooks/web/usePermission';
  import Form from '@/views/study/classes/form.vue';
  import { Modal } from 'ant-design-vue';

  const emit = defineEmits(['selection-change']);
  const props = defineProps({
    semesterId: {
      type: String,
      required: true,
    },
  });

  const userStore = useUserStore();
  const { hasFormP } = usePermission();
  const route = useRoute();
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const formRef = ref<any>(null);
  const tableRef = ref<Nullable<TableActionType>>(null);
  const cacheList = ref<any>([]);
  const defaultSearchInfo = {
    menuId: route.meta.modelId as string,
    moduleId: '***********', //模块ID
    superQueryJson: '',
    dataType: 0,
  };

  const searchInfo = reactive({
    ...cloneDeep(defaultSearchInfo),
  });

  const [registerSearchForm] = useForm({
    baseColProps: { span: 24 },
    submitOnChange: true,
    autoAdvancedLine: 1,
    wrapperCol: { span: 24 },
    showActionButtonGroup: true,
    showAdvancedButton: true,
  });

  const getList = data => {
    data.semesterId = props.semesterId;
    return getClassesList(data);
  };

  const [registerTable, { reload, redoHeight, clearSelectedRowKeys }] = useTable({
    api: getList,
    columns: classesColumns,
    searchInfo: searchInfo,
    clickToRowSelect: true,
    afterFetch: data => {
      const list = data.map(o => ({
        ...o,
      }));
      cacheList.value = cloneDeep(list);
      return list;
    },
    rowSelection: {
      type: 'radio',
      onChange: (selectedRowKeys, selectedRows) => {
        emit('selection-change', selectedRowKeys, selectedRows);
      },
      getCheckboxProps: record => ({
        disabled: record.top,
      }),
    },
  });

  function handleSearchReset() {
    clearSelectedRowKeys();
  }

  function handleSearchSubmit(data) {
    clearSelectedRowKeys();
    let obj = {
      ...defaultSearchInfo,
      superQueryJson: searchInfo.superQueryJson,
      ...data,
    };
    Object.keys(searchInfo).map(key => {
      delete searchInfo[key];
    });
    for (let [key, value] of Object.entries(obj)) {
      searchInfo[key.replaceAll('-', '_')] = value;
    }
    reload({ page: 1 });
  }
  function HandleAdd() {
    // 不带流程新增
    const data = {
      id: '',
      semesterId: props.semesterId,
      menuId: searchInfo.menuId,
      allList: cacheList.value,
    };
    formRef.value?.init(data);
  }
  // 编辑
  function handleEdit(record) {
    // 不带工作流
    const data = {
      id: record.id,
      menuId: searchInfo.menuId,
      allList: cacheList.value,
    };
    formRef.value?.init(data);
  }

  // 删除
  function handleDelete(id) {
    const query = { ids: [id] };
    batchDeleteClasses(query).then(res => {
      createMessage.success(res.msg);
      clearSelectedRowKeys();
      reload();
    });
  }

  function setSearchSchema() {}

  onMounted(() => {
    setSearchSchema();
  });
</script>
