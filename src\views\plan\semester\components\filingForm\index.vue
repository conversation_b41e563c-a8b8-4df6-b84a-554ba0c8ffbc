<template>
  <a-row class="mt-20px h-full" :class="'overflow-auto'">
    <a-col :span="22" :offset="1">
      <a-row class="dynamic-form">
        <a-form
          ref="formRef"
          :model="formState"
          size="middle"
          layout="horizontal"
          :colon="false"
          labelAlign="right"
          :labelCol="{ style: { width: '250px' } }"
          :rules="dataRule">
          <div class="form-actions">
            <a-button type="primary" @click="handlePrint">下载报备表</a-button>
          </div>
          <a-row :gutter="15">
            <!-- 基本信息 -->
            <a-col :span="24" class="ant-col-item" :hidden="false">
              <a-form-item name="planName">
                <template #label>
                  <span>计划名称</span>
                  <a-tooltip title="此处显示计划名称，不可修改">
                    <question-circle-outlined class="tip-icon" />
                  </a-tooltip>
                </template>
                <XundaInput v-model:value="formState.planName" :disabled="true" placeholder="请输入" :allowClear="true" :style="{ width: '100%' }">
                </XundaInput>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item" :hidden="false">
              <a-form-item name="recognitionPlace">
                <template #label>
                  <span>认定地点</span>
                  <a-tooltip title="请填写具体的认定考核地点">
                    <question-circle-outlined class="tip-icon" />
                  </a-tooltip>
                </template>
                <XundaInput v-model:value="formState.recognitionPlace" :disabled="disabled" placeholder="请输入" :allowClear="true" :style="{ width: '100%' }">
                </XundaInput>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item" :hidden="false">
              <a-form-item name="orgName">
                <template #label>
                  <span>机构名称</span>
                  <a-tooltip title="请填写完整的机构名称">
                    <question-circle-outlined class="tip-icon" />
                  </a-tooltip>
                </template>
                <XundaInput v-model:value="formState.orgName" :disabled="disabled" placeholder="请输入" :allowClear="true" :style="{ width: '100%' }">
                </XundaInput>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item" :hidden="false">
              <a-form-item name="contactPerson">
                <template #label>
                  <span>联系人</span>
                  <a-tooltip title="请填写实际负责人姓名">
                    <question-circle-outlined class="tip-icon" />
                  </a-tooltip>
                </template>
                <XundaInput v-model:value="formState.contactPerson" :disabled="disabled" placeholder="请输入" :allowClear="true" :style="{ width: '100%' }">
                </XundaInput>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item" :hidden="false">
              <a-form-item name="contactPhone">
                <template #label>
                  <span>联系电话</span>
                  <a-tooltip title="请填写有效的联系电话">
                    <question-circle-outlined class="tip-icon" />
                  </a-tooltip>
                </template>
                <XundaInput v-model:value="formState.contactPhone" :disabled="disabled" placeholder="请输入" :allowClear="true" :style="{ width: '100%' }">
                </XundaInput>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item" :hidden="false">
              <a-form-item name="qualitySupervisor">
                <template #label>
                  <span>内部质量督导人员</span>
                  <a-tooltip title="请填写负责质量督导的人员姓名">
                    <question-circle-outlined class="tip-icon" />
                  </a-tooltip>
                </template>
                <XundaInput v-model:value="formState.qualitySupervisor" :disabled="disabled" placeholder="请输入" :allowClear="true" :style="{ width: '100%' }">
                </XundaInput>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item" :hidden="false">
              <a-form-item name="professionLevelNum">
                <template #label>
                  <span>职业（工种）及级别人数</span>
                  <a-tooltip title='请按照示例格式填写：例如"焊工（三级）10人"'>
                    <question-circle-outlined class="tip-icon" />
                  </a-tooltip>
                </template>
                <XundaInput
                  v-model:value="formState.professionLevelNum"
                  placeholder="例：焊工（三级）10人"
                  :style="{ width: '100%' }"
                  :max="9999"
                  :step="1"
                  :controls="true"
                  :disabled="disabled">
                </XundaInput>
              </a-form-item>
            </a-col>
            <a-col :span="24" class="ant-col-item" :hidden="false">
              <!-- 安排场次及认定方式 -->
              <a-form-item name="theoryExam">
                <template #label>
                  <span>理论考试</span>
                  <a-tooltip title="请详细说明理论考试的场次、人数和认定方式">
                    <question-circle-outlined class="tip-icon" />
                  </a-tooltip>
                </template>
                <XundaTextarea
                  v-model:value="formState.theoryExam"
                  placeholder="请输入"
                  :allowClear="true"
                  :style="{ width: '100%' }"
                  :disabled="disabled"
                  :autoSize="{ minRows: 4, maxRows: 4 }">
                </XundaTextarea>
              </a-form-item>
            </a-col>
            <a-col :span="24" class="ant-col-item" :hidden="false">
              <a-form-item name="skillAssessment">
                <template #label>
                  <span>技能考核</span>
                  <a-tooltip title="请详细说明技能考核的分组、顺序和认定方式">
                    <question-circle-outlined class="tip-icon" />
                  </a-tooltip>
                </template>
                <XundaTextarea
                  v-model:value="formState.skillAssessment"
                  placeholder="请输入"
                  :allowClear="true"
                  :style="{ width: '100%' }"
                  :disabled="disabled"
                  :autoSize="{ minRows: 4, maxRows: 4 }">
                </XundaTextarea>
              </a-form-item>
            </a-col>
            <a-col :span="24" class="ant-col-item" :hidden="false">
              <a-form-item name="comprehensiveReview">
                <template #label>
                  <span>综合评审</span>
                  <a-tooltip title="请详细说明综合评审的进行方式和认定方式">
                    <question-circle-outlined class="tip-icon" />
                  </a-tooltip>
                </template>
                <XundaTextarea
                  v-model:value="formState.comprehensiveReview"
                  placeholder="请输入"
                  :allowClear="true"
                  :style="{ width: '100%' }"
                  :disabled="disabled"
                  :autoSize="{ minRows: 4, maxRows: 4 }">
                </XundaTextarea>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item" :hidden="false">
              <a-form-item name="professionName">
                <template #label>
                  <span>职业（工种）名称</span>
                  <a-tooltip title="请选择对应的职业（工种）名称">
                    <question-circle-outlined class="tip-icon" />
                  </a-tooltip>
                </template>
                <XundaSelect
                  v-model:value="formState.professionName"
                  :disabled="disabled"
                  :options="[
                    { key: '工程师', fullName: '工程师' },
                    { key: '高级工程师', fullName: '高级工程师' },
                    { key: '技师', fullName: '技师' },
                    { key: '高级技师', fullName: '高级技师' },
                  ]"
                  :fieldNames="{
                    label: 'fullName',
                    value: 'key',
                  }"
                  :allowClear="true"
                  :style="{ width: '100%' }"></XundaSelect>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item" :hidden="false">
              <a-form-item name="skillLevel">
                <template #label>
                  <span>技能等级</span>
                  <a-tooltip title="请选择对应的技能等级">
                    <question-circle-outlined class="tip-icon" />
                  </a-tooltip> </template
                ><XundaSelect
                  v-model:value="formState.skillLevel"
                  :disabled="disabled"
                  :options="[
                    {
                      key: '1级',
                      fullName: '1级',
                    },
                    {
                      key: '2级',
                      fullName: '2级',
                    },
                    {
                      key: '3级',
                      fullName: '3级',
                    },
                    {
                      key: '4级',
                      fullName: '4级',
                    },
                    {
                      key: '5级',
                      fullName: '5级',
                    },
                  ]"
                  :fieldNames="{
                    label: 'fullName',
                    value: 'key',
                  }"
                  :allowClear="true"
                  :style="{ width: '100%' }"></XundaSelect>
              </a-form-item>
            </a-col>
            <a-col :span="24" class="ant-col-item" :hidden="false">
              <!-- 评价内容 表格 -->
              <a-form-item>
                <template #label>
                  <span>评价内容</span>
                  <a-tooltip title="请填写完整的评价内容，包括科目、认定人数、监考/考评人员、认定日期和时间">
                    <question-circle-outlined class="tip-icon" />
                  </a-tooltip>
                </template>
                <a-table ref="tableRef" bordered :data-source="dataSource" :columns="columns" :pagination="false">
                  <template #bodyCell="{ column, text, record }">
                    <template v-if="column.dataIndex === 'recognitionNum'">
                      <div v-if="!detailed" class="editable-cell-input-wrapper">
                        <XundaInputNumber
                          v-model:value="dataSource[record.index].recognitionNum"
                          placeholder="请输入"
                          :style="{ width: '100%' }"
                          :max="9999"
                          :step="1"
                          :controls="true">
                        </XundaInputNumber>
                      </div>
                      <div v-else class="editable-cell-text-wrapper">
                        {{ text || ' ' }}
                      </div>
                    </template>
                    <template v-if="column.dataIndex === 'supervisorEvaluator'">
                      <div v-if="!detailed" class="editable-cell-input-wrapper">
                        <XundaInput v-model:value="dataSource[record.index].supervisorEvaluator" placeholder="请输入" :style="{ width: '100%' }"> </XundaInput>
                      </div>
                      <div v-else class="editable-cell-text-wrapper">
                        {{ text || ' ' }}
                      </div>
                    </template>
                    <template v-if="column.dataIndex === 'recognitionDate'">
                      <div v-if="!detailed" class="editable-cell-input-wrapper">
                        <XundaDatePicker
                          v-model:value="dataSource[record.index].recognitionDate"
                          placeholder="请选择日期"
                          :allowClear="true"
                          :style="{ width: '100%' }"
                          format="yyyy-MM-dd">
                        </XundaDatePicker>
                      </div>
                      <div v-else class="editable-cell-text-wrapper">
                        {{ xundaUtils.toDateString(text) || ' ' }}
                      </div>
                    </template>
                    <template v-if="column.dataIndex === 'recognitionTime'">
                      <div v-if="!detailed" class="editable-cell-input-wrapper">
                        <XundaTimePicker
                          v-model:value="dataSource[record.index].recognitionTime"
                          placeholder="请选择"
                          :allowClear="true"
                          :style="{ width: '100%' }"
                          format="HH:mm:ss" />
                      </div>
                      <div v-else class="editable-cell-text-wrapper">
                        {{ text || ' ' }}
                      </div>
                    </template>
                  </template>
                </a-table>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-row>
    </a-col>
  </a-row>
</template>

<style scoped>
  .tip-icon {
    margin-left: 4px;
    color: #999;
    cursor: pointer;
  }
  .tip-icon:hover {
    color: #1890ff;
  }
</style>

<script lang="ts" setup>
  import { computed, reactive, ref, unref } from 'vue';
  import { QuestionCircleOutlined } from '@ant-design/icons-vue';
  import { xundaUtils } from '@/utils/xunda';
  import { useMessage } from '@/hooks/web/useMessage';
  import { getFilingForm, printFilingFormAsync, setFilingForm } from './index';
  import { XundaSelect } from '@/components/Xunda';
  import { downloadByUrl } from '@/utils/file/download';
  const { createMessage } = useMessage();

  const emit = defineEmits(['changeLoading', 'handleStep']);
  const changeLoading = loading => {
    emit('changeLoading', loading);
  };
  const props = defineProps({
    semesterId: { type: String, default: '' },
    semesterName: { type: String, default: '' },
    disabled: { type: Boolean, default: false },
  });

  const detailed = computed(() => props.disabled);

  const defaultFormData = reactive({
    orgName: '贵州交通职业学院',
    contactPerson: '',
    contactPhone: '',
    planName: '',
    recognitionPlace: '',
    qualitySupervisor: '',
    professionLevelNum: '',
    theoryExam: '一场30人。认定方式：机考',
    skillAssessment: '分组抽签顺序，按工位设置进行。认定方式：实际操作',
    comprehensiveReview: '按分组抽签顺序进行。认定方式：论文写作、口头答辩',
    professionName: '',
    skillLevel: '',
    recognition: [],
    evaluationOpinion: '',
    countyOpinion: '',
    cityOpinion: '',
    provinceOpinion: '',
    evaluationData: [],
  });

  const formState = ref<any>({});
  // 都设置为必填项
  const dataRule = {
    orgName: [{ required: 'true', message: '请输入机构名称', trigger: 'blur' }],
    contactPerson: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
    contactPhone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
    planName: [{ required: true, message: '请输入计划名称', trigger: 'blur' }],
    recognitionPlace: [{ required: true, message: '请输入认定地点', trigger: 'blur' }],
    qualitySupervisor: [{ required: true, message: '请输入质量监督员', trigger: 'blur' }],
    professionLevelNum: [{ required: true, message: '请输入职业等级编号', trigger: 'blur' }],
    theoryExam: [{ required: true, message: '请输入理论考试', trigger: 'blur' }],
    practicalExam: [{ required: true, message: '请输入实操考试', trigger: 'blur' }],
    skillAssessment: [{ required: true, message: '请输入技能考核', trigger: 'blur' }],
    comprehensiveReview: [{ required: true, message: '请输入综合评审', trigger: 'blur' }],
    professionName: [{ required: true, message: '请输入职业（工种）名称', trigger: 'blur' }],
    skillLevel: [{ required: true, message: '请输入技能等级', trigger: 'blur' }],
    subject: [{ required: true, message: '请输入科目', trigger: 'blur' }],
    recognitionNum: [{ required: true, message: '请输入认定人数', trigger: 'blur' }],
    supervisorEvaluator: [{ required: true, message: '请输入监督员评价人', trigger: 'blur' }],
    recognitionDate: [{ required: true, message: '请输入认定日期', trigger: 'blur' }],
    recognitionTime: [{ required: true, message: '请输入认定时间', trigger: 'blur' }],
    evaluationOpinion: [{ required: true, message: '请输入评价机构意见', trigger: 'blur' }],
    recognitionResult: [{ required: true, message: '请输入认定结果', trigger: 'blur' }],
    countyOpinion: [{ required: true, message: '请输入县（区）人力资源社会保障部门意见', trigger: 'blur' }],
    cityOpinion: [{ required: true, message: '请输入市（州）人力资源社会保障部门意见', trigger: 'blur' }],
    provinceOpinion: [{ required: true, message: '请输入省人力资源社会保障部门意见', trigger: 'blur' }],
  };

  // 表单提交
  const formRef = ref();

  function getFormRef() {
    const form = unref(formRef);
    if (!form) throw new Error('form is null!');
    return form;
  }

  // 定义表格列
  const columns = [
    {
      title: '科目',
      dataIndex: 'subject',
      key: 'subject',
    },
    {
      title: '认定人数',
      dataIndex: 'recognitionNum',
      key: 'recognitionNum',
    },
    {
      title: '监考/考评人员',
      dataIndex: 'supervisorEvaluator',
      key: 'supervisorEvaluator',
    },
    {
      title: '认定日期',
      dataIndex: 'recognitionDate',
      key: 'recognitionDate',
    },
    {
      title: '认定时间',
      dataIndex: 'recognitionTime',
      key: 'recognitionTime',
    },
  ];

  const defaultDataSource = ref([
    { index: 0, subject: '理论考试', recognitionNum: null, supervisorEvaluator: '', recognitionDate: null, recognitionTime: null },
    { index: 1, subject: '技能考核', recognitionNum: null, supervisorEvaluator: '', recognitionDate: null, recognitionTime: null },
    { index: 2, subject: '综合评审', recognitionNum: null, supervisorEvaluator: '', recognitionDate: null, recognitionTime: null },
  ]);
  const dataSource = ref([
    { index: 0, subject: '理论考试', recognitionNum: null, supervisorEvaluator: '', recognitionDate: null, recognitionTime: null },
    { index: 1, subject: '技能考核', recognitionNum: null, supervisorEvaluator: '', recognitionDate: null, recognitionTime: null },
    { index: 2, subject: '综合评审', recognitionNum: null, supervisorEvaluator: '', recognitionDate: null, recognitionTime: null },
  ]);

  function getForm() {
    const form = unref(formRef);
    if (!form) {
      throw new Error('form is null!');
    }
    return form;
  }
  // 初始化表单数据
  function init(data) {
    changeLoading(true);
    getFilingForm({ id: data.id })
      .then(res => {
        initData(res.data ?? { planName: props.semesterName });
        changeLoading(false);
      })
      .catch(() => {
        changeLoading(false);
      });
  }

  function initData(data) {
    formState.value = { ...defaultFormData, ...data };
    dataSource.value = defaultDataSource.value;
    if (formState.value.recognition && formState.value.recognition.length > 0) {
      dataSource.value = formState.value.recognition;
    }
  }

  function getTableData() {
    return dataSource.value;
  }
  async function handleSubmit(type) {
    try {
      if (type === 'next') {
        const values = await getForm()?.validate();
        if (!values) return;
      }
      formState.value.recognition = getTableData();

      // if (!formState.value.recognition && formState.value.recognition.length !== 0) {
      //   let num = 0;
      //   for (let i = 0; i < formState.value.recognition.length; i++) {
      //     if (!formState.value.recognition[i].subject) {
      //       createMessage.error('请输入科目');
      //       return;
      //     }
      //     if (!formState.value.recognition[i].recognitionNum) {
      //       createMessage.error('请输入认定人数');
      //       return;
      //     } else {
      //       num += formState.value.recognition[i].recognitionNum;
      //     }
      //   }
      // }

      changeLoading(true);
      setFilingForm({ id: props.semesterId, filingForm: formState.value })
        .then(res => {
          if (res.code === 200 && res.data === true) {
            createMessage.success('报备表配置成功');
            changeLoading(false);
            emit('handleStep', type);
          }
        })
        .catch(() => {
          changeLoading(false);
        });
    } catch (_) {
      changeLoading(false);
    }
  }
  function handlePrint() {
    printFilingFormAsync(props.semesterId, 'filingForm').then(res => {
      if (res.code === 200) {
        downloadByUrl({ url: res.data?.url });
      }
    });
  }

  defineExpose({
    init,
    handleSubmit,
  });
</script>
