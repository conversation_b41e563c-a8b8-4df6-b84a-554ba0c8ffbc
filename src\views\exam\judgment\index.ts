import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';

// 基础Api
export const judgmentApi = '/api/exam/judgment';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: judgmentApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: judgmentApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: judgmentApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: judgmentApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: judgmentApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: judgmentApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: judgmentApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: judgmentApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: judgmentApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: judgmentApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: judgmentApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: judgmentApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: judgmentApi + `/getForSelect`, data });
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'name',
    label: t('改卷人'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('标识'),
    dataIndex: 'id',
    width: 120,
  },
  {
    title: t('题目'),
    dataIndex: 'studentQuestionId',
    width: 120,
  },
  {
    title: t('分数'),
    dataIndex: 'score',
    width: 120,
  },
  {
    title: t('说明'),
    dataIndex: 'note',
    width: 120,
  },
  {
    title: t('申述后重改'),
    dataIndex: 'reviewFlag',
    width: 120,
    customRender: xundaUtils.customRenderBoolean,
  },
  {
    title: t('改卷人'),
    dataIndex: 'name',
    width: 120,
  },
];
