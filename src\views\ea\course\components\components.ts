import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';

/**
 * 表格列配置
 */
export const courseColumns: BasicColumn[] = [
  {
    title: t('学校'),
    dataIndex: 'schoolName',
    width: 120,
  },
  {
    title: t('校区'),
    dataIndex: 'campusName',
    width: 120,
  },
  {
    title: t('课程号'),
    dataIndex: 'no',
    width: 120,
  },
  {
    title: t('课程名（中文）'),
    dataIndex: 'name',
    width: 120,
  },
  {
    title: t('课程名（英文）'),
    dataIndex: 'englishName',
    width: 120,
  },
  {
    title: t('学分'),
    dataIndex: 'credit',
    width: 120,
  },
];
