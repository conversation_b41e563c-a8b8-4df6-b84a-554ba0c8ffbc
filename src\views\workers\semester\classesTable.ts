import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { defHttp } from '@/utils/http/axios';

const { t } = useI18n();

// 基础Api
export const classesApi = '/api/study/classes';

/**
 * 获取列表
 * @param data
 * @returns
 */
export function getClassesList(data) {
  return defHttp.post({ url: classesApi + `/getList`, data });
}

// 批量删除数据
export function batchDeleteClasses(data) {
  return defHttp.delete({ url: classesApi + `/batchRemove`, data });
}

/**
 * 搜索表单配置
 */
export const classesSearchSchemas: FormSchema[] = [
  {
    field: 'name',
    label: t('班级名称'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
];

/**
 * 表格列配置
 */
export const classesColumns: BasicColumn[] = [
  {
    title: t('班级名称'),
    dataIndex: 'name',
    width: 120,
  },
];
