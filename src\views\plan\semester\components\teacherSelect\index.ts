import { useI18n } from '@/hooks/web/useI18n';
import { FormSchema } from '@/components/Form';

import { BasicColumn } from '@/components/Table/src/types/table';
import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
// 获取列表
export const teacherApi = '/api/ea/teacher';
export function getAllTeacher(data) {
  return defHttp.post({ url: teacherApi + `/getList`, data });
}

export const semesterApi = '/api/plan/semester';
// 发布计划
export function getTeachers(id) {
  return defHttp.post({ url: semesterApi + `/getTeachers/` + id });
}

export function getSemesterTeachers(id) {
  return defHttp.post({ url: semesterApi + `/getTeachers/` + id });
}

// 考评人员
export function saveTeacherJson(data) {
  return defHttp.post({ url: semesterApi + `/saveTeacherJson`, data });
}
// 随机抽取考评人员
export function randomGenTeacher(data) {
  return defHttp.post({ url: semesterApi + `/randomGenTeacher`, data });
}
/**
 * 表格列配置
 */
export const teacherColumns: BasicColumn[] = [
  {
    dataIndex: 'name', // 名称
    title: t('ea.teacher.name'),
    width: 120,
  },
  {
    dataIndex: 'nickname', // 昵称
    title: t('ea.teacher.nickname'),
    width: 120,
  },
  {
    dataIndex: 'no', // 工号
    title: t('ea.teacher.no'),
    width: 120,
  },
  {
    dataIndex: 'graduatedAt', // 毕业学校
    title: t('ea.teacher.graduatedAt'),
    width: 120,
  },
  {
    dataIndex: 'idCardType', // 证件类型
    title: t('ea.teacher.idCardType'),
    width: 120,
  },
  {
    dataIndex: 'idCardNo', // 证件号
    title: t('ea.teacher.idCardNo'),
    width: 120,
  },
  {
    dataIndex: 'workAt', // 就职单位
    title: t('ea.teacher.workAt'),
    width: 120,
  },
  {
    dataIndex: 'highestEducation', // 最高学历
    title: t('ea.teacher.highestEducation'),
    width: 120,
  },
  {
    dataIndex: 'profession', // 专业
    title: t('ea.teacher.profession'),
    width: 120,
  },
  {
    dataIndex: 'jobTitle', // 职称
    title: t('ea.teacher.jobTitle'),
    width: 120,
  },
  {
    dataIndex: 'note', // 备注
    title: t('ea.teacher.note'),
    width: 120,
  },
  {
    dataIndex: 'extraInfo', // 扩展信息
    title: t('ea.teacher.extraInfo'),
    width: 120,
  },
];

export const leftSearchSchemas: FormSchema[] = [
  {
    field: 'name',
    label: t('姓名'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
  {
    field: 'no',
    label: t('工号'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
  {
    field: 'profession',
    label: t('专业'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
  {
    field: 'teacherLevel',
    label: t('级别'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
];
