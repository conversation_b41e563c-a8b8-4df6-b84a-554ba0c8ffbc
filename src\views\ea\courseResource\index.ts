import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';

// 基础Api
export const courseResourceApi = '/api/ea/courseResource';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: courseResourceApi + `/getList`, data });
}
// 获取树形列表
export function getTree(data) {
  return defHttp.post({ url: courseResourceApi + `/getTree`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: courseResourceApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: courseResourceApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: courseResourceApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: courseResourceApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: courseResourceApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: courseResourceApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: courseResourceApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: courseResourceApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: courseResourceApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: courseResourceApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: courseResourceApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: courseResourceApi + `/getForSelect`, data });
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('主键'),
    dataIndex: 'id',
    width: 120,
  },
  {
    title: t('课程'),
    dataIndex: 'courseId',
    width: 120,
  },
  {
    title: t('父级'),
    dataIndex: 'parentId',
    width: 120,
  },
  {
    title: t('显示顺序'),
    dataIndex: 'displayOrder',
    width: 120,
  },
  {
    title: t('公开模式'),
    dataIndex: 'viewRole',
    width: 120,
  },
  {
    title: t('叶子节点'),
    dataIndex: 'leafFlag',
    width: 120,
    customRender: xundaUtils.customRenderBoolean,
  },
  // {
  //   title: t('附件'),
  //   dataIndex: 'attachment',
  //   width: 120,
  // },
  {
    title: t('附件'),
    dataIndex: 'attachmentList',
    width: 400,
    customRender: ({ record }) => (record.attachmentList = record.attachment ? JSON.parse(record.attachment) : []),
  },
  {
    title: t('描述'),
    dataIndex: 'description',
    width: 120,
  },
  {
    title: t('访问人数'),
    dataIndex: 'viewCount',
    width: 120,
  },
  {
    title: t('下载次数'),
    dataIndex: 'downloadCount',
    width: 120,
  },
  {
    title: t('大小'),
    dataIndex: 'size',
    width: 120,
  },
  {
    title: t('创建人'),
    dataIndex: 'creator',
    width: 120,
  },
];
