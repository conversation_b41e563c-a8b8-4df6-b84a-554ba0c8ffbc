import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
import { getDictionaryFullName, toDateString, toFixedPercent } from '@/utils/myUtil';
import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';

// 基础Api
export const examConfigApi = '/api/exam/examConfig';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: examConfigApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: examConfigApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: examConfigApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: examConfigApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: examConfigApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: examConfigApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: examConfigApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: examConfigApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: examConfigApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: examConfigApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: examConfigApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: examConfigApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: examConfigApi + `/getForSelect`, data });
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('名称'),
    dataIndex: 'name',
    width: 120,
  },
  {
    title: t('计划'),
    dataIndex: 'semesterName',
    width: 120,
  },
  {
    title: t('课程'),
    dataIndex: 'courseName',
    width: 120,
  },
  {
    title: t('开始时间'),
    dataIndex: 'beginTime',
    width: 120,
  },
  {
    title: t('结束时间'),
    dataIndex: 'endTime',
    width: 120,
  },
  {
    title: t('准备时间'),
    dataIndex: 'prepareTime',
    width: 120,
  },
  {
    title: t('考试限时'),
    dataIndex: 'limitTime',
    width: 120,
  },
  {
    title: t('可交卷时间'),
    dataIndex: 'submitTime',
    width: 120,
  },
  {
    title: t('允许迟到时间'),
    dataIndex: 'lateTime',
    width: 120,
  },
  {
    title: t('总分'),
    dataIndex: 'totalScore',
    width: 120,
  },
  {
    title: t('及格分'),
    dataIndex: 'passScore',
    width: 120,
  },
  {
    title: t('题目数'),
    dataIndex: 'questionCount',
    width: 120,
  },
  {
    title: t('试卷模式（随机，固定）'),
    dataIndex: 'paperMode',
    width: 120,
  },
  {
    title: t('考试状态'),
    dataIndex: 'examState',
    width: 120,
  },
];
