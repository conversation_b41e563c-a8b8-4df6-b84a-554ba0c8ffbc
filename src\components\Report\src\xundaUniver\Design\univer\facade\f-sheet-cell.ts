import { ICellData } from '@univerjs/core';
import { XundaSheetsCellService } from '../services/sheet-cell.service';

export class XundaFacadeSheetsCell {
  constructor(private readonly _xundaSheetsCellService: XundaSheetsCellService) {}

  getCellData(col: number, row: number) {
    return this._xundaSheetsCellService?.getCellData(col, row);
  }

  setCellData(col: number, row: number, cellData: ICellData) {
    this._xundaSheetsCellService?.setCellData(col, row, cellData);
  }

  getTargetCellSize(col: number, row: number) {
    return this._xundaSheetsCellService?.getTargetCellSize(col, row);
  }

  getCurrentSheetCellsCustom() {
    return this._xundaSheetsCellService?.getCurrentSheetCellsCustom();
  }

  refreshRangeCellsView(range: any) {
    this._xundaSheetsCellService?.refreshRangeCellsView(range);
  }
}
