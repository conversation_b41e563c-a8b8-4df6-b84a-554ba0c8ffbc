<template>
  <div class="plan-result">
    <a-card class="result-card" :bordered="false">
      <div class="status-content">
        <template v-if="publishFlag">
          <a-result status="success" title="计划已发布" sub-title="您可以进行以下操作">
            <template #extra> </template>
            <div class="action-section">
              <div class="section-title">计划操作</div>
              <div class="action-buttons">
                <a-button v-if="category === '0001'" type="primary" @click="handlePrintNotice" class="action-button">
                  <template #icon><PrinterOutlined /></template>
                  打印公告
                </a-button>
                <a-button v-if="category === '0001'" type="primary" @click="handlePrintReport" class="action-button">
                  <template #icon><PrinterOutlined /></template>
                  打印报备表
                </a-button>
                <div v-if="category === '0002'" class="register-form-actions" hidden>
                  <a-button type="primary" class="register-form-btn" @click="handleView">
                    <FileDoneOutlined />
                    <span>申请登记表</span>
                  </a-button>
                </div>
                <a-button danger @click="handleUnPublish" class="action-button">
                  <template #icon><NotificationOutlined /></template>
                  撤销发布
                </a-button>
              </div>
            </div>
          </a-result>
        </template>
        <template v-else>
          <a-result status="warning" title="计划未发布" sub-title="完成以下操作以发布计划">
            <template #extra>
              <div class="action-section">
                <div class="section-title">发布操作</div>
                <div class="action-buttons">
                  <a-button v-if="category === '0001'" type="primary" @click="handlePrintNotice" class="action-button">
                    <template #icon><PrinterOutlined /></template>
                    公告预览
                  </a-button>
                  <a-button v-if="category === '0001'" type="primary" @click="handlePrintReport" class="action-button">
                    <template #icon><PrinterOutlined /></template>
                    报备表预览
                  </a-button>
                  <a-button type="warning" @click="handlePublish" class="action-button publish-button">
                    <template #icon><NotificationOutlined /></template>
                    {{ t('发布计划') }}
                  </a-button>
                </div>
              </div>
            </template>
          </a-result>
        </template>
      </div>
    </a-card>
  </div>
  <PrintBrowse @register="registerPrintBrowse" />
  <PdfPreview @register="registerPdfPreview" />
</template>

<script lang="ts" setup>
  import { PrinterOutlined, NotificationOutlined, FileDoneOutlined } from '@ant-design/icons-vue';
  import { ref } from 'vue';
  import AResult from 'ant-design-vue/es/result';
  import PrintBrowse from '@/components/PrintDesign/printBrowse/index.vue';
  import { useModal } from '@/components/Modal';
  import { checkRegistrationAsync, getPublishFlagAsync, printFilingFormAsync, printNoticeDataAsync, publishSemester } from '.';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { downloadByUrl } from '@/utils/file/download';
  import PdfPreview from '@/components/Bit/PdfPreview/src/index.vue';
  import { getDownloadUrl } from '@/api/basic/common';

  const emit = defineEmits(['changeLoading', 'handleStep', 'afterPublish', 'update:publishFlag']);
  const changeLoading = loading => {
    emit('changeLoading', loading);
  };
  const { t } = useI18n();
  const { createMessage, createConfirm } = useMessage();

  const [registerPrintBrowse, { openModal: openPrintBrowse }] = useModal();
  const props = defineProps({
    semesterId: {
      type: String,
      required: true,
    },
    publishFlag: {
      type: Boolean,
      default: false,
    },
    category: {
      type: String,
      required: true,
    },
  });
  const djbDownloadUrl = ref([
    {
      fileSize: 4695,
      fileExtension: 'xlsx',
      name: '生产经营单位"三项岗位"人员安全生产培训申请登记表.doc',
      url: '/api/file/Image/templatefile/plan,san_xiang_gang_wei_shen_qing_deng_ji_biao.doc',
      fileId: 'plan,san_xiang_gang_wei_shen_qing_deng_ji_biao.doc',
    },
    {
      fileSize: 4695,
      fileExtension: 'xlsx',
      name: '生产经营单位"三项岗位"人员安全生产培训申请登记表.pdf',
      url: '/api/file/Image/templatefile/plan,san_xiang_gang_wei_shen_qing_deng_ji_biao.pdf',
      fileId: 'plan,san_xiang_gang_wei_shen_qing_deng_ji_biao.pdf',
    },
  ]);

  const handlePrintNotice = () => {
    printNoticeDataAsync(props.semesterId, 'noticeData').then(res => {
      if (res.code === 200) {
        downloadByUrl({ url: res.data?.url });
      }
    });
  };

  const handlePrintReport = () => {
    printFilingFormAsync(props.semesterId, 'filingForm').then(res => {
      if (res.code === 200) {
        downloadByUrl({ url: res.data?.url });
      }
    });
  };

  // 发布计划
  function handlePublish() {
    createConfirm({
      iconType: 'warning',
      title: '发布计划',
      content: '确认发布计划吗？',
      onOk: () => {
        changeLoading(true);
        publishSemester({
          id: props.semesterId,
          isPublish: true,
        })
          .then(res => {
            createMessage.success('计划发布成功');
            getPublishFlag();
          })
          .catch(() => {
            changeLoading(false);
          });
      },
    });
  }

  // 撤销发布
  function handleUnPublish() {
    changeLoading(true);
    // 判断是否有人报名
    checkRegistrationAsync(props.semesterId)
      .then(res => {
        changeLoading(false);
        var content = '确认撤销计划吗？';
        if (res.data === true) {
          content = '计划已有人员报名，确认撤销计划吗？';
        }
        createConfirm({
          iconType: 'warning',
          title: '撤销发布',
          content: content,
          onOk: () => {
            changeLoading(true);
            publishSemester({
              id: props.semesterId,
              isPublish: false,
            })
              .then(res => {
                changeLoading(false);
                if (res.data) {
                  createMessage.success('计划撤销成功');
                  changeLoading(false);
                  getPublishFlag();
                }
              })
              .catch(() => {
                changeLoading(false);
              });
          },
        });
      })
      .catch(() => {
        changeLoading(false);
      });
  }
  function getPublishFlag() {
    changeLoading(true);
    getPublishFlagAsync(props.semesterId)
      .then(res => {
        emit('update:publishFlag', res.data.publishFlag);
        changeLoading(false);
      })
      .catch(() => {
        changeLoading(false);
      });
  }

  function handleDownload() {
    createConfirm({
      iconType: 'warning',
      title: '下载登记表',
      content: '确认下载登记表吗？',
      onOk: () => {
        getDownloadUrl('templatefile', djbDownloadUrl.value[0].fileId).then(res => {
          downloadByUrl({
            url: res.data.url,
            fileName: djbDownloadUrl.value[0].name,
          });
        });
      },
    });
  }

  const [registerPdfPreview, { openModal: openPdfPreviewModal }] = useModal();

  function handleView() {
    // createConfirm({
    //   iconType: 'info',
    //   title: '查看登记表',
    //   content: '确认在新窗口查看登记表吗？',
    //   onOk: () => {
    openPdfPreviewModal(true, {
      ...djbDownloadUrl.value[1],
      type: 'templatefile',
    });
    // createMessage.success(`正在预览: ${djbDownloadUrl.value[0].name}`);
    //   },
    // });
  }

  const init = data => {
    getPublishFlag();
  };

  defineExpose({
    init,
  });
</script>

<style lang="less" scoped>
  .plan-result {
    width: 100%;
    padding: 24px;
    min-height: 100%;

    .result-card {
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 6px 24px rgba(0, 0, 0, 0.08);
        transform: translateY(-2px);
      }
    }

    .status-content {
      display: flex;
      justify-content: center;
      padding: 32px 0;

      :deep(.ant-result) {
        padding: 24px 32px;

        .ant-result-title {
          font-size: 24px;
          font-weight: 600;
          color: #1a1a1a;
          margin-bottom: 16px;
        }

        .ant-result-subtitle {
          font-size: 16px;
          color: #666;
          margin-bottom: 32px;
        }

        .ant-result-icon {
          margin-bottom: 24px;

          .anticon {
            font-size: 72px;
          }
        }
      }
    }

    .action-section {
      background: #fafafa;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 24px;
      transition: all 0.3s ease;

      &:hover {
        background: #f5f7fa;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      }

      .section-title {
        font-size: 16px;
        font-weight: 500;
        color: #1a1a1a;
        margin-bottom: 16px;
        padding-left: 12px;
        border-left: 3px solid @primary-color;
      }

      .action-buttons {
        display: flex;
        gap: 16px;
        justify-content: center;
        flex-wrap: nowrap;
        overflow-x: auto;
        padding-bottom: 5px;
      }
    }
  }

  /* 按钮通用样式 */
  .action-button,
  .register-form-btn {
    min-width: 140px;
    height: 40px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .anticon {
      font-size: 16px;
    }

    &.publish-button {
      background: linear-gradient(135deg, #faad14 0%, #d48806 100%);
      border: none;
      color: #fff;

      &:hover {
        background: linear-gradient(135deg, #ffc53d 0%, #faad14 100%);
      }
    }
  }

  /* 申请登记表按钮组样式 */
  .register-form-actions {
    display: flex;
    align-items: center;
  }

  .action-icon-btn {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.25s ease;

    .anticon {
      font-size: 16px;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
    }
  }
</style>
