import { useI18n } from '@/hooks/web/useI18n';
import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();

// 基础Api
export const semesterApi = '/api/plan/semester';
export const planNoticeApi = '/api/plan/notice';

// 发布计划
export function publishSemester(data) {
  return defHttp.post({ url: semesterApi + `/publishSemester`, data });
}

export function getPublishFlagAsync(id) {
  return defHttp.get({ url: semesterApi + `/getPublishFlag/` + id });
}
export function checkRegistrationAsync(id) {
  return defHttp.get({ url: semesterApi + `/checkRegistration/` + id });
}
export function printNoticeDataAsync(id, type) {
  // 调用defHttp的post方法，发送请求
  return defHttp.post({ url: semesterApi + `/printNoticeData/` + id + '/' + type });
  // 请求URL由semesterApi、`/printNoticeData/`和id拼接而成
}
export function printFilingFormAsync(id, type) {
  // 调用defHttp的post方法，发送请求
  return defHttp.post({ url: semesterApi + `/printNoticeData/` + id + '/' + type });
  // 请求URL由semesterApi、`/printNoticeData/`和id拼接而成
}
