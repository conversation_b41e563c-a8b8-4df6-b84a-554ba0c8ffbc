import { defHttp } from '@/utils/http/axios';

//获取考试题目
export const getExam = (id:number) => {
  return defHttp.get({
    url:`/api/train/TrainTestPaperLibrary/onlineExam/${id}`
  })
}

//提交考试答案
export const submit = (params) => {
  return defHttp.post({
    url:'/api/train/TrainTestPaperLibrary/onlineExam/submit',params
  })
}

//查看考试详情
export const getDetail = (id:number) => {
  return defHttp.get({
    url:`/api/train/TrainExamRecords/examRecordsDetail/${id}`
  })
}
