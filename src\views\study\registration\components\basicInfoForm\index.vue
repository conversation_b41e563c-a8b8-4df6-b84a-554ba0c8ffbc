<template>
  <div class="basic-info-form">
    <a-form :model="dataForm" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" readonly>
      <div class="form-card">
        <div class="section-title">基本信息</div>
        <div class="form-row">
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item label="申请人姓名" name="name">
              <div class="detail-text">{{ dataForm.name || '-' }}</div>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item label="学历" name="educationalBackground">
              <div class="detail-text">{{ dataForm.educationalBackground || '-' }}</div>
            </a-form-item>
          </a-col>
        </div>
        <div class="form-row">
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item label="出生日期" name="birthday">
              <div class="detail-text">{{ dataForm.birthday || '-' }}</div>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item label="专业" name="major">
              <div class="detail-text">{{ dataForm.major || '-' }}</div>
            </a-form-item>
          </a-col>
        </div>

        <div class="form-row">
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item label="证件号" name="idNo">
              <div class="detail-text">{{ dataForm.idNo || '-' }}</div>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item label="手机号" name="phone">
              <div class="detail-text">{{ dataForm.phone || '-' }}</div>
            </a-form-item>
          </a-col>
        </div>

        <div class="form-row">
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item label="政治面貌" name="politicalStatus">
              <div class="detail-text">{{ dataForm.politicalStatus || '-' }}</div>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item label="户籍地址" name="permanentAddress">
              <div class="detail-text">{{ dataForm.permanentAddress || '-' }}</div>
            </a-form-item>
          </a-col>
        </div>

        <div class="form-row">
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item label="居住地址" name="residentialAddress">
              <div class="detail-text">{{ dataForm.residentialAddress || '-' }}</div>
            </a-form-item>
          </a-col>
        </div>
      </div>
    </a-form>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { getBasicInfoAsync } from '.';

  const emit = defineEmits(['changeLoading', 'next', 'update:auditState', 'update:semesterCatogry']);
  const changeLoading = loading => {
    emit('changeLoading', loading);
  };

  const dataForm = ref<any>({} as any);

  const init = (record: any) => {
    changeLoading(true);
    getBasicInfoAsync(record.id)
      .then(res => {
        dataForm.value = res.data;
        changeLoading(false);
      })
      .catch(() => {
        changeLoading(false);
      });
  };

  defineExpose({
    init,
  });
</script>

<style lang="less" scoped>
  .detail-text {
    line-height: 32px;
    min-height: 32px;
    color: #262626;
    font-size: 14px;
    transition: all 0.3s ease;
    background: linear-gradient(to right, #fafafa, #ffffff);
    border-radius: 6px;
    border: 1px solid #eaeaea;
    padding: 6px 16px;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.02);
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;

    &:hover {
      background: linear-gradient(to right, #f0f7ff, #f8faff);
      border-color: #bae0ff;
      box-shadow: inset 0 2px 4px rgba(24, 144, 255, 0.05);
      transform: translateY(-1px);
    }

    &::after {
      content: '';
      position: absolute;
      bottom: -1px;
      left: 0;
      width: 100%;
      height: 2px;
      background: linear-gradient(to right, #1890ff, transparent);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover::after {
      opacity: 1;
    }
  }

  .basic-info-form {
    padding: 32px;
    min-height: 100%;

    .form-card {
      background: #fff;
      padding: 28px;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      margin-bottom: 28px;
      border: 1px solid rgba(234, 234, 234, 0.8);
      backdrop-filter: blur(10px);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(to right, #1890ff, #40a9ff, #69c0ff);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover {
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
        border-color: rgba(24, 144, 255, 0.1);

        &::before {
          opacity: 1;
        }
      }

      .section-title {
        font-size: 20px;
        font-weight: 600;
        color: #1a1a1a;
        margin: 0 0 28px;
        padding-left: 20px;
        position: relative;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #f0f0f0;
        padding-bottom: 16px;
        background: linear-gradient(to right, rgba(24, 144, 255, 0.08), transparent);
        border-radius: 8px 0 0 8px;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 24px;
          background: linear-gradient(45deg, #1890ff, #40a9ff, #69c0ff);
          border-radius: 4px;
          box-shadow: 0 0 12px rgba(24, 144, 255, 0.6);
        }

        &:not(:first-child) {
          margin-top: 20px;
        }
      }

      .form-row {
        display: flex;
        flex-wrap: wrap;
        gap: 28px;
        margin-bottom: 28px;
        position: relative;

        .ant-col {
          flex: 1;
          min-width: 300px;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-1px);
          }
        }

        @media (max-width: 768px) {
          flex-direction: column;
          .ant-col {
            width: 100%;
          }
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    :deep(.ant-form-item) {
      margin-bottom: 28px;

      &:last-child {
        margin-bottom: 0;
      }

      .ant-form-item-label {
        padding-bottom: 8px;

        > label {
          color: #666;
          font-weight: 500;
          font-size: 14px;
          transition: all 0.3s ease;
          position: relative;
          padding-left: 12px;

          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 14px;
            background: #e6e6e6;
            border-radius: 2px;
            transition: all 0.3s ease;
          }

          &:hover {
            color: #1890ff;
            &::before {
              background: #1890ff;
              height: 16px;
            }
          }
        }
      }

      &:hover .ant-form-item-label > label {
        color: #1890ff;
        &::before {
          background: #1890ff;
        }
      }
    }
  }

  .certificate-section {
    margin-bottom: 28px;
    padding: 24px;
    background: linear-gradient(to right, #f0f7ff, #f5faff);
    border-radius: 12px;
    border: 1px solid #e1ebfa;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.08);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(45deg, rgba(24, 144, 255, 0.03), transparent);
      z-index: 0;
    }

    &:hover {
      border-color: #bbd8fa;
      background: linear-gradient(to right, #f5faff, #ffffff);
      box-shadow: 0 8px 16px rgba(24, 144, 255, 0.15);
      transform: translateY(-3px);
    }

    &:last-child {
      margin-bottom: 0;
    }

    .certificate-title {
      font-size: 16px;
      font-weight: 500;
      color: #1890ff;
      margin-bottom: 24px;
      padding: 10px 20px;
      border-bottom: 1px dashed #d9e8f7;
      background: linear-gradient(to right, rgba(24, 144, 255, 0.1), transparent);
      border-radius: 6px;
      display: inline-block;
      position: relative;
      z-index: 1;

      &::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        width: 50%;
        height: 2px;
        background: linear-gradient(to right, #1890ff, transparent);
      }
    }

    .form-row {
      margin-bottom: 20px;
      position: relative;
      z-index: 1;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .empty-certificate {
    text-align: center;
    color: #999;
    padding: 48px 0;
    background: linear-gradient(to bottom, #f8fafd, #ffffff);
    border-radius: 12px;
    margin-bottom: 28px;
    font-size: 15px;
    border: 1px dashed #d9e3f0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(to right, #1890ff, #40a9ff, #69c0ff);
      opacity: 0.5;
      transition: all 0.3s ease;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(45deg, rgba(24, 144, 255, 0.02), transparent);
      z-index: 0;
    }

    &:hover {
      border-color: #1890ff;
      color: #666;
      box-shadow: 0 8px 16px rgba(24, 144, 255, 0.1);
      transform: translateY(-2px);

      &::before {
        opacity: 1;
        height: 4px;
      }
    }
  }
</style>
