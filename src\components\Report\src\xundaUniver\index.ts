import { App } from 'vue';
import XundaUniver from './Design/index.vue';
import XundaUniverPrint from './Print/Render/index.vue';

XundaUniver.install = (app: App) => app.component(XundaUniver.name as string, XundaUniver);
XundaUniverPrint.install = (app: App) => app.component(XundaUniverPrint.name as string, XundaUniver);

export { XundaUniver, XundaUniverPrint };

export default {
  install(app: App) {
    app.component(XundaUniver.name as string, XundaUniver);
    app.component(XundaUniverPrint.name as string, XundaUniverPrint);
  },
};
