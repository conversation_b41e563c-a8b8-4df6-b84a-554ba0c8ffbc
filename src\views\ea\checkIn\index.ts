import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';

// 基础Api
export const checkInApi = '/api/ea/checkIn';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: checkInApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: checkInApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: checkInApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: checkInApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: checkInApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: checkInApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: checkInApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: checkInApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: checkInApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: checkInApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: checkInApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: checkInApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: checkInApi + `/getForSelect`, data });
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('标识'),
    dataIndex: 'id',
    width: 120,
  },
  {
    title: t('课程'),
    dataIndex: 'courseName',
    width: 120,
  },
  {
    title: t('学生'),
    dataIndex: 'studentName',
    width: 120,
  },
  {
    title: t('状态'),
    dataIndex: 'state',
    width: 120,
  },
  {
    title: t('纬度'),
    dataIndex: 'latitude',
    width: 120,
  },
  {
    title: t('经度'),
    dataIndex: 'longtitude',
    width: 120,
  },
];
