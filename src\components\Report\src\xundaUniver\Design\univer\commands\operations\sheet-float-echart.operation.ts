import { type ICommand, type IAccessor, CommandType } from '@univerjs/core';
import { XundaSheetsFloatEchartService } from '../../services/sheet-float-echart.service';
import { XundaCommandIds } from '../../utils/define';

export const XundaSheetsInsertedFloatEchartOperation: ICommand = {
  id: XundaCommandIds.insertedFloatEchart,
  type: CommandType.OPERATION,
  handler: async (_: IAccessor) => {
    return true;
  },
};

export const XundaSheetsInsertFloatBarEchartOperation: ICommand = {
  id: XundaCommandIds.insertFloatBarEchart,
  type: CommandType.OPERATION,
  handler: async (accessor: IAccessor) => {
    const xundaSheetsFloatEchartService = accessor.get(XundaSheetsFloatEchartService);
    xundaSheetsFloatEchartService.insertFloatEchart('XundaUniverFloatBarEchart');

    return true;
  },
};

export const XundaSheetsInsertFloatLineEchartOperation: ICommand = {
  id: XundaCommandIds.insertFloatLineEchart,
  type: CommandType.OPERATION,
  handler: async (accessor: IAccessor) => {
    const xundaSheetsFloatEchartService = accessor.get(XundaSheetsFloatEchartService);
    xundaSheetsFloatEchartService.insertFloatEchart('XundaUniverFloatLineEchart');

    return true;
  },
};

export const XundaSheetsInsertFloatPieEchartOperation: ICommand = {
  id: XundaCommandIds.insertFloatPieEchart,
  type: CommandType.OPERATION,
  handler: async (accessor: IAccessor) => {
    const xundaSheetsFloatEchartService = accessor.get(XundaSheetsFloatEchartService);
    xundaSheetsFloatEchartService.insertFloatEchart('XundaUniverFloatPieEchart');

    return true;
  },
};

export const XundaSheetsInsertFloatRadarEchartOperation: ICommand = {
  id: XundaCommandIds.insertFloatRadarEchart,
  type: CommandType.OPERATION,
  handler: async (accessor: IAccessor) => {
    const xundaSheetsFloatEchartService = accessor.get(XundaSheetsFloatEchartService);
    xundaSheetsFloatEchartService.insertFloatEchart('XundaUniverFloatRadarEchart');

    return true;
  },
};

export const XundaSheetsFocusEchartOperation: ICommand = {
  id: XundaCommandIds.focusFloatEchart,
  type: CommandType.OPERATION,
  handler: async (_: IAccessor) => {
    return true;
  },
};
