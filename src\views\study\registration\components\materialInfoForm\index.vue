<template>
  <div class="material-info-form">
    <a-form :model="formState" :label-col="{ span: 6 }" :wrapper-col="{ span: 26 }" layout="vertical">
      <a-row :gutter="24">
        <!-- 具体表单 -->
        <a-col :span="24" :xs="24" :sm="24" :md="24" :lg="24" class="ant-col-item" :hidden="false">
          <div class="photo-item">
            <div class="photo-title">学历证书</div>
            <div class="photo-wrapper">
              <a-image
                v-if="formState.diploma"
                :src="apiUrl + formState.diploma.url"
                :preview="{
                  src: apiUrl + formState.diploma.url,
                  mask: '点击预览',
                }" />
              <div v-else class="empty-placeholder">
                <p>暂无学历证书照片</p>
              </div>
            </div>
          </div>
        </a-col>
        <a-col :span="24" :xs="24" :sm="24" :md="24" :lg="24" class="ant-col-item" :hidden="false">
          <a-form-item label="其他材料" name="otherMaterials">
            <!-- 第一中类型资料 -->
            <div class="file-list" v-if="formState.semesterCatogry === '0001' || formState.semesterCatogry === '0002'">
              <!-- TODO: 添加文件列表组件 -->
              <p> 身份证复印件 </p>
              <div class="pdf-viewer" v-if="formState.idCard && formState.idCard.length > 0">
                <pdf-viewer :file="formState.idCard[0]" :min-height="1200" :show-title="false" />
              </div>
              <div v-else class="no-upload-hint">
                <p>未上传身份证复印件</p>
              </div>
            </div>
            <div class="file-list" v-if="formState.semesterCatogry === '0003' || formState.semesterCatogry === '0004'">
              <div class="file-title">其他附件</div>
              <div class="file-items" v-if="formState.attachmentConfig && formState.attachmentConfig.length > 0">
                <div class="file-item" v-for="(item, index1) in formState.attachmentConfig" :key="index1">
                  <div class="file-label">{{ item.name }}:</div>
                  <template v-for="(file, index2) in formState.attachmentValue[item.field]" :key="index2">
                    <file-viewer :file="file" :previewMode="true" :show-title="false" />
                  </template>
                </div>
              </div>
            </div>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, ref } from 'vue';
  import { FileOutlined } from '@ant-design/icons-vue';
  import { getMaterialInfoAsync } from '.';
  import { useGlobSetting } from '@/hooks/setting';
  import pdfViewer from '@/views/study/registration/components/pdfViewer';
  import FileViewer from '@/components/Bit/fileViewer';

  const emit = defineEmits(['changeLoading', 'prev', 'next', 'update:auditState']);
  const changeLoading = loading => {
    emit('changeLoading', loading);
  };
  const globSetting = useGlobSetting();
  const apiUrl = ref(globSetting.apiUrl);
  const formState = reactive({
    idCardFront: <any>[],
    idCardReverse: <any>[],
    diploma: null as any,
    apply: <any>[],
    describe: <any>[],
    otherMaterials: [],
    semesterCatogry: '0001',
    attachmentConfig: <any>[],
    attachmentValue: <any>{},
    idCard: <any>[],
  });

  function init(data) {
    changeLoading(true);

    getMaterialInfoAsync(data.id)
      .then(res => {
        formState.semesterCatogry = res.data.semesterCatogry;
        formState.diploma = res.data.diploma;
        formState.apply = res.data.apply ? res.data.apply : [];
        formState.describe = res.data.describe ? res.data.describe : [];
        formState.idCard = res.data.idCard ? res.data.idCard : [];
        if (res.data.attachmentConfig) {
          formState.attachmentConfig = res.data.attachmentConfig;
        }
        if (res.data.attachmentValue) {
          formState.attachmentValue = res.data.attachmentValue;
        }
        console.log(formState);
        changeLoading(false);
      })
      .catch(err => {
        changeLoading(false);
        console.log(err);
      });
  }

  const handlePrev = () => {
    emit('prev');
  };

  const handleNext = () => {
    emit('next');
  };

  defineExpose({
    init,
  });
</script>

<style lang="less" scoped>
  .material-info-form {
    padding: 16px;
    background-color: #f5f7fa;
    display: flex;
    flex-direction: column;
    height: 100%;

    .pdf-viewer-wrapper {
      flex: 1;
      min-height: 0;
      margin: 20px 0;
      height: calc(100vh - 200px);

      :deep(.pdf-viewer-container) {
        height: 100%;
      }
    }

    .ant-col-item {
      margin-bottom: 24px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
      }
    }

    .photo-item {
      background-color: #fff;
      border-radius: 12px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      padding: 20px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      }

      .photo-title {
        font-size: 18px;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 20px;
        position: relative;
        padding-left: 12px;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 18px;
          background: linear-gradient(to bottom, #1890ff, #69c0ff);
          border-radius: 2px;
        }
      }

      .photo-wrapper {
        width: 100%;
        height: 300px;
        border: 1px solid #eaedf1;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        background: #fff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);

        &:hover {
          border-color: #1890ff;
          box-shadow: 0 4px 16px rgba(24, 144, 255, 0.12);
        }

        :deep(.ant-image) {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;

          img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            transition: transform 0.3s ease;

            &:hover {
              transform: scale(1.02);
            }
          }
        }

        .empty-placeholder {
          color: #8c9daa;
          font-size: 15px;
          text-align: center;
          width: 100%;

          p {
            margin-top: 12px;
            color: #8c9daa;
            opacity: 0.8;
          }

          :deep(.ant-empty) {
            margin: 8px 0;
          }
        }
      }
    }

    :deep(.ant-form-item-label) {
      label {
        font-size: 18px;
        font-weight: 600;
        color: #1e293b;
        position: relative;
        padding-left: 12px;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 18px;
          background: linear-gradient(to bottom, #1890ff, #69c0ff);
          border-radius: 2px;
        }
      }
    }

    .file-list {
      background-color: #fff;
      border-radius: 12px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      padding: 20px;
      margin-bottom: 16px;
      transition: all 0.3s ease;

      p {
        font-size: 16px;
        font-weight: 500;
        color: #1e293b;
        margin-bottom: 16px;
      }

      .file-title {
        font-size: 16px;
        font-weight: 500;
        color: #1e293b;
        margin-bottom: 16px;
      }

      .file-items {
        .file-item {
          margin-bottom: 16px;

          .file-label {
            font-weight: 500;
            margin-bottom: 8px;
            color: #4b5563;
          }

          .file-content {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;

            .file-tag {
              cursor: pointer;
              padding: 6px 12px;
              border-radius: 6px;
              font-size: 14px;
              background-color: #f0f7ff;
              color: #1890ff;
              border: 1px solid #d6e4ff;
              transition: all 0.3s ease;

              &:hover {
                background-color: #e6f7ff;
                transform: translateY(-2px);
                box-shadow: 0 2px 8px rgba(24, 144, 255, 0.12);
              }
            }
          }
        }

        .empty-files {
          padding: 20px 0;
        }
      }

      :deep(.xunda-upload-file) {
        .ant-btn {
          border-radius: 8px;
          height: 40px;
          font-weight: 500;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }
        }

        .ant-upload-list-item {
          transition: all 0.3s ease;
          border-radius: 6px;
          margin-bottom: 8px;

          &:hover {
            background-color: #f0f7ff;
          }
        }
      }
    }

    .action-buttons {
      display: flex;
      justify-content: center;
      margin-top: 32px;
      gap: 16px;

      .prev-btn,
      .next-btn {
        min-width: 120px;
        height: 40px;
        font-size: 16px;
        font-weight: 500;
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
      }

      .next-btn {
        background: linear-gradient(to right, #1890ff, #3ca0ff);
        border: none;

        &:hover {
          background: linear-gradient(to right, #0c7ad9, #2994ff);
        }
      }
    }
  }
  .no-upload-hint {
    text-align: center;
    font-weight: bold;
    color: #ff4d4f;
    padding: 15px;
    margin: 10px 0;
    background-color: #fff1f0;
    border: 1px solid #ffccc7;
    border-radius: 8px;
  }
</style>
