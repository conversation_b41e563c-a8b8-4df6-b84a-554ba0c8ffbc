<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    width="600px"
    :minHeight="100"
    :cancelText="t('common.cancelText', '取消')"
    :okText="t('common.okText', '确定')"
    @ok="handleSubmit"
    :closeFunc="onClose">
    <template #title>
      <a-space :size="10">
        <div class="text-16px font-medium">{{ title }}</div>
        <a-space-compact size="small" block v-if="dataForm.id">
          <a-tooltip :title="t('common.prevRecord')">
            <a-button size="small" :disabled="getPrevDisabled" @click="handlePrev">
              <i class="icon-ym icon-ym-caret-left text-10px"></i>
            </a-button>
          </a-tooltip>
          <a-tooltip :title="t('common.nextRecord')">
            <a-button size="small" :disabled="getNextDisabled" @click="handleNext">
              <i class="icon-ym icon-ym-caret-right text-10px"></i>
            </a-button>
          </a-tooltip>
        </a-space-compact>
      </a-space>
    </template>
    <template #insertFooter>
      <div class="loat-left mt-5px">
        <XundaCheckboxSingle v-model:value="submitType" :label="continueText" />
      </div>
    </template>
    <a-row class="dynamic-form">
      <a-form
        :colon="false"
        size="middle"
        layout="horizontal"
        labelAlign="left"
        :labelCol="{ style: { width: '100px' } }"
        :model="dataForm"
        :rules="dataRule"
        ref="formRef">
        <a-row :gutter="15">
          <!-- 具体表单 -->
          <a-col :span="24" class="ant-col-item" :hidden="hiddenCourseId">
            <a-form-item name="courseId">
              <template #label>课程 </template>
              <XundaSelect
                v-model:value="dataForm.courseId"
                :disabled="false"
                @change="changeData('courseId', -1)"
                placeholder="请选择课程"
                :allowClear="true"
                :style="{ width: '100%' }"
                showSearch
                :options="optionsObj.courseIdOptions"
                :fieldNames="optionsObj.courseIdFieldNames">
              </XundaSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="parentId">
              <template #label>父级 </template>
              <XundaTreeSelect
                v-model:value="dataForm.parentId"
                @change="changeData('parentId', -1)"
                placeholder="请选择"
                :templateJson="state.interfaceRes.parentId"
                :allowClear="true"
                :style="{ width: '100%' }"
                :showSearch="false"
                :options="optionsObj.parentIdOptions"
                :fieldNames="optionsObj.parentIdFieldNames">
              </XundaTreeSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="displayOrder">
              <template #label>显示顺序 </template>
              <XundaInputNumber
                v-model:value="dataForm.displayOrder"
                :disabled="false"
                @change="changeData('displayOrder', -1)"
                placeholder="请输入显示顺序"
                :allowClear="true"
                :style="{ width: '100%' }"
                :precision="2"
                :controls="true">
              </XundaInputNumber>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="viewRole">
              <template #label>公开模式 </template>
              <XundaInput
                v-model:value="dataForm.viewRole"
                :disabled="false"
                @change="changeData('viewRole', -1)"
                placeholder="请输入公开模式"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="leafFlag">
              <template #label>叶子节点 </template>
              <XundaSwitch v-model:value="dataForm.leafFlag" :disabled="false" @change="changeData('leafFlag', -1)" placeholder="叶子节点"> </XundaSwitch>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="attachment">
              <template #label>附件 </template>
              <XundaUploadFile
                v-model:value="dataForm.attachment"
                @change="changeData('attachment', -1)"
                :fileSize="10"
                sizeUnit="MB"
                :limit="9"
                pathType="selfPath"
                :sortRule="[3]"
                timeFormat="YYYY"
                folder="courseResource/attachment"
                buttonText="点击上传" />
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="description">
              <template #label>描述 </template>
              <XundaTextarea
                v-model:value="dataForm.description"
                :disabled="false"
                @change="changeData('description', -1)"
                placeholder="描述"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaTextarea>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="viewCount">
              <template #label>访问人数 </template>
              <XundaInputNumber
                v-model:value="dataForm.viewCount"
                :disabled="false"
                @change="changeData('viewCount', -1)"
                placeholder="访问人数"
                :allowClear="true"
                :style="{ width: '100%' }"
                :precision="2"
                :controls="true">
              </XundaInputNumber>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="downloadCount">
              <template #label>下载次数 </template>
              <XundaInputNumber
                v-model:value="dataForm.downloadCount"
                :disabled="false"
                @change="changeData('downloadCount', -1)"
                placeholder="下载次数"
                :allowClear="true"
                :style="{ width: '100%' }"
                :precision="2"
                :controls="true">
              </XundaInputNumber>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="size">
              <template #label>大小 </template>
              <XundaInputNumber
                v-model:value="dataForm.size"
                :disabled="false"
                @change="changeData('size', -1)"
                placeholder="大小"
                :allowClear="true"
                :style="{ width: '100%' }"
                :precision="2"
                :controls="true">
              </XundaInputNumber>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="creator">
              <template #label>创建人 </template>
              <XundaInput
                v-model:value="dataForm.creator"
                :disabled="false"
                @change="changeData('creator', -1)"
                placeholder="创建人"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { create, update, getInfo, getTree } from '@/views/ea/courseResource';
  import { reactive, toRefs, nextTick, ref, unref, computed, inject } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useUserStore } from '@/store/modules/user';
  import type { FormInstance } from 'ant-design-vue';
  import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
  // 表单权限
  import { usePermission } from '@/hooks/web/usePermission';
  interface State {
    dataForm: any;
    tableRows: any;
    dataRule: any;
    optionsObj: any;
    childIndex: any;
    isEdit: any;
    interfaceRes: any;
    //可选范围默认值
    ableAll: any;
    //掩码配置
    maskConfig: any;
    //定位属性
    locationScope: any;
    title: string;
    continueText: string;
    allList: any[];
    currIndex: number;
    isContinue: boolean;
    submitType: number;
    showContinueBtn: boolean;
  }

  const emit = defineEmits(['reload']);
  const getLeftTreeActiveInfo: (() => any) | null = inject('getLeftTreeActiveInfo', null);
  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const [registerModal, { openModal, setModalProps }] = useModal();
  const formRef = ref<FormInstance>();
  const state = reactive<State>({
    dataForm: {
      fId: '',
      courseId: '',
      parentId: '',
      displayOrder: undefined,
      viewRole: '',
      leafFlag: undefined,
      attachment: '',
      description: '',
      viewCount: undefined,
      downloadCount: undefined,
      size: undefined,
      creator: '',
      fCreatorTime: undefined,
      fCreatorUserId: '',
      fLastModifyTime: undefined,
      fLastModifyUserId: '',
      fDeleteTime: undefined,
      fDeleteUserId: '',
      fDeleteMark: undefined,
      fTenantId: '',
    },
    tableRows: {},
    dataRule: {
      fId: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '主键不能为空'),
          trigger: 'blur',
        },
      ],
      courseId: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '课程不能为空'),
          trigger: 'blur',
        },
      ],
      displayOrder: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '显示顺序不能为空'),
          trigger: 'blur',
        },
      ],
      viewRole: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '公开模式不能为空'),
          trigger: 'blur',
        },
      ],
      fTenantId: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '租户不能为空'),
          trigger: 'blur',
        },
      ],
    },
    optionsObj: {
      //选项配置
      defaultProps: { label: 'fullName', value: 'enCode' }, // 默认下拉选择键值
    },
    childIndex: -1,
    isEdit: false,
    interfaceRes: {},
    //可选范围默认值
    ableAll: {},

    //掩码配置
    maskConfig: {},
    //定位属性
    locationScope: {
      faddressDetail: [],
    },
    title: '',
    continueText: '',
    allList: [],
    currIndex: 0,
    isContinue: false,
    submitType: 0,
    showContinueBtn: true,
  });
  const { title, continueText, showContinueBtn, dataRule, dataForm, optionsObj, ableAll, maskConfig, submitType } = toRefs(state);

  const getPrevDisabled = computed(() => state.currIndex === 0);
  const getNextDisabled = computed(() => state.currIndex === state.allList.length - 1);
  // 表单权限
  const { hasFormP } = usePermission();
  const courseId = ref('');
  const hiddenCourseId = ref(false);

  defineExpose({ init });

  function init(data) {
    state.submitType = 0;
    state.isContinue = false;
    state.title = !data.id ? t('common.add2Text', '新增') : t('common.editText', '编辑');
    state.continueText = !data.id ? t('common.continueAndAddText', '确定并新增') : t('common.continueText', '确定并继续');
    setFormProps({ continueLoading: false });
    state.dataForm.id = data.id;
    courseId.value = data.courseId;
    hiddenCourseId.value = data.courseId ? true : false;
    openModal();
    state.allList = data.allList;
    state.currIndex = state.allList.length && data.id ? state.allList.findIndex(item => item.id === data.id) : 0;
    console.log('state.currIndex', data, courseId.value);
    nextTick(() => {
      getAllSelectOptions();
      getForm().resetFields();
      setTimeout(initData, 0);
    });
  }
  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      // 设置默认值
      state.dataForm = {
        id: '',
        courseId: courseId.value,
        parentId: '',
        displayOrder: undefined,
        viewRole: '',
        leafFlag: undefined,
        attachment: '',
        description: '',
        viewCount: undefined,
        downloadCount: undefined,
        size: undefined,
        creator: '',
      };

      if (getLeftTreeActiveInfo) state.dataForm = { ...state.dataForm, ...(getLeftTreeActiveInfo() || {}) };
      state.childIndex = -1;
      changeLoading(false);
    }
  }
  function getForm() {
    const form = unref(formRef);
    if (!form) {
      throw new Error('form is null!');
    }
    return form;
  }
  function getData(id) {
    getInfo(id).then(res => {
      state.dataForm = res.data || {};
      state.dataForm.attachment = res.data.attachment ? JSON.parse(res.data.attachment) : [];
      state.childIndex = -1;
      changeLoading(false);
    });
  }
  async function handleSubmit(type) {
    try {
      const values = await getForm()?.validate();
      if (!values) return;
      setFormProps({ confirmLoading: true });
      const formMethod = state.dataForm.id ? update : create;
      state.dataForm.attachment = JSON.stringify(state.dataForm.attachment);
      console.log('values', state.dataForm);
      formMethod(state.dataForm)
        .then(res => {
          createMessage.success(res.msg);
          setFormProps({ confirmLoading: false });
          if (state.submitType == 1) {
            initData();
            state.isContinue = true;
          } else {
            setFormProps({ open: false });
            emit('reload');
          }
        })
        .catch(() => {
          setFormProps({ confirmLoading: false });
        });
    } catch (_) {}
  }

  // 跳转上一个
  function handlePrev() {
    state.currIndex--;
    handleGetNewInfo();
  }

  // 跳转下一个
  function handleNext() {
    state.currIndex++;
    handleGetNewInfo();
  }

  // 重新获取编辑信息
  function handleGetNewInfo() {
    changeLoading(true);
    getForm().resetFields();
    const id = state.allList[state.currIndex].id;
    getData(id);
  }

  function setFormProps(data) {
    setModalProps(data);
  }

  //
  function changeLoading(loading) {
    setModalProps({ loading });
  }

  // 关闭弹窗
  async function onClose() {
    if (state.isContinue) emit('reload');
    return true;
  }

  function changeData(model, index) {
    state.isEdit = false;
    state.childIndex = index;
    for (let key in state.interfaceRes) {
      if (key != model) {
        let faceReList = state.interfaceRes[key];
        for (let i = 0; i < faceReList.length; i++) {
          let relationField = faceReList[i].relationField;
          if (relationField) {
            let modelAll = relationField.split('-');
            let faceMode = '';
            let faceMode2 = modelAll.length == 2 ? modelAll[0].substring(0, modelAll[0].length - 4) + modelAll[1] : '';
            for (let i = 0; i < modelAll.length; i++) {
              faceMode += modelAll[i];
            }
            if (faceMode == model || faceMode2 == model) {
              let options = 'get' + key + 'Options';
              eval(options)(true);
              changeData(key, index);
            }
          }
        }
      }
    }
  }
  function getAllSelectOptions() {
    state.optionsObj.courseIdFieldNames = { label: 'name', value: 'id' };
    state.optionsObj.courseIdOptions = [];
    state.optionsObj.parentIdFieldNames = { label: 'id', value: 'id' };
    console.log('state.dataForm', courseId.value);
    getTree({
      moduleId: '***********', //模块ID
      superQueryJson: '',
      courseId: courseId.value,
      dataType: 0,
    }).then(res => {
      state.optionsObj.parentIdOptions = res.data;
    });
  }
</script>
