export default [
  {
    options: {
      left: 235.5,
      top: 18,
      height: 33,
      width: 120,
      title: '采购合同',
      coordinateSync: false,
      widthHeightSync: false,
      fontSize: 21,
      fontWeight: 'bold',
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      right: 353.99571990966797,
      bottom: 42.743614196777344,
      vCenter: 293.99571990966797,
      hCenter: 26.243614196777344,
      fontFamily: 'SimSun',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 46.5,
      top: 72,
      height: 16.5,
      width: 493.5,
      title: '甲方（需方）：',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 257.99146270751953,
      bottom: 65.24574279785156,
      vCenter: 151.49146270751953,
      hCenter: 56.99574279785156,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 46.5,
      top: 105,
      height: 16.5,
      width: 495,
      title: '乙方（供方）：',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 258.2428894042969,
      bottom: 85.74715805053711,
      vCenter: 151.74288940429688,
      hCenter: 77.49715805053711,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 46.5,
      top: 130.5,
      height: 54,
      width: 495,
      title:
        '     根据(中华人民共和国合同法》、《中华人民共和国政府采购法》等相关法律法规的规定，甲乙双方协商一致，签订本合同。\n一、设备名称、规格质量、数量及金额（含运输、装卸等相关费用）\n',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      lineHeight: 18,
      longTextIndent: 22.5,
      right: 543,
      bottom: 195,
      vCenter: 294.75,
      hCenter: 162.75,
    },
    printElementType: { title: '长文本', type: 'longText' },
  },
  {
    options: {
      left: 178.5,
      top: 184.5,
      height: 32,
      width: 102,
      title: '规格及质量要求',
      right: 114.75,
      bottom: 224,
      vCenter: 81.75,
      hCenter: 208,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 279,
      top: 184.5,
      height: 32,
      width: 66,
      title: '单位',
      right: 344.25,
      bottom: 224,
      vCenter: 311.25,
      hCenter: 208,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 343.5,
      top: 184.5,
      height: 32,
      width: 66,
      title: '数量',
      right: 177.24609375,
      bottom: 222.74609375,
      vCenter: 144.24609375,
      hCenter: 206.74609375,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 409.5,
      top: 184.5,
      height: 32,
      width: 66,
      title: '单价/元',
      right: 477,
      bottom: 223.25,
      vCenter: 444,
      hCenter: 207.25,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 112.5,
      top: 184.5,
      height: 32,
      width: 66,
      title: '名称',
      right: 175.5000228881836,
      bottom: 216.49219512939453,
      vCenter: 142.5000228881836,
      hCenter: 200.49219512939453,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 475.5,
      top: 184.5,
      height: 32,
      width: 66,
      title: '小计（元）',
      right: 541.5,
      bottom: 224,
      vCenter: 508.5,
      hCenter: 208,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 46.5,
      top: 184.5,
      height: 32,
      width: 67.5,
      title: '序号',
      right: 113.9974136352539,
      bottom: 216.49219512939453,
      vCenter: 80.2474136352539,
      hCenter: 200.49219512939453,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 343.5,
      top: 216,
      height: 32,
      width: 66,
      title: ' ',
      right: 411,
      bottom: 248,
      vCenter: 378,
      hCenter: 232,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 279,
      top: 216,
      height: 32,
      width: 66,
      title: ' ',
      right: 344.25,
      bottom: 224,
      vCenter: 311.25,
      hCenter: 208,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 178.5,
      top: 216,
      height: 32,
      width: 102,
      title: ' ',
      right: 280.5,
      bottom: 248,
      vCenter: 229.5,
      hCenter: 232,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 409.5,
      top: 216,
      height: 32,
      width: 66,
      title: ' ',
      right: 477,
      bottom: 223.25,
      vCenter: 444,
      hCenter: 207.25,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 112.5,
      top: 216,
      height: 32,
      width: 66,
      title: ' ',
      right: 178.5,
      bottom: 248,
      vCenter: 145.5,
      hCenter: 232,
      coordinateSync: false,
      widthHeightSync: false,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 475.5,
      top: 216,
      height: 32,
      width: 66,
      title: ' ',
      right: 543,
      bottom: 248,
      vCenter: 510,
      hCenter: 232,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 46.5,
      top: 216,
      height: 32,
      width: 67.5,
      title: '1',
      right: 114.75,
      bottom: 224,
      vCenter: 81.75,
      hCenter: 208,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 279,
      top: 247.5,
      height: 32,
      width: 66,
      title: ' ',
      right: 345,
      bottom: 279.5,
      vCenter: 312,
      hCenter: 263.5,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 343.5,
      top: 247.5,
      height: 32,
      width: 66,
      title: ' ',
      right: 411,
      bottom: 279.5,
      vCenter: 378,
      hCenter: 263.5,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 178.5,
      top: 247.5,
      height: 32,
      width: 102,
      title: ' ',
      right: 280.5,
      bottom: 279.5,
      vCenter: 229.5,
      hCenter: 263.5,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 409.5,
      top: 247.5,
      height: 32,
      width: 66,
      title: ' ',
      right: 477,
      bottom: 279.5,
      vCenter: 444,
      hCenter: 263.5,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 112.5,
      top: 247.5,
      height: 32,
      width: 66,
      title: ' ',
      right: 177.24609375,
      bottom: 222.74609375,
      vCenter: 144.24609375,
      hCenter: 206.74609375,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 475.5,
      top: 247.5,
      height: 32,
      width: 66,
      title: ' ',
      right: 543,
      bottom: 279.5,
      vCenter: 510,
      hCenter: 263.5,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 46.5,
      top: 247.5,
      height: 32,
      width: 67.5,
      title: '2',
      right: 114.75,
      bottom: 224,
      vCenter: 81.75,
      hCenter: 208,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 279,
      top: 279,
      height: 32,
      width: 66,
      title: ' ',
      right: 345,
      bottom: 311,
      vCenter: 312,
      hCenter: 295,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 343.5,
      top: 279,
      height: 32,
      width: 66,
      title: ' ',
      right: 411,
      bottom: 311,
      vCenter: 378,
      hCenter: 295,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 178.5,
      top: 279,
      height: 32,
      width: 102,
      title: ' ',
      right: 280.5,
      bottom: 311,
      vCenter: 229.5,
      hCenter: 295,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 409.5,
      top: 279,
      height: 32,
      width: 66,
      title: ' ',
      right: 477,
      bottom: 311,
      vCenter: 444,
      hCenter: 295,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 112.5,
      top: 279,
      height: 32,
      width: 66,
      title: ' ',
      right: 178.5,
      bottom: 311,
      vCenter: 145.5,
      hCenter: 295,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 475.5,
      top: 279,
      height: 32,
      width: 66,
      title: ' ',
      right: 543,
      bottom: 311,
      vCenter: 510,
      hCenter: 295,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 46.5,
      top: 279,
      height: 32,
      width: 67.5,
      title: '3',
      right: 114.75,
      bottom: 224,
      vCenter: 81.75,
      hCenter: 208,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 279,
      top: 310.5,
      height: 32,
      width: 66,
      title: ' ',
      right: 345,
      bottom: 342.5,
      vCenter: 312,
      hCenter: 326.5,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 343.5,
      top: 310.5,
      height: 32,
      width: 66,
      title: ' ',
      right: 411,
      bottom: 342.5,
      vCenter: 378,
      hCenter: 326.5,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 178.5,
      top: 310.5,
      height: 32,
      width: 102,
      title: ' ',
      right: 280.5,
      bottom: 342.5,
      vCenter: 229.5,
      hCenter: 326.5,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 409.5,
      top: 310.5,
      height: 32,
      width: 66,
      title: ' ',
      right: 477,
      bottom: 342.5,
      vCenter: 444,
      hCenter: 326.5,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 112.5,
      top: 310.5,
      height: 32,
      width: 66,
      title: ' ',
      right: 178.5,
      bottom: 342.5,
      vCenter: 145.5,
      hCenter: 326.5,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 475.5,
      top: 310.5,
      height: 32,
      width: 66,
      title: ' ',
      right: 543,
      bottom: 342.5,
      vCenter: 510,
      hCenter: 326.5,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 46.5,
      top: 310.5,
      height: 32,
      width: 67.5,
      title: '4',
      right: 112.5,
      bottom: 342.5,
      vCenter: 79.5,
      hCenter: 326.5,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 279,
      top: 342,
      height: 32,
      width: 66,
      title: ' ',
      right: 345,
      bottom: 374,
      vCenter: 312,
      hCenter: 358,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 343.5,
      top: 342,
      height: 32,
      width: 66,
      title: ' ',
      right: 411,
      bottom: 374,
      vCenter: 378,
      hCenter: 358,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 178.5,
      top: 342,
      height: 32,
      width: 102,
      title: ' ',
      right: 280.5,
      bottom: 374,
      vCenter: 229.5,
      hCenter: 358,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 409.5,
      top: 342,
      height: 32,
      width: 66,
      title: ' ',
      right: 477,
      bottom: 374,
      vCenter: 444,
      hCenter: 358,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 112.5,
      top: 342,
      height: 32,
      width: 66,
      title: ' ',
      right: 178.5,
      bottom: 374,
      vCenter: 145.5,
      hCenter: 358,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 475.5,
      top: 342,
      height: 32,
      width: 66,
      title: ' ',
      right: 543,
      bottom: 374,
      vCenter: 510,
      hCenter: 358,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 46.5,
      top: 342,
      height: 32,
      width: 67.5,
      title: '5',
      right: 112.5,
      bottom: 374,
      vCenter: 79.5,
      hCenter: 358,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 279,
      top: 373.5,
      height: 32,
      width: 66,
      title: ' ',
      right: 345,
      bottom: 405.5,
      vCenter: 312,
      hCenter: 389.5,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 343.5,
      top: 373.5,
      height: 32,
      width: 66,
      title: ' ',
      right: 411,
      bottom: 405.5,
      vCenter: 378,
      hCenter: 389.5,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 178.5,
      top: 373.5,
      height: 32,
      width: 102,
      title: ' ',
      right: 280.5,
      bottom: 405.5,
      vCenter: 229.5,
      hCenter: 389.5,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 409.5,
      top: 373.5,
      height: 32,
      width: 66,
      title: ' ',
      right: 477,
      bottom: 405.5,
      vCenter: 444,
      hCenter: 389.5,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 112.5,
      top: 373.5,
      height: 32,
      width: 66,
      title: ' ',
      right: 178.5,
      bottom: 405.5,
      vCenter: 145.5,
      hCenter: 389.5,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 475.5,
      top: 373.5,
      height: 32,
      width: 66,
      title: ' ',
      right: 543,
      bottom: 405.5,
      vCenter: 510,
      hCenter: 389.5,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 46.5,
      top: 373.5,
      height: 32,
      width: 67.5,
      title: '6',
      right: 112.5,
      bottom: 405.5,
      vCenter: 79.5,
      hCenter: 389.5,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 178.5,
      top: 405,
      height: 32,
      width: 102,
      title: ' ',
      right: 280.5,
      bottom: 437,
      vCenter: 229.5,
      hCenter: 421,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 279,
      top: 405,
      height: 32,
      width: 66,
      title: ' ',
      right: 345,
      bottom: 437,
      vCenter: 312,
      hCenter: 421,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 343.5,
      top: 405,
      height: 32,
      width: 66,
      title: ' ',
      right: 411,
      bottom: 437,
      vCenter: 378,
      hCenter: 421,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 409.5,
      top: 405,
      height: 32,
      width: 66,
      title: ' ',
      right: 477,
      bottom: 437,
      vCenter: 444,
      hCenter: 421,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 112.5,
      top: 405,
      height: 32,
      width: 66,
      title: ' ',
      right: 178.5,
      bottom: 437,
      vCenter: 145.5,
      hCenter: 421,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 475.5,
      top: 405,
      height: 32,
      width: 66,
      title: ' ',
      right: 541.4974136352539,
      bottom: 437,
      vCenter: 508.4974136352539,
      hCenter: 421,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 46.5,
      top: 405,
      height: 32,
      width: 67.5,
      title: '7',
      right: 112.5,
      bottom: 437,
      vCenter: 79.5,
      hCenter: 421,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 112.5,
      top: 436.5,
      height: 32,
      width: 231,
      title: '（小写）',
      right: 180.75,
      bottom: 468.5,
      vCenter: 147.75,
      hCenter: 452.5,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderBottom: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 343.5,
      top: 436.5,
      height: 32,
      width: 198,
      title: '（大写）',
      right: 539.5000228881836,
      bottom: 467.7473907470703,
      vCenter: 441.5000228881836,
      hCenter: 451.7473907470703,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 46.5,
      top: 436.5,
      height: 32,
      width: 67.5,
      title: '合计',
      right: 113.9974136352539,
      bottom: 468.4973907470703,
      vCenter: 80.2474136352539,
      hCenter: 452.4973907470703,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderBottom: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 46.5,
      top: 481.5,
      height: 135,
      width: 495,
      title:
        '二、交货。时间、地点、方式：\n三、验收。交货时由需方验收，验收标准：\n四、付款。验收合格后，一次性付清款项。\n五、违规责任：\n六、因合同执行而产生问题的解决方式。1、当事人双方协商解决；2、向政府采购监督部门投诉；3、提请仲裁；4、向人民法院起诉。\n本合同一式三份，供方、需方、政府采购监督部门各执一份，具有同等法律效力。\n',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      lineHeight: 18,
      longTextIndent: 22.5,
      right: 543.24609375,
      bottom: 524.49609375,
      vCenter: 294.99609375,
      hCenter: 497.49609375,
    },
    printElementType: { title: '长文本', type: 'longText' },
  },
  {
    options: {
      left: 225,
      top: 486,
      height: 11.25,
      width: 315,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      hideTitle: true,
      borderBottom: 'solid',
      right: 345.75,
      bottom: 496.5,
      vCenter: 285.75,
      hCenter: 490.875,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 280.5,
      top: 502.5,
      height: 11.25,
      width: 261,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      hideTitle: true,
      borderBottom: 'solid',
      right: 402.24609375,
      bottom: 513.99609375,
      vCenter: 342.24609375,
      hCenter: 508.37109375,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 147,
      top: 538.5,
      height: 11.25,
      width: 394.5,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      hideTitle: true,
      borderBottom: 'solid',
      right: 405.24609375,
      bottom: 549.99609375,
      vCenter: 276.24609375,
      hCenter: 544.37109375,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 327,
      top: 649.5,
      height: 16.5,
      width: 213,
      title: '需方：（盖章）',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 540.24609375,
      bottom: 702.99609375,
      vCenter: 433.74609375,
      hCenter: 694.74609375,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 46.5,
      top: 649.5,
      height: 16.5,
      width: 213,
      title: '供方：（盖章）',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 258.75,
      bottom: 702,
      vCenter: 152.25,
      hCenter: 693.75,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 327,
      top: 682.5,
      height: 16.5,
      width: 211.5,
      title: '法人代表：（签名）',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 539.9921951293945,
      bottom: 686.9948272705078,
      vCenter: 433.49219512939453,
      hCenter: 678.7448272705078,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 46.5,
      top: 682.5,
      height: 16.5,
      width: 213,
      title: '法人代表：（签名）',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 260.99146270751953,
      bottom: 639.7435684204102,
      vCenter: 154.49146270751953,
      hCenter: 631.4935684204102,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 46.5,
      top: 714,
      height: 16.5,
      width: 213,
      title: '日期：',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 256.74609375,
      bottom: 766.74609375,
      vCenter: 150.24609375,
      hCenter: 758.49609375,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 327,
      top: 714,
      height: 16.5,
      width: 211.5,
      title: '日期：',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 540,
      bottom: 767.49609375,
      vCenter: 433.5,
      hCenter: 759.24609375,
    },
    printElementType: { title: '文本', type: 'text' },
  },
];
