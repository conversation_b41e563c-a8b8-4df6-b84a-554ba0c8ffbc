import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';

// 基础Api
export const accommodationApi = '/api/study/accommodation';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: accommodationApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: accommodationApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: accommodationApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: accommodationApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: accommodationApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: accommodationApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: accommodationApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: accommodationApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: accommodationApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: accommodationApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: accommodationApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: accommodationApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: accommodationApi + `/getForSelect`, data });
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('标识'),
    dataIndex: 'id',
    width: 120,
  },
  {
    title: t('报名信息'),
    dataIndex: 'registrationId',
    width: 120,
  },
  {
    title: t('住宿'),
    dataIndex: 'accommodationId',
    width: 120,
  },
  {
    title: t('是否吸烟'),
    dataIndex: 'smokingFlag',
    width: 120,
    customRender: xundaUtils.customRenderBoolean,
  },
  {
    title: t('作息时间'),
    dataIndex: 'sleepCycle',
    width: 120,
  },
  {
    title: t('爱好'),
    dataIndex: 'hobbie',
    width: 120,
  },
  {
    title: t('币种'),
    dataIndex: 'currency',
    width: 120,
  },
  {
    title: t('支付记录'),
    dataIndex: 'paymentId',
    width: 120,
  },
  {
    title: t('支付状态'),
    dataIndex: 'payState',
    width: 120,
  },
  {
    title: t('原价'),
    dataIndex: 'originalPrice',
    width: 120,
  },
  {
    title: t('实价'),
    dataIndex: 'realPrice',
    width: 120,
  },
  {
    title: t('费用调整流程实例'),
    dataIndex: 'flowTaskId',
    width: 120,
  },
  {
    title: t('费用调整流程'),
    dataIndex: 'flowId',
    width: 120,
  },
];
