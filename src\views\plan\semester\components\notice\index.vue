<template>
  <a-row class="dynamic-form">
    <a-form
      :colon="false"
      size="middle"
      layout="horizontal"
      labelAlign="left"
      :labelCol="{ style: { width: '80px' } }"
      :model="dataForm"
      :rules="dataRule"
      ref="formRef">
      <a-row :gutter="15">
        <!-- 生成通知按钮 -->
        <a-col :span="24" class="ant-col-item" :hidden="disabled">
          <div class="flex justify-start">
            <a-button type="primary" :disabled="disabled" @click="generateNoticeFromData" class="generate-notice-btn">
              <template #icon><FileAddOutlined /></template>
              从基础数据生成通知
            </a-button>
          </div>
        </a-col>
        <!-- 具体表单 -->
        <a-col :span="24" class="ant-col-item" :hidden="false">
          <a-form-item name="title">
            <template #label>标题 </template>
            <XundaInput
              v-if="!disabled"
              v-model:value="dataForm.title"
              :disabled="false"
              placeholder="请输入"
              :allowClear="true"
              :style="{ width: '100%' }"
              :readOnly="disabled">
            </XundaInput>
            <div v-else>{{ dataForm.title }}</div>
          </a-form-item>
        </a-col>
        <a-col :span="24" class="ant-col-item" :hidden="false">
          <a-form-item name="content">
            <template #label>内容 </template>
            <XundaEditor v-if="!disabled" v-model:value="dataForm.content" />
            <div v-else v-html="dataForm.content"></div>
          </a-form-item>
        </a-col>
        <a-col :span="24" class="ant-col-item" :hidden="false">
          <a-form-item name="attachment">
            <template #label>附件 </template>
            <XundaUploadFile
              v-if="!disabled"
              v-model:value="dataForm.attachment"
              :fileSize="10"
              sizeUnit="MB"
              :limit="9"
              pathType="selfPath"
              :sortRule="[3]"
              timeFormat="YYYY"
              folder="fileFiled"
              buttonText="点击上传"
              :showUploadList="false"
              :showDownload="false"
              :detailed="disabled"
              :disabled="disabled">
            </XundaUploadFile>
          </a-form-item>
        </a-col>
        <!-- 表单结束 -->
      </a-row>
    </a-form>
  </a-row>

  <div class="file-list-container">
    <div v-if="fileList && fileList.length > 0" class="file-list">
      <div v-for="(file, index) in fileList" :key="index" class="file-item">
        <Bit_KKFileView :file="file" operationMode="icon" :previewMode="false" :allowDelete="!disabled" @delete="handleDeleteFile" />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { create, update, getInfo, getSemesterInfoAsync } from './index';
  import { reactive, toRefs, nextTick, ref, unref, computed, inject } from 'vue';
  import { FileAddOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { formatToDate } from '@/utils/dateUtil';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useUserStore } from '@/store/modules/user';
  import type { FormInstance } from 'ant-design-vue';
  import Bit_KKFileView from '@/components/Bit/kkFileView';

  interface State {
    dataForm: any;
    tableRows: any;
    dataRule: any;
    optionsObj: any;
    childIndex: any;
    isEdit: any;
    interfaceRes: any;
    //可选范围默认值
    ableAll: any;
    //掩码配置
    maskConfig: any;
    //定位属性
    locationScope: any;
    title: string;
    continueText: string;
    allList: any[];
    currIndex: number;
    isContinue: boolean;
    submitType: number;
    showContinueBtn: boolean;
  }

  const props = defineProps({
    semesterId: { type: String, default: '' },
    disabled: { type: Boolean, default: false },
  });
  // 定义组件的 emits
  const emit = defineEmits(['changeLoading', 'handleStep']);
  const changeLoading = loading => {
    emit('changeLoading', loading);
  };

  const getLeftTreeActiveInfo: (() => any) | null = inject('getLeftTreeActiveInfo', null);
  const userStore = useUserStore();
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const formRef = ref<FormInstance>();
  function getForm() {
    const form = unref(formRef);
    if (!form) {
      throw new Error('form is null!');
    }
    return form;
  }
  const state = reactive<State>({
    dataForm: {
      id: undefined,
      semesterId: undefined,
      title: '',
      content: '',
      attachment: '',
    },
    tableRows: {},
    dataRule: {
      title: [
        {
          required: 'true',
        },
      ],
      content: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '不能为空'),
          trigger: 'blur',
        },
      ],
    },
    optionsObj: {
      //选项配置
      semesterIdOptions: [],
      semesterIdProps: {
        label: 'name',
        value: 'id',
      },
      defaultProps: { label: 'fullName', value: 'enCode' }, // 默认下拉选择键值
    },
    childIndex: -1,
    isEdit: false,
    interfaceRes: {
      id: [],
      semesterId: [],
      title: [],
      content: [],
      attachment: [],
    },
    //可选范围默认值
    ableAll: {},

    //掩码配置
    maskConfig: {},
    //定位属性
    locationScope: {
      faddressDetail: [],
    },
    title: '',
    continueText: '',
    allList: [],
    currIndex: 0,
    isContinue: false,
    submitType: 0,
    showContinueBtn: true,
  });
  const { dataRule, dataForm } = toRefs(state);
  const fileList = computed(() => {
    return dataForm.value.attachment;
  });

  /**
   * 从基础数据生成通知内容
   */
  function generateNoticeFromData() {
    const { createConfirm, createMessage } = useMessage();

    createConfirm({
      iconType: 'warning',
      title: '确认生成通知',
      content: '此操作将根据学期基础数据生成通知内容，可能会覆盖当前已有内容，是否继续？',
      onOk: async () => {
        try {
          changeLoading(true);
          // 这里可以调用后端API获取基础数据并生成通知
          // 示例：生成通知标题和内容
          const semesterId = props.semesterId;
          getSemesterInfoAsync(semesterId).then(res => {
            // 模拟数据，实际应该从API获取
            const semesterInfo = res.data;
            // 生成标题
            state.dataForm.title = `关于${semesterInfo.name}计划发布相关事项的通知`;

            // 生成通知内容
            state.dataForm.content = `
            <h2 style="text-align: center;">关于${semesterInfo.name}计划发布相关事项的通知</h2>
            <p>各位同学：</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;${semesterInfo.name}计划即将开始，开始时间为${formatToDate(semesterInfo.startTime)}，本学期结束时间为${formatToDate(
              semesterInfo.endTime,
            )}。请需要参加的学员做好报名准备。</p>
            <p><strong>注意事项</strong></p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;1. 请按时缴纳学费</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;2. 遵守学校各项规章制度</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;3. 注意安全，保持良好的学习和生活习惯</p>
            <p style="text-align: right;">教务处</p>
            <p style="text-align: right;">${formatToDate(new Date(), 'YYYY年MM月DD日')}</p>
          `;
            createMessage.success('通知内容已生成');
          });
        } catch (error) {
          console.error('生成通知失败:', error);
          createMessage.error('生成通知失败');
        } finally {
          changeLoading(false);
        }
      },
    });
  }

  function init() {
    state.submitType = 0;
    nextTick(() => {
      getForm().resetFields();
      setTimeout(initData, 0);
    });
  }
  function initData() {
    changeLoading(true);
    getInfo(props.semesterId).then(res => {
      state.dataForm = res.data || {};
      if (res.data.attachment) {
        state.dataForm.attachment = JSON.parse(res.data.attachment);
      }
      console.log(state.dataForm);
      changeLoading(false);
    });
  }

  async function handleSubmit(type) {
    try {
      const values = await getForm()?.validate();
      if (!values) return;
      changeLoading(true);

      var query = { ...state.dataForm };

      if (query.attachment) {
        query.attachment = JSON.stringify(query.attachment);
      }
      console.log(state.dataForm);
      const formMethod = query.id ? update : create;
      query.type = '2'; // 2 表示通知配置类型
      if (!query.id) {
        query.id = props.semesterId;
      }
      formMethod(query)
        .then(res => {
          createMessage.success('计划通知配置成功');
          changeLoading(false);
          emit('handleStep', type);
        })
        .catch(() => {
          changeLoading(false);
        });
    } catch (_) {
      changeLoading(false);
    }
  }

  function handleDeleteFile(file) {
    state.dataForm.attachment = state.dataForm.attachment.filter(item => item.fileId !== file.fileId);
  }

  defineExpose({
    init,
    handleSubmit,
  });
</script>

<style lang="less" scoped>
  .ant-col-item {
    margin-bottom: 8px;
  }

  .generate-notice-btn {
    margin-bottom: 10px;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
  }

  .flex {
    display: flex;
  }

  .justify-end {
    justify-content: flex-end;
  }

  .justify-start {
    justify-content: flex-start;
  }

  .mb-2 {
    margin-bottom: 0.5rem;
  }

  /* 附件列表样式 */
  .attachment-list {
    margin-top: 20px;
    margin-bottom: 20px;
  }

  .attachment-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 16px;
    padding-left: 8px;
    border-left: 3px solid #1890ff;
  }

  .attachment-item {
    margin-bottom: 16px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    overflow: hidden;
  }
  .file-list-container {
    margin-top: 20px;

    .file-list {
      display: flex;
      flex-direction: column;
      gap: 16px;
      padding: 10px 0;
    }

    .file-item {
      background: white;
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;
      width: 100%;
      display: flex;
      flex-direction: column;

      /* 移除鼠标悬停效果 */

      .file-info {
        padding: 12px 15px;
        border-top: 1px solid #f0f0f0;

        .file-name {
          font-weight: 500;
          margin-bottom: 5px;
          color: #333;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 100%;
        }

        .file-size {
          font-size: 12px;
          color: #888;
        }
      }
    }
  }
</style>
