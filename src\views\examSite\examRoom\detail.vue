<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" width="700px" :minHeight="400" :showOkBtn="false" class="detail-modal-custom">
    <!-- 详情内容 -->
    <div class="detail-container">
      <div class="detail-content">
        <div class="detail-row">
          <div class="detail-item">
            <label class="detail-label">考场编号：</label>
            <span class="detail-value code-value">{{ dataForm.code || '-' }}</span>
          </div>
          <div class="detail-item">
            <label class="detail-label">考场名称：</label>
            <span class="detail-value name-value">{{ dataForm.name || '-' }}</span>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item">
            <label class="detail-label">考场位置：</label>
            <span class="detail-value location-value">{{ dataForm.location || '-' }}</span>
          </div>
          <div class="detail-item">
            <label class="detail-label">容量：</label>
            <span class="detail-value capacity-value">{{ dataForm.capacity || '-' }}</span>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item">
            <label class="detail-label">设备信息：</label>
            <span class="detail-value equipment-value">{{ dataForm.equipmentInfo || '-' }}</span>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item">
            <label class="detail-label">联系人：</label>
            <span class="detail-value contact-value">{{ dataForm.contactPerson || '-' }}</span>
          </div>
          <div class="detail-item">
            <label class="detail-label">联系电话：</label>
            <span class="detail-value phone-value">{{ dataForm.contactPhone || '-' }}</span>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item">
            <label class="detail-label">状态：</label>
            <span class="detail-value status-value" :class="dataForm.status === 1 ? 'status-enabled' : 'status-disabled'">
              {{ dataForm.status === 1 ? '启用' : dataForm.status === 0 ? '禁用' : '-' }}
            </span>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item full-width">
            <label class="detail-label">备注：</label>
            <div class="detail-value note-value">{{ dataForm.note || '-' }}</div>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item">
            <label class="detail-label">创建时间：</label>
            <span class="detail-value time-value">{{ formatDate(dataForm.creatorTime) }}</span>
          </div>
          <div class="detail-item">
            <label class="detail-label">最后修改：</label>
            <span class="detail-value time-value">{{ formatDate(dataForm.lastModifyTime) }}</span>
          </div>
        </div>
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { getDetailInfo } from './index';
  import { reactive, toRefs, nextTick } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { useI18n } from '@/hooks/web/useI18n';
  import { toDateString } from '@/utils/xunda';

  interface State {
    dataForm: Record<string, any>;
    title: string;
  }

  defineOptions({ name: 'Detail' });
  const emit = defineEmits(['reload']);
  const [registerModal, { openModal, setModalProps }] = useModal();
  const { t } = useI18n();

  const state = reactive<State>({
    dataForm: {},
    title: t('common.detailText', '详情'),
  });

  const { title, dataForm } = toRefs(state);

  defineExpose({ init });

  // 初始化详情
  function init(data: { id: string }) {
    state.dataForm.id = data.id;
    openModal();
    nextTick(() => {
      initData();
    });
  }

  // 加载数据
  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    }
  }

  // 获取详情数据
  function getData(id: string) {
    getDetailInfo(id)
      .then(res => {
        state.dataForm = res.data || {};
        // 优化位置信息显示，如果存在经纬度和地址，使用地址信息
        if (state.dataForm.address && (state.dataForm.latitude || state.dataForm.longtitude)) {
          state.dataForm.location = state.dataForm.address;
        }
        changeLoading(false);
      })
      .catch(() => {
        changeLoading(false);
      });
  }

  // 设置加载状态
  function changeLoading(loading: boolean) {
    setModalProps({ loading });
  }

  // 格式化日期时间
  function formatDate(dateString?: string): string {
    if (!dateString) return '-';
    return toDateString(dateString) || dateString;
  }
</script>

<style scoped>
  .detail-modal-custom {
    .ant-modal-content {
      border-radius: 8px;
      overflow: hidden;
    }

    .ant-modal-header {
      background-color: #fafafa;
      border-bottom: 1px solid #f0f0f0;
      padding: 16px 24px;
    }

    .ant-modal-body {
      padding: 24px;
    }
  }

  .detail-container {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
  }

  .detail-content {
    padding: 16px 0;
  }

  .detail-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 16px;
    align-items: flex-start;
  }

  .detail-item {
    display: flex;
    align-items: flex-start;
    width: 50%;
    padding-right: 20px;
    box-sizing: border-box;
    margin-bottom: 8px;
  }

  .detail-item.full-width {
    width: 100%;
  }

  .detail-label {
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
    min-width: 80px;
    margin-right: 8px;
    flex-shrink: 0;
  }

  .detail-value {
    flex: 1;
    color: rgba(0, 0, 0, 0.65);
    word-break: break-word;
    line-height: 22px;
    padding: 2px 0;
  }

  .note-value {
    min-height: 44px;
    padding: 4px 8px;
    background-color: #fafafa;
    border-radius: 4px;
  }

  .status-enabled {
    color: #52c41a;
  }

  .status-disabled {
    color: #d9d9d9;
  }

  .code-value {
    font-family: Monaco, Menlo, 'Ubuntu Mono', monospace;
  }

  .time-value {
    color: rgba(0, 0, 0, 0.5);
    font-size: 12px;
  }
</style>
