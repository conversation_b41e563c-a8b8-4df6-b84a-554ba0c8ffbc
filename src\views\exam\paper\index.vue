<template>
  <div class="xunda-content-wrapper">
    <div class="xunda-content-wrapper-center">
      <div class="xunda-content-wrapper-search-box">
        <BasicForm
          @register="registerSearchForm"
          :schemas="searchSchemas"
          @advanced-change="redoHeight"
          @submit="handleSearchSubmit"
          @reset="handleSearchReset"
          class="search-form">
        </BasicForm>
      </div>
      <div class="xunda-content-wrapper-content bg-white">
        <BasicTable @register="registerTable" ref="tableRef">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="HandleAdd()"> {{ t('common.add2Text', '新增') }}</a-button>
            <a-button
              type="link"
              preIcon="icon-ym icon-ym-btn-download"
              @click="openExportModal(true, { columnList: exportColumnList, selectIds: getSelectRowKeys() })">
              {{ t('common.exportText', '导出') }}</a-button
            >
            <a-button type="link" preIcon="icon-ym icon-ym-btn-upload" @click="openImportModal(true, { url: '/exam/paper', menuId: searchInfo.menuId })">
              {{ t('common.importText', '导入') }}</a-button
            >
            <a-button type="link" preIcon="icon-ym icon-ym-btn-clearn" @click="handelBatchRemove()"> {{ t('common.batchDelText', '批量删除') }}</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'name'">
              <xunda-input v-model:value="record['name']" :useMask="column.useMask" :maskConfig="column.maskConfig" :showOverflow="true" detailed />
            </template>
            <template v-if="column.dataIndex === 'totalScore'">
              <xunda-input v-model:value="record['totalScore']" :useMask="column.useMask" :maskConfig="column.maskConfig" :showOverflow="true" detailed />
            </template>
            <template v-if="column.dataIndex === 'questionCount'">
              <xunda-input v-model:value="record['questionCount']" :useMask="column.useMask" :maskConfig="column.maskConfig" :showOverflow="true" detailed />
            </template>
            <template v-if="column.dataIndex === 'paperMode'">
              <xunda-input v-model:value="record['paperMode']" :useMask="column.useMask" :maskConfig="column.maskConfig" :showOverflow="true" detailed />
            </template>
            <template v-if="column.dataIndex === 'paperState'">
              <xunda-input v-model:value="record['paperState']" :useMask="column.useMask" :maskConfig="column.maskConfig" :showOverflow="true" detailed />
            </template>
            <template v-if="column.key === 'action'">
              <TableAction
                :actions="[
                  {
                    label: t('common.editText', '编辑'),
                    onClick: handleEdit.bind(null, record),
                    ifShow: record.paperState !== 'published',
                  },
                  {
                    label: t('common.delText', '删除'),
                    color: 'error',
                    modelConfirm: {
                      onOk: handleDelete.bind(null, record.id),
                    },
                    ifShow: record.paperState !== 'published',
                  },
                  {
                    label: t('common.detailText', '详情'),
                    onClick: HandleDetail.bind(null, record),
                  },
                  {
                    label: '发布',
                    color: 'success',
                    onClick: handlePublish.bind(null, record),
                    ifShow: record.paperState !== 'published',
                  },
                  {
                    label: '取消发布',
                    color: 'error',
                    onClick: handleUnPublish.bind(null, record),
                    ifShow: record.paperState === 'published',
                  },
                  {
                    label: '组卷',
                    onClick: () => handleConfigQuestions(record),
                    ifShow: record.paperMode === '固定',
                  },
                ]"
              />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form ref="formRef" @reload="reload" />
    <Detail ref="detailRef" @reload="reload" />
    <ExportModal @register="registerExportModal" @download="handleExport" />
    <ImportModal @register="registerImportModal" @reload="reload" />
    <ExamConfigForm ref="examConfigFormRef" @onReload="onExamConfigReload" />
  </div>
</template>

<script lang="ts" setup>
import { paperApi, getList, batchDelete, exportData, columns, searchSchemas, publishPaper, unpublishPaper } from '@/views/exam/paper';
import { getConfigData } from '@/api/onlineDev/visualDev';
import { ref, reactive, toRefs, onMounted, computed } from 'vue';
import { useModal } from '@/components/Modal';
import { useMessage } from '@/hooks/web/useMessage';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicForm, useForm } from '@/components/Form';
import { BasicTable, useTable, TableAction, TableActionType } from '@/components/Table';
import Form from '@/views/exam/paper/form.vue';
import Detail from '@/views/exam/paper/detail.vue';
import { useRoute, useRouter } from 'vue-router';
import { cloneDeep } from 'lodash-es';
import { ExportModal } from '@/components/CommonModal';
import { downloadByUrl } from '@/utils/file/download';
import { ImportModal } from '@/components/CommonModal';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
import { useUserStore } from '@/store/modules/user';
import { usePermission } from '@/hooks/web/usePermission';
import ExamConfigForm from '@/views/exam/examConfig/form.vue';
const userStore = useUserStore();
const userInfo = userStore.getUserInfo;
const { hasFormP } = usePermission();
const route = useRoute();
const router = useRouter();
const { createMessage, createConfirm } = useMessage();
const { t } = useI18n();
const [registerExportModal, { openModal: openExportModal, closeModal: closeExportModal, setModalProps: setExportModalProps }] = useModal();
const [registerImportModal, { openModal: openImportModal }] = useModal();
const formRef = ref<any>(null);
const tableRef = ref<Nullable<TableActionType>>(null);
const detailRef = ref<any>(null);
const cacheList = ref<any>([]);
const examConfigFormRef = ref();
const defaultSearchInfo = {
  menuId: route.meta.modelId as string,
  moduleId: '***********', //模块ID
  superQueryJson: '',
  dataType: 0,
};
const exportColumnList = computed(() => {
  return columns.map(o => {
    return {
      id: o.dataIndex,
      key: o.dataIndex,
      value: o.dataIndex,
      fullName: o.title,
      __config__: {
        xundakey: 'text',
      },
    };
  });
});
const searchInfo = reactive({
  ...cloneDeep(defaultSearchInfo),
});
const [
  registerSearchForm,
] = useForm({
  baseColProps: { span: 6 },
  showActionButtonGroup: true,
  showAdvancedButton: true,
  compact: true,
});
const [registerTable, { reload, setLoading, getFetchParams, redoHeight, getSelectRowKeys, clearSelectedRowKeys }] = useTable({
  api: getList,
  columns: columns,
  searchInfo: searchInfo,
  clickToRowSelect: false,
  afterFetch: data => {
    const list = data.map(o => ({
      ...o,
    }));
    cacheList.value = cloneDeep(list);
    return list;
  },
  actionColumn: {
    width: 200,
    title: t('common.actionText', '操作'),
    dataIndex: 'action',
    fixed: 'right',
  },
  rowSelection: {
    type: 'checkbox',
    getCheckboxProps: record => ({
      disabled: record.top,
    }),
  },
});

function handleSearchReset() {
  clearSelectedRowKeys();
}

function handleSearchSubmit(data) {
  clearSelectedRowKeys();
  let obj = {
    ...defaultSearchInfo,
    superQueryJson: searchInfo.superQueryJson,
    ...data,
  };
  Object.keys(searchInfo).map(key => {
    delete searchInfo[key];
  });
  for (let [key, value] of Object.entries(obj)) {
    searchInfo[key.replaceAll('-', '_')] = value;
  }
  console.log(searchInfo);
  reload({ page: 1 });
}

// 编辑
function handleEdit(record) {
  // 不带工作流
  const data = {
    id: record.id,
    menuId: searchInfo.menuId,
    allList: cacheList.value,
  };
  formRef.value?.init(data);
}

// 删除
function handleDelete(id) {
  const query = { ids: [id] };
  batchDelete(query).then(res => {
    createMessage.success(res.msg);
    clearSelectedRowKeys();
    reload();
  });
}

// 查看详情
function HandleDetail(record) {
  // 不带流程
  const data = {
    id: record.id,
  };
  detailRef.value?.init(data);
}

// 新增
function HandleAdd() {
  // 不带流程新增
  const data = {
    id: '',
    menuId: searchInfo.menuId,
    allList: cacheList.value,
  };
  formRef.value?.init(data);
}

// 导出
function handleExport(data) {
  let query = { ...getFetchParams(), ...data };
  exportData(query)
    .then(res => {
      setExportModalProps({ confirmLoading: false });
      if (!res.data.url) return;
      downloadByUrl({ url: res.data.url });
      closeExportModal();
    })
    .catch(() => {
      setExportModalProps({ confirmLoading: false });
    });
}

// 批量删除
function handelBatchRemove() {
  const ids = getSelectRowKeys();
  if (!ids.length) return createMessage.error('请选择一条数据');
  createConfirm({
    iconType: 'warning',
    title: t('common.tipTitle'),
    content: '您确定要删除这些数据吗, 是否继续?',
    onOk: () => {
      const query = { ids: ids };
      setLoading(true);
      batchDelete(query).then(res => {
        createMessage.success(res.msg);
        clearSelectedRowKeys();
        reload();
      });
    },
  });
}

function setSearchSchema() {}

function handlePublish(record) {
  examConfigFormRef.value?.init({
    id: '',
    paperIds: record.id,
    allList: [],
  });
}

function handleUnPublish(record) {
  createConfirm({
    iconType: 'warning',
    title: t('common.tipTitle'),
    content: '确定要取消发布该试卷吗？',
    onOk: async () => {
      try {
        await unpublishPaper(record.id);
        createMessage.success('取消发布成功');
        reload();
      } catch (e) {
        createMessage.error('取消发布失败');
      }
    },
  });
}

function handleConfigQuestions(record) {
  // 跳转到 paperQuestion 列表页，带上 paperId 和 courseId
  router.push({
    path: '/exam/paperQuestion',
    query: { paperId: record.id, courseId: record.courseId },
  });
}

function onExamConfigReload() {
  router.push({ path: '/exam/examConfig' });
  // 这里假设examConfig页面有自动刷新逻辑，如果没有可用事件或bus通知
}

onMounted(() => {
  setSearchSchema();
});
</script>
