<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    title="选课配置"
    :width="800"
    @ok="handleSubmit"
    class="transfer-modal"
    :default-fullscreen="true"
    v-model:open="visible">
    <div class="transfer__body">
      <div class="transfer-pane left-pane" style="width: 50%">
        <div class="transfer-pane__tool">
          <a-input-search :placeholder="t('common.enterKeyword')" allowClear v-model:value="keyword" @search="handleSearch" />
        </div>
        <BasicTable @register="registerTable" ref="tableRef">
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'action'">
              <TableAction
                :actions="[
                  {
                    label: t('选择'),
                    onClick: addHandle.bind(null, record),
                    disabled: getSelectRowKeys().includes(record.id),
                  },
                ]" />
            </template>
          </template>
        </BasicTable>
      </div>
      <div class="transfer-pane right-pane" style="width: 50%; height: 100%">
        <div class="transfer-pane__tool">
          <span>已选</span>
          <span class="remove-all-btn" @click="removeAll">清空列表</span>
        </div>
        <BasicTable @register="registerTable2" ref="tableRef2">
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'action'">
              <TableAction
                :actions="[
                  {
                    label: t('取消选择'),
                    onClick: deleteHandle.bind(null, record),
                  },
                ]" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { toRefs, ref, reactive, unref, watch } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { BasicTable, useTable, TableAction, TableActionType } from '@/components/Table';
  import { getAllCourse, columns, getSelectCourse, setSelectCourse } from './CourseSelect';

  interface State {
    treeData: any[];
    dataForm: any;
    keyword: string;
    selectedData: any[];
    selectedDataIds: any[];
  }
  defineOptions({ name: 'PatientSelect', inheritAttrs: false });

  const { t } = useI18n();
  const { createMessage } = useMessage();
  const tableRef = ref<Nullable<TableActionType>>(null);
  const [registerModal, { changeLoading, changeOkLoading, closeModal }] = useModalInner(init);
  const state = reactive<State>({
    treeData: [],
    dataForm: {
      interfaceIdentId: '',
      dataInterfaceIds: [],
    },
    keyword: '',
    selectedData: [],
    selectedDataIds: [],
  });
  const { treeData, selectedData, selectedDataIds, keyword } = toRefs(state);

  const props = defineProps({});

  const leftTableSerchInfo = {
    moduleId: '661952363199727173',
    superQueryJson: '',
    dataType: 1,
    keyword: '',
  };

  const visible = ref(false);

  const emit = defineEmits(['register', 'submit']);

  const [registerTable, { reload, setLoading, getFetchParams, getSelectRows, getSelectRowKeys, redoHeight, clearSelectedRowKeys, setSelectedRowKeys }] =
    useTable({
      api: getAllCourse,
      columns: columns,
      searchInfo: leftTableSerchInfo,
      beforeFetch: params => {
        params.semesterId = semesterId.value;
        return params;
      },
      clickToRowSelect: false,
      afterFetch: data => {
        const list = data.map(o => ({
          ...o,
          // ...state.expandObj,
        }));
        const arr = state.selectedData.map(o => o.id);
        setSelectedRowKeys(arr);
        return list;
      },
      immediate: true,
      canResize: true,
      bordered: true,
      showIndexColumn: true,
      resizeHeightOffset: 100,
      indexColumnProps: {
        width: 50,
      },
      actionColumn: {
        width: 150,
        title: t('component.table.action'),
        dataIndex: 'action',
      },
      rowSelection: {
        type: 'checkbox',
        hideSelectAll: true,
        onSelect: handleSelect,
        onSelectAll: handleSelectAll,
        getCheckboxProps: record => ({
          disabled: record.top,
        }),
      },
    });

  const [registerTable2, { setTableData }] = useTable({
    columns: columns,
    dataSource: state.selectedData,
    immediate: true,
    canResize: true,
    bordered: true,
    showIndexColumn: true,
    resizeHeightOffset: 100,
    indexColumnProps: {
      width: 50,
    },
    actionColumn: {
      width: 150,
      title: t('component.table.action'),
      dataIndex: 'action',
    },
    pagination: false,
  });
  const semesterId = ref('');
  function init(data) {
    console.log(data);
    semesterId.value = data.id;
    state.dataForm.interfaceIdentId = data.id;
    state.dataForm.dataInterfaceIds = [];
    state.treeData = [];
    state.selectedData = [];
    state.keyword = '';
    visible.value = true;
    initData(data);
  }
  function initData(data) {
    changeLoading(true);
    if (getSelectedValue) {
      getSelectedValue(data).then(res => {
        state.selectedData = res.data ?? [];
        changeLoading(false);
        reload();
      });
    }
  }

  watch(
    () => state.selectedData,
    () => {
      const arr = (state.selectedData ?? []).map(o => o.id);
      setSelectedRowKeys(arr);
      setTableData(state.selectedData);
    },
    { deep: true },
  );

  function getSelectedValue(record) {
    var query = {
      id: record.id,
    };
    return getSelectCourse(query);
  }

  function handleSelect(record, selected) {
    if (selected) {
      selectedData.value.push(record);
    } else [(selectedData.value = selectedData.value.filter(o => o.id !== record.id))];
  }

  function handleSelectAll(selected, selectedRows: any[], changeRows) {
    selectedData.value = selectedData.value.filter(o => selectedRows.findIndex(s => s.id === o) !== -1);
    if (selected) {
      selectedData.value.push(...selectedRows);
    }
  }

  function handleSearch(value) {
    leftTableSerchInfo.keyword = value;
    getTable().reload();
  }
  function getTable() {
    const table = unref(tableRef);
    if (!table) throw new Error('table is null!');
    return table;
  }
  function removeAll() {
    state.selectedData = [];
    const arr = state.selectedData.map(o => o.id);
    setSelectedRowKeys(arr);
  }
  function removeData(index: number) {
    state.selectedData.splice(index, 1);
    const arr = state.selectedData.map(o => o.id);
    setSelectedRowKeys(arr);
  }

  function addHandle(record) {
    state.selectedData.push(record);
    const arr = state.selectedData.map(o => o.id);
    setSelectedRowKeys(arr);
  }

  function deleteHandle(record) {
    state.selectedData = state.selectedData.filter(x => x.id !== record.id);
  }

  function handleCancel() {
    visible.value = false;
  }
  function handleSubmit() {
    changeOkLoading(true);

    function callBack() {
      handleCancel();
      changeOkLoading(false);
    }
    const arr = state.selectedData.map(o => o.id);
    state.dataForm.dataInterfaceIds = arr.join(',');
    var query = {
      semesterId: semesterId.value,
      courseIds: arr,
    };
    setSelectCourse(query).then(() => {
      closeModal();
    });
  }
</script>
