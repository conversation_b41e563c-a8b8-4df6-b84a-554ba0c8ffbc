<template>
  <a-row class="mt-20px h-full" :class="'overflow-auto'">
    <a-col :span="16" :offset="4">
      <a-row class="dynamic-form">
        <a-form
          ref="formRef"
          :model="formState"
          size="middle"
          layout="horizontal"
          :colon="false"
          labelAlign="right"
          :labelCol="{ style: { width: '150px' } }"
          :rules="dataRule">
          <a-row :gutter="15">
            <!-- 公告数据 -->
            <a-col :span="12" class="ant-col-item">
              <a-form-item label="认定职业" name="profession">
                <XundaSelect
                  v-model:value="formState.profession"
                  :disabled="disabled"
                  :options="[
                    { key: '工程师', fullName: '工程师' },
                    { key: '高级工程师', fullName: '高级工程师' },
                    { key: '技师', fullName: '技师' },
                    { key: '高级技师', fullName: '高级技师' },
                  ]"
                  :fieldNames="{
                    label: 'fullName',
                    value: 'key',
                  }"
                  placeholder="请选择认定职业"
                  :allowClear="true"
                  :style="{ width: '100%' }">
                </XundaSelect>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item">
              <a-form-item label="认定等级" name="level">
                <XundaSelect
                  v-model:value="formState.level"
                  :disabled="disabled"
                  :options="[
                    {
                      key: '1级',
                      fullName: '1级',
                    },
                    {
                      key: '2级',
                      fullName: '2级',
                    },
                    {
                      key: '3级',
                      fullName: '3级',
                    },
                    {
                      key: '4级',
                      fullName: '4级',
                    },
                    {
                      key: '5级',
                      fullName: '5级',
                    },
                  ]"
                  :fieldNames="{
                    label: 'fullName',
                    value: 'key',
                  }"
                  placeholder="请选择认定等级"
                  :allowClear="true"
                  :style="{ width: '100%' }">
                </XundaSelect>
              </a-form-item>
            </a-col>
            <a-col :span="24" class="ant-col-item">
              <a-form-item label="申报条件" name="applicationConditions">
                <XundaTextarea
                  v-model:value="formState.applicationConditions"
                  :disabled="disabled"
                  placeholder="请输入申报条件"
                  :allowClear="true"
                  :style="{ width: '100%' }"
                  :autoSize="{ minRows: 4, maxRows: 6 }"
                  :showCount="true"
                  :maxlength="500">
                </XundaTextarea>
              </a-form-item>
            </a-col>
            <a-col :span="24" class="ant-col-item">
              <a-form-item label="认定标准" name="certificationStandards">
                <XundaInput
                  v-model:value="formState.certificationStandards"
                  :disabled="disabled"
                  placeholder="请输入认定标准"
                  :allowClear="true"
                  :style="{ width: '100%' }"
                  :autoSize="{ minRows: 4, maxRows: 6 }"
                  :showCount="true"
                  :maxlength="500">
                </XundaInput>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item">
              <a-form-item label="报名截止时间" name="registrationTime">
                <XundaDatePicker
                  v-model:value="formState.registrationTime"
                  :disabled="disabled"
                  placeholder="请选择日期"
                  :allowClear="true"
                  :style="{ width: '100%' }"
                  format="YYYY-MM-DD">
                </XundaDatePicker>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item">
              <a-form-item label="认定时间" name="certificationTime">
                <XundaDatePicker
                  v-model:value="formState.certificationTime"
                  :disabled="disabled"
                  placeholder="请选择日期"
                  :allowClear="true"
                  :style="{ width: '100%' }"
                  format="YYYY-MM-DD">
                </XundaDatePicker>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item">
              <a-form-item label="考区考点" name="examLocation">
                <XundaInput
                  v-model:value="formState.examLocation"
                  :disabled="disabled"
                  placeholder="请输入考区考点信息"
                  :allowClear="true"
                  :style="{ width: '100%' }">
                </XundaInput>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item">
              <a-form-item label="收费标准" name="feeStandard">
                <XundaInput
                  v-model:value="formState.feeStandard"
                  :disabled="disabled"
                  placeholder="请输入收费标准"
                  :allowClear="true"
                  :style="{ width: '100%' }"
                  :precision="2">
                </XundaInput>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item">
              <a-form-item label="咨询电话" name="consultationPhone">
                <XundaInput
                  v-model:value="formState.consultationPhone"
                  :disabled="disabled"
                  placeholder="请输入咨询电话"
                  :allowClear="true"
                  :style="{ width: '100%' }">
                </XundaInput>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item">
              <a-form-item label="举报监督电话" name="supervisionPhone">
                <XundaInput
                  v-model:value="formState.supervisionPhone"
                  :disabled="disabled"
                  placeholder="请输入举报监督电话"
                  :allowClear="true"
                  :style="{ width: '100%' }">
                </XundaInput>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item">
              <a-form-item label="公告人" name="announcer">
                <XundaInput v-model:value="formState.announcer" :disabled="disabled" placeholder="请输入公告人" :allowClear="true" :style="{ width: '100%' }">
                </XundaInput>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item">
              <a-form-item label="公告时间" name="announcementTime">
                <XundaDatePicker
                  v-model:value="formState.announcementTime"
                  :disabled="disabled"
                  placeholder="请选择公告时间"
                  :allowClear="true"
                  :style="{ width: '100%' }"
                  format="YYYY-MM-DD">
                </XundaDatePicker>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-row>
    </a-col>
  </a-row>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, unref } from 'vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import type { Rule } from 'ant-design-vue/es/form';
  import { getNoticeData, setNoticeData } from './index';
  import { XundaSelect, XundaInput, XundaInputNumber, XundaDatePicker, XundaTextarea } from '@/components/Xunda';

  const { createMessage, createConfirm } = useMessage();
  const props = defineProps({
    semesterId: { type: String, default: '' },
    disabled: { type: Boolean, default: false },
  });

  // 定义组件的 emits
  const emit = defineEmits(['changeLoading', 'handleStep']);
  const changeLoading = loading => {
    emit('changeLoading', loading);
  };

  const detailed = computed(() => props.disabled);

  // 默认表单数据
  const defaultFormData = reactive({
    profession: '',
    applicationConditions: '',
    certificationStandards: '',
    registrationTime: null,
    certificationTime: null,
    examLocation: '',
    feeStandard: null,
    consultationPhone: '',
    supervisionPhone: '',
    announcer: '',
    announcementTime: null,
  });

  const formState = ref<any>({});

  // 表单引用
  const formRef = ref();

  // 电话号码验证规则
  const validatePhone = async (_rule: Rule, value: string) => {
    if (!value) {
      return Promise.reject('请输入电话号码');
    }
    const phoneReg = /^1[3-9]\d{9}$|^0\d{2,3}-?\d{7,8}$/;
    if (!phoneReg.test(value)) {
      return Promise.reject('请输入正确的电话号码格式');
    }
    return Promise.resolve();
  };

  // 收费标准验证规则
  const validateFee = async (_rule: Rule, value: number | null) => {
    if (value === null) {
      return Promise.reject('请输入收费标准');
    }
    if (value < 0) {
      return Promise.reject('收费标准不能小于0');
    }
    return Promise.resolve();
  };

  // 表单验证规则
  const dataRule = {
    profession: [{ required: true, message: '请选择认定职业', trigger: 'change' }],
    level: [{ required: true, message: '请选择级别', trigger: 'change' }],
    applicationConditions: [{ required: true, message: '请输入申报条件', trigger: 'blur' }],
    certificationStandards: [{ required: true, message: '请输入认定标准', trigger: 'blur' }],
    registrationTime: [{ required: true, message: '请选择报名时间', trigger: 'change' }],
    certificationTime: [{ required: true, message: '请选择认定时间', trigger: 'change' }],
    examLocation: [{ required: true, message: '请输入考区考点', trigger: 'blur' }],
    feeStandard: [
      { required: true, message: '请输入收费标准', trigger: 'change' },
      { validator: validateFee, trigger: 'change' },
    ],
    consultationPhone: [
      { required: true, message: '请输入咨询电话', trigger: 'blur' },
      // { validator: validatePhone, trigger: 'blur' },
    ],
    supervisionPhone: [
      { required: true, message: '请输入举报监督电话', trigger: 'blur' },
      // { validator: validatePhone, trigger: 'blur' },
    ],
    announcer: [{ required: true, message: '请输入公告人', trigger: 'blur' }],
    announcementTime: [{ required: true, message: '请选择公告时间', trigger: 'change' }],
  };

  // 初始化表单数据
  function init(data) {
    changeLoading(true);
    getNoticeData({ id: data.id })
      .then(res => {
        initData(res.data ?? {});
        changeLoading(false);
      })
      .catch(() => {
        changeLoading(false);
      });
  }

  function initData(data) {
    formState.value = { ...defaultFormData, ...data };
  }

  // 重置表单
  function handleReset() {
    formState.value = { ...defaultFormData };
  }
  function getForm() {
    const form = unref(formRef);
    if (!form) {
      throw new Error('form is null!');
    }
    return form;
  }

  // 提交表单
  async function handleSubmit(type) {
    try {
      if (type === 'next') {
        const values = await getForm()?.validate();
        if (!values) return;
      }
      changeLoading(true);
      setNoticeData({ id: props.semesterId, noticeData: formState.value })
        .then(res => {
          if (res.code === 200 && res.data === true) {
            createMessage.success('公告配置成功');
            changeLoading(false);
            emit('handleStep', type);
          }
        })
        .catch(() => {
          changeLoading(false);
        });
    } catch (_) {
      changeLoading(false);
    }
  }

  // 暴露表单方法给父组件
  defineExpose({
    init,
    validate: () => formRef.value.validate(),
    resetFields: handleReset,
    getFieldsValue: () => formState.value,
    handleSubmit,
  });
</script>

<style scoped>
  .profession-certification-form {
    padding: 24px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .form-title {
    margin-bottom: 24px;
    color: #333;
    font-size: 18px;
    font-weight: 600;
    border-bottom: 2px solid #1890ff;
    padding-bottom: 24px;
  }

  .certification-form {
    max-width: 100%;
  }

  .form-buttons {
    margin-top: 32px;
    text-align: center;
  }

  :deep(.ant-form-item-label > label) {
    font-weight: 500;
    color: #666;
  }

  :deep(.ant-input) {
    border-radius: 6px;
  }

  :deep(.ant-input-affix-wrapper) {
    border-radius: 6px;
  }

  :deep(.ant-picker) {
    border-radius: 6px;
  }
</style>
