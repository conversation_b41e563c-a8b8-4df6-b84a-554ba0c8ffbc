import { type ICommand, type IAccessor, CommandType } from '@univerjs/core';
import { XundaSheetsExcelFileService } from '../../services/sheet-excel-file.service';
import { XundaCommandIds } from '../../utils/define';

export const XundaSheetsImportExcelFileOperation: ICommand = {
  id: XundaCommandIds.importExcelFile,
  type: CommandType.OPERATION,
  handler: async (_: IAccessor) => {
    return true;
  },
};

export const XundaSheetsDownloadExcelFileOperation: ICommand = {
  id: XundaCommandIds.downloadExcelFile,
  type: CommandType.OPERATION,
  handler: async (_: IAccessor) => {
    return true;
  },
};

export const XundaSheetsImportCsvFileOperation: ICommand = {
  id: XundaCommandIds.importCsvFile,
  type: CommandType.OPERATION,
  handler: async (accessor: IAccessor) => {
    const xundaSheetsExcelFileService = accessor.get(XundaSheetsExcelFileService);
    xundaSheetsExcelFileService?.handleImportCsv();

    return true;
  },
};
