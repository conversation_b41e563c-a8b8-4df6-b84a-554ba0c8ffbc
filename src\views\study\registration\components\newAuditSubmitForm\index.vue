<template>
  <div class="plan-result">
    <div class="title"> 审核信息 </div>

    <a-form ref="formRef" :model="formState" layout="vertical" class="audit-form">
      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-item label="审核意见" name="remark">
            <a-textarea
              v-model:value="formState.remark"
              :rows="4"
              :placeholder="showApproveButton ? '请输入审核意见' : ''"
              class="custom-textarea"
              :readOnly="showRevokeButton" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="备注" name="note">
            <a-textarea v-model:value="formState.note" :rows="4" placeholder=" " class="custom-textarea" :readOnly="showRevokeButton" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>

    <div class="audit-actions">
      <a-button @click="handleGetRemark" class="audit-btn" v-if="showApproveButton">
        <HourglassOutlined />
        <span>重置</span>
      </a-button>
      <a-button type="primary" @click="handleSetRemark" class="audit-btn" v-if="showApproveButton">
        <SaveFilled />
        <span>暂存</span>
      </a-button>
      <a-button type="primary" class="audit-btn approve-btn" @click="handleApprove" v-if="showApproveButton && canAudit">
        <CheckOutlined />
        <span>通过</span>
      </a-button>
      <a-button type="error" class="audit-btn reject-btn" @click="handleRevoke" v-if="showRevokeButton">
        <SyncOutlined />
        <span>撤销</span>
      </a-button>

      <a-button type="warning" class="audit-btn reject-btn" @click="handleReject()" v-if="showRejectButton && canAudit">
        <CloseOutlined />
        <span>驳回</span>
      </a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed, reactive, ref, toRefs } from 'vue';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useMessage } from '@/hooks/web/useMessage';
  import { auditRegistrationAsync, getRemarkAsync, sendMessage, setRemarkAsync } from '.';
  import { FormActionType } from '@/components/Form';
  import { CheckOutlined, CloseOutlined, SyncOutlined, HourglassOutlined, SaveFilled } from '@ant-design/icons-vue';

  const emit = defineEmits(['prev', 'next', 'finish', 'changeLoading', 'update:auditState']);

  const props = defineProps({
    registrationId: {
      type: String,
      default: '',
    },
    auditState: { type: String, default: '' },
    canAudit: { type: Boolean, default: false },
  });

  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const formRef = ref<FormActionType>();

  const showApproveButton = computed(() => {
    if (props.auditState === '待审核') return true;
    // if (props.auditState === '审核不通过') return true;
    return false;
  });

  const showRevokeButton = computed(() => {
    if (props.auditState === '审核通过') return true;
    if (props.auditState === '待支付') return true;
    if (props.auditState === '完成') return true;
    return false;
  });

  const showRejectButton = computed(() => {
    if (props.auditState === '待审核') return true;
    return false;
  });

  const auditSuccess = computed(() => {
    if (props.auditState === '审核通过') return true;
    if (props.auditState === '待支付') return true;
    if (props.auditState === '完成') return true;
    return false;
  });
  const canNotAudit = computed(() => {
    if (props.auditState === '选择计划') return true;
    if (props.auditState === '填写资料') return true;
    if (props.auditState === '资料上传') return true;
    if (props.auditState === '电子档上传') return true;
    return false;
  });

  interface FormState {
    id: string;
    state: string;
    remark: string;
    note: string;
  }

  const formState = reactive<FormState>({
    id: '',
    state: '',
    remark: '',
    note: '',
  });
  const { state } = toRefs(formState);

  function init() {
    getRenark();
  }

  // 获取审核情况
  function getRenark() {
    var id = props.registrationId;
    emit('changeLoading', true);
    getRemarkAsync(id)
      .then(res => {
        const { state, remark, note } = res.data;
        formState.id = props.registrationId;
        formState.state = state;
        formState.remark = remark;
        formState.note = note;
        emit('update:auditState', state);
      })
      .catch(error => {
        createMessage.error('获取审核信息失败');
      })
      .finally(() => {
        emit('changeLoading', false);
      });
  }

  // 刷新审核信息
  function handleGetRemark() {
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '刷新会覆盖未保存的审核信息，确认筛选吗?',
      onOk: () => {
        getRenark();
      },
      onCancel: () => {
        console.log('Cancel');
      },
    });
  }

  // 暂存审核信息
  function handleSetRemark() {
    var data = {
      id: props.registrationId,
      remark: formState.remark,
      note: formState.note,
    };
    emit('changeLoading', true);
    setRemarkAsync(data)
      .then(res => {
        createMessage.success('暂存成功');

        emit('changeLoading', false);
      })
      .catch(error => {
        emit('changeLoading', false);
      });
  }

  // 通过
  function handleApprove() {
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '确认通过该学员的报名申请吗?',
      onOk: () => {
        var data = {
          id: props.registrationId,
          state: '审核通过',
          auditType: '审核通过',
          remark: formState.remark,
          note: formState.note,
        };
        emit('changeLoading', true);
        handleSubmit(data);
        emit('next');
      },
      onCancel: () => {
        console.log('Cancel');
      },
    });
  }

  // 驳回
  function handleReject() {
    if (!formState.remark) {
      createMessage.error('驳回申请，请填写审核意见');
      return;
    }
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '确认驳回该学员的报名申请吗?',
      onOk: () => {
        var data = {
          id: props.registrationId,
          state: '审核不通过',
          auditType: '审核驳回',
          remark: formState.remark,
          note: formState.note,
        };
        emit('changeLoading', true);
        handleSubmit(data);
      },
      onCancel: () => {
        console.log('Cancel');
      },
    });
  }

  // 撤销
  function handleRevoke() {
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '确认撤销通过该学员的报名申请吗?',
      onOk: () => {
        var data = {
          id: props.registrationId,
          state: '待审核',
          auditType: '审核通过撤销',
          remark: formState.remark,
          note: formState.note,
        };
        emit('changeLoading', true);
        handleSubmit(data);
      },
      onCancel: () => {
        console.log('Cancel');
      },
    });
  }

  // 提交审核结果
  function handleSubmit(data) {
    auditRegistrationAsync(data)
      .then(res => {
        getRenark();
        emit('finish');
      })
      .catch(() => {
        emit('changeLoading', false);
      });
  }

  defineExpose({
    init,
  });
</script>

<style lang="less" scoped>
  .plan-result {
    width: 100%;
    padding: 32px;

    .title {
      font-size: 20px;
      font-weight: 600;
      color: #333;
      margin-bottom: 24px;
      padding-bottom: 12px;
      border-bottom: 1px solid #eaeaea;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        width: 60px;
        height: 3px;
        background: #1890ff;
        border-radius: 2px;
      }
    }

    .custom-icon-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 72px;
      height: 72px;
      border-radius: 50%;
      margin-bottom: 16px;
      transition: all 0.3s ease;

      &.success {
        background-color: rgba(82, 196, 26, 0.1);
        border: 2px solid #52c41a;
        color: #52c41a;
      }

      &.error {
        background-color: rgba(245, 34, 45, 0.1);
        border: 2px solid #f5222d;
        color: #f5222d;
      }

      &.warning {
        background-color: rgba(250, 173, 20, 0.1);
        border: 2px solid #faad14;
        color: #faad14;
      }

      &:hover {
        transform: scale(1.05);
      }
    }

    .result-card {
      width: 100%;
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      padding: 24px;
      margin-bottom: 32px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
        transform: translateY(-3px);
      }

      &.success-card {
        border: 1px solid #52c41a;
      }

      &.error-card {
        border: 1px solid #f5222d;
      }

      &.pending-card {
        border: 1px solid #faad14;
      }

      &.info-card {
        border: 1px solid #1890ff;
      }
    }

    .status-content {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 24px 0;
      max-width: 800px;
      margin: 0 auto;

      .status-message {
        font-size: 16px;
        color: #ffffff;
        margin-bottom: 16px;
        padding: 12px 20px;
        background: rgba(250, 173, 20, 0.1);
        border-radius: 8px;
        border-left: 4px solid #faad14;
      }

      :deep(.ant-result) {
        width: 100%;
        margin-bottom: 24px;
      }

      :deep(.ant-form-item) {
        margin-bottom: 28px;
      }
    }

    .audit-form {
      width: 100%;
      padding: 24px;
      background: #f9fafb;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      margin-bottom: 24px;

      .form-header {
        margin-bottom: 20px;

        h3 {
          font-size: 18px;
          font-weight: 500;
          color: #333;
          margin-bottom: 8px;
        }

        .form-divider {
          height: 2px;
          background: linear-gradient(to right, #1890ff, #f0f2f5);
          border-radius: 2px;
        }
      }

      :deep(.ant-form-item-label) {
        font-weight: 500;
        padding-bottom: 8px;
      }

      :deep(.ant-input) {
        border-radius: 8px;
        padding: 12px;
        &:hover,
        &:focus {
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
        }
      }

      .audit-radio-group {
        display: flex;
        gap: 24px;
        padding: 8px 0;

        :deep(.ant-radio-wrapper) {
          font-size: 15px;
        }
      }
    }

    .action-buttons {
      display: flex;
      gap: 20px;
      justify-content: center;
      margin-top: 32px;

      .ant-btn {
        min-width: 140px;
        height: 44px;
        font-size: 15px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        transition: all 0.3s;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }

  .audit-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px dashed #e8e8e8;
  }

  .audit-btn {
    min-width: 120px;
    height: 40px;
    border-radius: 4px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
  }

  .audit-btn span + span {
    margin-left: 8px;
  }

  .approve-btn {
    background-color: #52c41a;
    border-color: #52c41a;
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
  }

  .approve-btn:hover {
    background-color: #73d13d;
    border-color: #73d13d;
  }

  .reject-btn {
    background-color: #ff4d4f;
    border-color: #ff4d4f;
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
  }

  .reject-btn:hover {
    background-color: #ff7875;
    border-color: #ff7875;
  }
</style>
