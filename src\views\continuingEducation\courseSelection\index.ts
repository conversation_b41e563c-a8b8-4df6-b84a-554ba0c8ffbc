import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';

// 基础Api
export const studentCourseApi = '/api/ea/studentCourse';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: studentCourseApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: studentCourseApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: studentCourseApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: studentCourseApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: studentCourseApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: studentCourseApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: studentCourseApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: studentCourseApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: studentCourseApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: studentCourseApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: studentCourseApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: studentCourseApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: studentCourseApi + `/getForSelect`, data });
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'name',
    label: t('姓名'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
  {
    field: 'no',
    label: t('学号'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('课程名称'),
    dataIndex: 'courseName',
    width: 120,
  },
  {
    title: t('学号'),
    dataIndex: 'studentNo',
    width: 120,
  },
  {
    title: t('姓名'),
    dataIndex: 'studentName',
    width: 120,
  },
];
