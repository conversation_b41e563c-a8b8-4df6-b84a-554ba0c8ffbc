import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';

// 基础Api
export const questionTypeApi = '/api/exam/questionType';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: questionTypeApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: questionTypeApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: questionTypeApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: questionTypeApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: questionTypeApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: questionTypeApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: questionTypeApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: questionTypeApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: questionTypeApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: questionTypeApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: questionTypeApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: questionTypeApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: questionTypeApi + `/getForSelect`, data });
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'name',
    label: t('名称'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('主键'),
    dataIndex: 'id',
    width: 120,
  },
  {
    title: t('名称'),
    dataIndex: 'name',
    width: 120,
  },
  {
    title: t('自动判题'),
    dataIndex: 'autoJudgement',
    width: 120,
    customRender: xundaUtils.customRenderBoolean,
  },
  {
    title: t('配置'),
    dataIndex: 'extraConfig',
    width: 120,
  },
  {
    title: t('题目模板'),
    dataIndex: 'questionTemplate',
    width: 120,
  },
];
