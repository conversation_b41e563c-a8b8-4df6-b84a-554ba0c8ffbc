<template>
  <div class="anticon" :class="getAppLogoClass" @click="goHome">
    <div style="margin-right: 10px"> </div>
    <template v-if="showTitle">
      <a-image
        class="login-logo"
        :src="apiUrl + getSysConfig.navigationIcon"
        :fallback="logoImg"
        :preview="false"
        v-if="getSysConfig && getSysConfig.navigationIcon" />
      <img class="login-logo" :src="logoImg" v-else />
      <span>继续教育与社会服务管理平台</span>
    </template>
    <template v-else>
      <a-image
        class="login-logo"
        :src="apiUrl + getSysConfig.workLogoIcon"
        :fallback="xundaImg"
        :preview="false"
        v-if="getSysConfig && getSysConfig.workLogoIcon" />
      <img :src="xundaImg" v-else />
    </template>
  </div>
</template>
<script lang="ts" setup>
  import { computed, unref, ref } from 'vue';
  import { Image as AImage } from 'ant-design-vue';
  import { useGo } from '@/hooks/web/usePage';
  import { useMenuSetting } from '@/hooks/setting/useMenuSetting';
  import { useDesign } from '@/hooks/web/useDesign';
  import { PageEnum } from '@/enums/pageEnum';
  import { useAppStore } from '@/store/modules/app';
  import { useGlobSetting } from '@/hooks/setting';
  import logoImg from '@/assets/images/xunda2.png';
  import xundaImg from '@/assets/images/xunda.png';

  const props = defineProps({
    /**
     * The theme of the current parent component
     */
    theme: { type: String, validator: (v: string) => ['light', 'dark'].includes(v) },
    /**
     * Whether to show title
     */
    showTitle: { type: Boolean, default: true },
    /**
     * The title is also displayed when the menu is collapsed
     */
    alwaysShowTitle: { type: Boolean },
  });

  const { prefixCls } = useDesign('app-logo');
  const { getCollapsedShowTitle } = useMenuSetting();
  const appStore = useAppStore();
  const globSetting = useGlobSetting();
  const apiUrl = ref(globSetting.apiUrl);
  const go = useGo();

  const getAppLogoClass = computed(() => [prefixCls, props.theme, { 'collapsed-show-title': unref(getCollapsedShowTitle) }]);
  const getSysConfig = computed(() => appStore.getSysConfigInfo);

  function goHome() {
    go(PageEnum.BASE_HOME);
  }
</script>
<style lang="less" scoped>
  @prefix-cls: ~'@{namespace}-app-logo';

  .@{prefix-cls} {
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s ease;

    &.collapsed-show-title {
      padding-left: 20px;
    }

    &.light &__title {
      color: @primary-color;
    }

    &.dark &__title {
      color: @white;
    }

    &__title {
      font-size: 16px;
      font-weight: 700;
      transition: all 0.5s;
      line-height: normal;
    }
    :deep(.ant-image),
    .login-logo {
      width: auto;
      height: 100%;
      .login-logo {
        width: auto;
        height: 100%;
      }
    }
  }

  /* 添加标题文字样式 */
  span {
    color: #135383; /* 墨蓝色 */
    font-size: 30px; /* 放大字体 */
    white-space: nowrap;
    font-family: '行草', '行书', '草书', '毛笔行草', '行楷', STXingkai, '华文行楷', cursive;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1); /* 添加轻微阴影增强毛笔效果 */
    margin-left: 12px; /* 添加左侧间距 */
  }

  /* 为anticon添加样式 */
  .anticon {
    display: flex;
    align-items: center;
    height: 70px; /* 设置合适的高度 */
    padding: 8px 0; /* 上下内边距 */
    margin: 51px 1; /* 上下外边距 */
  }
</style>
