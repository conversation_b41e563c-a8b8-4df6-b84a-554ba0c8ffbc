<template>
  <div class="work-experience-container">
    <div class="work-experience-detail-form">
      <div class="work-experience-form">
        <div class="form-header">
          <h2 class="title">职业技能等级评价申报从事本职业(工种)年限说明</h2>

          <h2 class="subtitle">【电子档和纸质版 本都要提交】 </h2>
        </div>
        <div class="form-content">
          <div class="form-description">
            <p>
              &nbsp;&nbsp;&nbsp;&nbsp;兹有 <span class="highlight-text" v-if="formData.name">{{ formData.name }}</span> <span v-else>____</span> 同志（身份证号
              <span class="highlight-text" v-if="formData.idNo">{{ formData.idNo }}</span> <span v-else>____</span>）， 从<span
                class="highlight-text"
                v-if="formData.startYear && formData.startMonth">
                {{ formData.startYear }} 年 {{ formData.startMonth }} 月</span
              ><span v-else>____ 年 ____ 月</span> 到
              <span class="highlight-text" v-if="formData.endYear && formData.endMonth">{{ formData.endYear }} 年 {{ formData.endMonth }} 月</span
              ><span v-else>____ 年 ____ 月</span> 在本单位 <span class="highlight-text" v-if="formData.department">{{ formData.department }}</span
              ><span v-else>____</span> 部门从事 <span class="highlight-text" v-if="formData.departmentPost">{{ formData.departmentPost }}</span
              ><span v-else>____</span> 工作， 累计已满 <span class="highlight-text" v-if="formData.duration">{{ formData.duration }}</span
              ><span v-else>____</span> 年以上。 本单位对以上信息承诺其真实性，如有虚假，由此引发的一切后果由本单位负责。
            </p>
          </div>
          <div class="form-footer">
            <div class="left-aligned">
              <div class="form-item" style="flex-wrap: nowrap">
                <div class="form-label" style="white-space: nowrap">单位工商注册号或组织机构代码证编号：</div>
                <div class="form-value">{{ formData.unitCode }}</div>
              </div>
              <div class="form-item">
                <div class="form-label">单位联系电话：</div>
                <div class="form-value">{{ formData.unitPhone }}</div>
              </div>
              <div class="form-item">
                <div class="form-label">经办人（签名）：</div>
                <div class="form-value">{{ formData.handler }}</div>
              </div>
            </div>
            <div class="right-aligned">
              <div class="form-item">
                <div class="form-label">单位（或单位人事部门）盖章：</div>
                <div class="form-value"> </div>
              </div>
              <div class="form-item">
                <div class="form-value">
                  <div class="date-section">
                    <span class="date-value">{{ formData.year }}</span> 年 <span class="date-value">{{ formData.month }}</span> 月
                    <span class="date-value">{{ formData.day }}</span> 日
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="pdf-viewer">
      <PdfViewer :file="appFile" :min-height="1200" :show-title="false" />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { useGlobSetting } from '@/hooks/setting';
  import { getWorkExperienceFormInfoAsync } from '.';
  import { PdfViewer } from '../pdfViewer';

  const globSetting = useGlobSetting();
  const apiUrl = ref(globSetting.apiUrl);

  const emit = defineEmits(['changeLoading', 'next', 'update:auditState', 'update:semesterCatogry']);
  const changeLoading = loading => {
    emit('changeLoading', loading);
  };
  const formData = ref<any>({});

  const appFile = computed(() => {
    if (formData.value && formData.value.describe && formData.value.describe[0].url) {
      return formData.value.describe[0];
    }
    return null;
  });
  function init(data) {
    changeLoading(true);
    formData.value = {};

    getWorkExperienceFormInfoAsync(data.id)
      .then(res => {
        formData.value = res.data;
        formData.value.startYear = formData.value.workStart ? new Date(formData.value.workStart).getFullYear() : '';
        formData.value.startMonth = formData.value.workStart ? new Date(formData.value.workStart).getMonth() + 1 : '';
        formData.value.endYear = formData.value.workEnd ? new Date(formData.value.workEnd).getFullYear() : '';
        formData.value.endMonth = formData.value.workEnd ? new Date(formData.value.workEnd).getMonth() + 1 : '';

        changeLoading(false);
      })
      .catch(err => {
        changeLoading(false);
      });
  }

  defineExpose({
    init,
  });
</script>

<style lang="less" scoped>
  .work-experience-container {
    display: flex;
    width: 100%;
    min-height: 80vh;

    .work-experience-detail-form {
      flex: 1;
      padding-right: 20px;
    }

    .pdf-viewer {
      flex: 1;
      min-width: 400px;
    }
  }

  .work-experience-form {
    padding: 20px;
    font-family: 'Microsoft YaHei', sans-serif;
    color: #333;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  }

  .form-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
  }

  .title {
    font-size: 20px;
    font-weight: bold;
    color: #333;
    margin: 5px 0;
  }

  .subtitle {
    font-size: 20px;
    font-weight: bold;
    color: #ff0000;
    margin: 5px 0;
  }

  .form-content {
    padding: 0 15px;
  }

  .form-description {
    margin-bottom: 30px;
    text-align: justify;
    border-radius: 8px;
    padding: 20px;
    line-height: 2;
    font-size: 16px;
  }

  .highlight-text {
    font-weight: 600;
    padding: 0 3px;
    text-decoration: underline;
  }

  .form-footer {
    margin-top: 40px;
    display: flex;
    flex-direction: column;
    font-size: 16px;
    font-family: 'Microsoft YaHei', sans-serif;
  }

  .form-item {
    display: flex;
    margin-bottom: 15px;
    align-items: center;
  }

  .left-aligned {
    align-self: flex-end;
    width: 100%;
  }

  .right-aligned {
    align-self: flex-end;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-top: 30px;
  }

  .right-aligned .form-item {
    width: 60%;
    align-items: flex-start;
    text-align: left;
  }

  .form-label {
    flex: 0 0 250px;
    text-align: right;
    padding-right: 15px;
    color: #666;
    font-size: 16px;
  }

  .form-value {
    flex: 1;
    padding: 5px 0;
    min-height: 24px;
    font-size: 16px;
  }

  .date-section {
    padding: 5px 0;
  }

  .stamp-area {
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px dashed #d9d9d9;
    color: #999;
    border-radius: 4px;
    margin: 10px 0;
  }

  .date-value {
    display: inline-block;
    min-width: 30px;
    text-align: center;
    font-weight: 600;
  }
</style>
