<template>
  <div class="xunda-content-wrapper">
    <div class="xunda-content-wrapper-left" v-if="!hideSemester">
      <div class="xunda-content-wrapper-center">
        <BasicTable @register="registerSemesterTable" ref="semesterTableRef" />
      </div>
    </div>
    <div class="xunda-content-wrapper-center">
      <div class="xunda-content-wrapper-search-box">
        <BasicForm
          @register="registerSearchForm"
          :model="searchInfo"
          :schemas="searchSchemas"
          @advanced-change="redoHeight"
          @submit="handleSearchSubmit"
          @reset="handleSearchReset"
          class="search-form" />
      </div>
      <div class="xunda-content-wrapper-content bg-white">
        <BasicTable @register="registerTable" ref="tableRef">
          <template #tableTitle>
            <a-button
              type="link"
              v-if="false"
              preIcon="icon-ym icon-ym-btn-download"
              @click="openExportModal(true, { columnList: exportColumnList, selectIds: getSelectRowKeys() })">
              {{ t('common.exportText', '导出') }}
            </a-button>
            <a-button
              type="link"
              v-if="false"
              preIcon="icon-ym icon-ym-btn-upload"
              @click="openImportModal(true, { url: '/study/registration', menuId: searchInfo.menuId })">
              {{ t('common.importText', '导入') }}
            </a-button>

            <!-- 批量下载按钮组 -->
            <a-dropdown>
              <a-button type="primary">
                批量下载所有
                <down-outlined />
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item key="exportAttachments" @click="handleBatchExport('attachments', 'all')">批量导出附件</a-menu-item>
                  <a-menu-item key="exportFirst" @click="handleBatchExport('first', 'all')" v-if="semesterCategory === '0002'">批量导出第一套</a-menu-item>
                  <a-menu-item key="exportSecond" @click="handleBatchExport('second', 'all')" v-if="semesterCategory === '0002'">批量导出第二套</a-menu-item>
                  <a-menu-item key="exportAll" @click="handleBatchExport('all', 'all')" v-if="semesterCategory === '0002'">批量导出全套</a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
            <a-dropdown>
              <a-button type="primary">
                批量下载所选
                <down-outlined />
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item key="exportAttachments" @click="handleBatchExport('attachments', 'selected')">批量导出附件</a-menu-item>
                  <a-menu-item key="exportFirst" @click="handleBatchExport('first', 'selected')" v-if="semesterCategory === '0002'">批量导出第一套</a-menu-item>
                  <a-menu-item key="exportSecond" @click="handleBatchExport('second', 'selected')" v-if="semesterCategory === '0002'"
                    >批量导出第二套</a-menu-item
                  >
                  <a-menu-item key="exportAll" @click="handleBatchExport('all', 'selected')" v-if="semesterCategory === '0002'">批量导出全套</a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
            <CustomUpload
              accept=".pdf"
              buttonText="批量上传附件"
              tipContent="只能上传PDF文件，单个文件大小不超过5MB"
              :fileSize="5"
              :multiple="true"
              sizeUnit="MB"
              customUploadUrl="/api/study/registration/batchUploaderAttachment"
              :uploadParams="{ acceptType: 'pdf' }"
              @success="handleUploadSuccess"
              @error="handleUploadError"
              :showFileList="false" />
            <a-dropdown v-if="false">
              <a-button type="primary"> 批量上传附件 <upload-outlined /> </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <CustomUpload
                      accept=".zip"
                      buttonText="批量上传附件（zip格式）"
                      tipContent="只能上传zip文件，单个文件大小不超过10MB"
                      :fileSize="1"
                      :multiple="true"
                      sizeUnit="MB"
                      customUploadUrl="/api/study/registration/batchUploaderAttachment"
                      :uploadParams="{ acceptType: 'zip' }"
                      @success="handleUploadSuccess"
                      @error="handleUploadError"
                      :showFileList="false" />
                  </a-menu-item>
                  <a-menu-item>
                    <CustomUpload
                      accept=".pdf"
                      buttonText="批量上传附件（PDF格式）"
                      tipContent="只能上传PDF文件，单个文件大小不超过5MB"
                      :fileSize="5"
                      :multiple="true"
                      sizeUnit="MB"
                      customUploadUrl="/api/study/registration/batchUploaderAttachment"
                      :uploadParams="{ acceptType: 'pdf' }"
                      @success="handleUploadSuccess"
                      @error="handleUploadError"
                      :showFileList="false" />
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>

            <a-button type="link" preIcon="icon-ym icon-ym-btn-clearn" @click="handleBatchRemove">
              {{ t('common.batchDelText', '批量删除') }}
            </a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'shouldPay'">
              <p>{{ xundaUtils.numtoAmount(record.shouldPay ?? 0, '元') }}</p>
            </template>
            <template v-if="column.key === 'payIn'">
              <p>{{ xundaUtils.numtoAmount(record.payIn ?? 0, '元') }}</p>
            </template>
            <template v-if="column.key === 'payWait'">
              <p>{{ xundaUtils.numtoAmount(record.payWait ?? 0, '元') }}</p>
            </template>
            <template v-if="column.key === 'state'">
              <p>{{ record.state }}</p>
            </template>
            <template v-if="column.key === 'attachment'">
              <div v-if="record.personalFile != null" class="file-actions">
                <p>附件已上传</p>
                <div v-if="false">
                  <a-button type="link" @click="handlePreviewAttachment(record.personalFile)">
                    <EyeOutlined />
                  </a-button>
                  <a-button type="link" @click="handleDownloadAttachment(record.personalFile)">
                    <DownloadOutlined />
                  </a-button>
                </div>
              </div>
              <div v-else>
                <p>未上传附件</p>
                <CustomUpload
                  v-if="false"
                  accept=".pdf"
                  buttonText="上传附件"
                  tipText="只能上传PDF文件，单个文件大小不超过5MB"
                  :fileSize="5"
                  sizeUnit="MB"
                  :showFileList="false"
                  :customUploadUrl="'/api/study/registration/uploaderAttachment'"
                  :uploadParams="{ id: record.id }"
                  @success="reload"
                  size="small" />
              </div>
            </template>
            <template v-if="column.key === 'action'">
              <TableAction
                :actions="[
                  {
                    label: '审核',
                    onClick: handleAudit.bind(null, record),
                    color: 'success',
                    ifShow: record.state === '待审核', // 只有待审核状态才显示审核按钮
                  },
                  {
                    label: '查看',
                    onClick: handleAudit.bind(null, record),
                    color: 'success',
                    ifShow: record.state === '审核通过' || record.state === '完成',
                  },
                  {
                    label: '附件',
                    onClick: handleAttachment.bind(null, record),
                    ifShow:
                      (record.state === '审核通过' || record.state === '完成') && (record.semesterCategory === '0001' || record.semesterCategory === '0002'), // 只有已审核状态且符合类别条件才显示附件按钮
                  },
                  {
                    label: '费用',
                    onClick: handleFee.bind(null, record),
                    color: 'warning',
                    ifShow: record.state === '审核通过' || record.state === '完成', // 只有已审核状态才显示费用按钮
                  },
                  {
                    label: t('common.editText', '编辑'),
                    ifShow: false,
                    onClick: handleEdit.bind(null, record),
                  },
                  {
                    label: t('common.delText', '删除'),
                    color: 'error',
                    modelConfirm: { onOk: handleDelete.bind(null, record.id) },
                  },
                  {
                    label: t('common.detailText', '详情'),
                    ifShow: false,
                    onClick: handleDetail.bind(null, record),
                  },
                ]" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form ref="formRef" @reload="reload" />
    <Detail ref="detailRef" @reload="reload" />
    <ExportModal @register="registerExportModal" @download="handleExport" />
    <ImportModal @register="registerImportModal" @reload="reload" />
    <!-- 审核组件 -->
    <audit-form @register="registerAuditForm" @reload="reload" />
    <fee-model @register="registerFeeModel" @reload="reload" />
    <attachment-model @register="registerAttachmentModel" @reload="reload" />
  </div>
</template>

<script lang="ts" setup>
  /**
   * 学生报名注册管理页面
   * 提供选课学期、报名管理、审核等功能
   */
  import {
    getList,
    batchDelete,
    exportData,
    columns,
    searchSchemas,
    getSemesterList,
    semesterColumns,
    semesterSearchSchemas,
    batchDownloadAsync,
  } from '@/views/study/registration';
  import AuditForm from './auditForm.vue';
  import { ref, reactive, onMounted, computed, unref, nextTick } from 'vue';
  import { useModal } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { BasicForm, useForm } from '@/components/Form';
  import { BasicTable, useTable, TableAction, TableActionType } from '@/components/Table';
  import { EyeOutlined, DownloadOutlined, DownOutlined, UploadOutlined } from '@ant-design/icons-vue';
  import Form from '@/views/study/registration/form.vue';
  import Detail from '@/views/study/registration/detail.vue';
  import { useRoute } from 'vue-router';
  import { cloneDeep } from 'lodash-es';
  import { ExportModal } from '@/components/CommonModal';
  import { downloadByUrl } from '@/utils/file/download';
  import { ImportModal } from '@/components/CommonModal';
  import CustomUpload from './components/CustomUpload/index.vue';
  import { isArray } from '../../../utils/is';
  import { xundaUtils } from '@/utils/xunda';
  import FeeModel from '@/views/study/registration/components/feeConfigForm/feeModel.vue';
  import AttachmentModel from '@/views/study/registration/components/personalElectronicDocuments/model.vue';

  const route = useRoute();
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();

  // Modal相关注册
  const [registerExportModal, { openModal: openExportModal, closeModal: closeExportModal, setModalProps: setExportModalProps }] = useModal();
  const [registerImportModal, { openModal: openImportModal }] = useModal();

  const viewType = ref('');

  // 组件引用
  const formRef = ref<any>(null);
  const tableRef = ref<Nullable<TableActionType>>(null);
  const detailRef = ref<any>(null);
  const cacheList = ref<any>([]);

  const props = defineProps({
    auditFlag: {
      type: Boolean,
      default: null,
    },
  });
  const auditFlag = computed(() => props.auditFlag);

  // 搜索相关配置
  const defaultSearchInfo = {
    menuId: route.meta.modelId as string,
    moduleId: '***********', // 模块ID
    superQueryJson: '',
    auditFlag: auditFlag.value,
    dataType: 0,
  };

  const exportColumnList = computed(() => {
    return columns.map(column => ({
      id: column.dataIndex,
      key: column.dataIndex,
      value: column.dataIndex,
      fullName: column.title,
      __config__: {
        xundakey: 'text',
      },
    }));
  });

  const searchInfo = reactive({
    ...cloneDeep(defaultSearchInfo),
  });

  // 学期选择相关
  const semesterId = ref('');
  const semesterCategory = ref('');
  const semesterTableRef = ref(null);
  const hideSemester = ref(false);
  // 学期搜索表单配置
  const [registerSemesterSearchForm] = useForm({
    baseColProps: { span: 6 },
    showActionButtonGroup: true,
    showAdvancedButton: true,
    compact: true,
  });

  // 学期表格配置
  const [registerSemesterTable] = useTable({
    api: getSemesterList,
    columns: semesterColumns,
    searchInfo,
    useSearchForm: true,
    clearSelectOnPageChange: true,
    beforeFetch: data => {
      // data.auditFlag = auditFlag.value;
      return data;
    },
    formConfig: {
      schemas: semesterSearchSchemas,
      baseColProps: { span: 18 },
      showActionButtonGroup: true,
      autoAdvancedLine: 1,
      showAdvancedButton: false,
      showResetButton: false,
    },
    clickToRowSelect: true,
    rowSelection: {
      type: 'radio',
      onChange: (selectedRowKeys, selectedRows) => {
        semesterId.value = selectedRowKeys[0] as string;
        if (selectedRows.length > 0) semesterCategory.value = selectedRows[0].category;
        else semesterCategory.value = '';
        reload();
      },
    },
  });

  // 搜索表单配置
  const [registerSearchForm, { setFieldsValue, resetFields, submit }] = useForm({
    baseColProps: { span: 6 },
    showActionButtonGroup: true,
    showAdvancedButton: false,
    compact: true,
  });

  const registrationParams = ref<any>({});

  const registrationColumns = computed(() => {
    if (viewType.value != 'payIn' && viewType.value != 'payWait' && viewType.value != 'shouldPay') {
      return columns.filter(item => item.dataIndex !== 'shouldPay' && item.dataIndex !== 'payIn' && item.dataIndex !== 'payWait');
    }
    return columns;
  });

  // 主数据表格配置
  const [registerTable, { reload, setLoading, getFetchParams, redoHeight, getSelectRowKeys, clearSelectedRowKeys, setProps, setColumns }] = useTable({
    api: getList,
    columns: registrationColumns.value,
    searchInfo,
    immediate: false,
    clickToRowSelect: false,
    beforeFetch: params => {
      params.semesterId = unref(semesterId);
      registrationParams.value = params;
      return params;
    },
    afterFetch: data => {
      const list = data.map(item => ({ ...item }));
      cacheList.value = cloneDeep(list);
      return list;
    },
    actionColumn: {
      width: 200,
      title: t('common.actionText', '操作'),
      dataIndex: 'action',
      fixed: 'right',
    },
    rowSelection: {
      type: 'checkbox',
      getCheckboxProps: record => ({
        disabled: record.top,
      }),
    },
  });

  /**
   * 搜索相关处理方法
   */
  function handleSearchReset() {
    clearSelectedRowKeys();
  }

  function handleSearchSubmit(data) {
    clearSelectedRowKeys();
    const obj = {
      ...defaultSearchInfo,
      superQueryJson: searchInfo.superQueryJson,
      ...data,
    };

    // 清空并重新设置搜索条件
    Object.keys(searchInfo).forEach(key => {
      delete searchInfo[key];
    });

    for (const [key, value] of Object.entries(obj)) {
      searchInfo[key.replaceAll('-', '_')] = value;
    }

    reload({ page: 1 });
  }

  /**
   * 数据操作方法
   */
  // 编辑
  function handleEdit(record) {
    const data = {
      id: record.id,
      menuId: searchInfo.menuId,
      allList: cacheList.value,
    };
    formRef.value?.init(data);
  }

  // 删除
  function handleDelete(id) {
    const query = { ids: [id] };
    batchDelete(query).then(res => {
      createMessage.success(res.msg);
      clearSelectedRowKeys();
      reload();
    });
  }

  // 查看详情
  function handleDetail(record) {
    const data = {
      id: record.id,
    };
    detailRef.value?.init(data);
  }

  // 导出
  function handleExport(data) {
    const query = { ...getFetchParams(), ...data };
    exportData(query)
      .then(res => {
        setExportModalProps({ confirmLoading: false });
        if (!res.data.url) return;
        downloadByUrl({ url: res.data.url });
        closeExportModal();
      })
      .catch(() => {
        setExportModalProps({ confirmLoading: false });
      });
  }

  // 批量删除
  function handleBatchRemove() {
    const ids = getSelectRowKeys();
    if (!ids.length) return createMessage.error('请选择至少一条数据');
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要删除这些数据吗, 是否继续?',
      onOk: () => {
        const query = { ids };
        setLoading(true);
        batchDelete(query).then(res => {
          createMessage.success(res.msg);
          clearSelectedRowKeys();
          reload();
        });
      },
    });
  }

  // 打开审核弹窗
  const handleAudit = record => {
    openModal(true, { record, registrationParams: registrationParams.value });
  };

  // 批量导出各类套件
  function handleBatchExport(type, selectedFlag) {
    let downloadType = '';
    let title = '';
    switch (type) {
      case 'first':
        title = '批量导出第一套';
        downloadType = 'diyitao';
        break;
      case 'second':
        title = '批量导出第二套';
        downloadType = 'diertao';
        break;
      case 'all':
        title = '批量导出全套';
        downloadType = 'quantao';
        break;
      case 'attachments':
        downloadType = 'diyizhong';
        title = '批量导出附件';
        break;
      default:
        title = '批量导出';
    }

    var selectedIds = [];
    if (selectedFlag == 'all') {
      title += '（全部）';
    }
    if (selectedFlag == 'selected') {
      title += '（选择）';
      var selectedIds = getSelectRowKeys();
      if (!selectedIds || selectedIds.length === 0) {
        createMessage.warning('请选择需要导出的学员数据');
        return;
      }
    }
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: `您确定要${title}吗, 是否继续?`,
      onOk: async () => {
        setLoading(true);
        try {
          const query = {
            ids: selectedIds,
            exportType: type,
            selectedFlag: selectedFlag,
            ...getFetchParams(),
          };
          const res = await batchDownloadAsync(query);
          if (res.data && res.data.url) {
            await downloadByUrl({ url: res.data.url, fileName: res.data.name || `${title}-${new Date().getTime()}.zip` });
          }
        } catch (error) {
        } finally {
          setLoading(false);
        }
      },
    });
  }

  // 处理附件上传成功
  function handleUploadSuccess(data) {
    createMessage.success('上传成功', data);
    reload(); // 刷新表格数据
  }

  function handleUploadError(e) {
    var data = JSON.parse(e.data?.toString() || '');
    const { createErrorModal } = useMessage();
    if (isArray(data)) {
      createErrorModal({
        title: '上传失败',
        content: data.join('<br>'),
      });
    } else {
      createErrorModal({
        title: '上传失败',
        content: data,
      });
    }
  }

  async function handleDownloadAttachment(record) {
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要下载该附件吗, 是否继续?',
      onOk: async () => {
        await downloadByUrl({ url: record.url, fileName: record.name });
      },
    });
  }

  // 预览附件
  function handlePreviewAttachment(record) {
    xundaUtils.printFile(record.url);
  }

  // 注册审核表单
  const [registerAuditForm, { openModal }] = useModal();

  const [registerFeeModel, { openModal: openFeeModel }] = useModal();
  function handleFee(record) {
    openFeeModel(true, {
      record,
      title: '缴费信息',
      registrationParams: registrationParams.value,
    });
  }

  const [registerAttachmentModel, { openModal: openAttachmentModel }] = useModal();
  function handleAttachment(record) {
    openAttachmentModel(true, {
      record,
      title: '附件信息',
      registrationParams: registrationParams.value,
    });
  }

  // 生命周期钩子
  onMounted(() => {
    if (route.query?.semesterId) {
      semesterId.value = route.query?.semesterId as string;
      hideSemester.value = true;
      route.meta.title = route.query?.semesterName as string;
      nextTick(() => {
        if (route.query?.type === '1') setFieldsValue({ auditFlag: 1 });
        if (route.query?.type === '2') setFieldsValue({ auditFlag: 0 });
        if (route.query?.type === '3') setFieldsValue({ payFlag: 1 });
        if (route.query?.type === '4') setFieldsValue({ payFlag: 0 });
        viewType.value = route.query?.type as string;
        semesterCategory.value = route.query?.semesterCategory as string;
        setColumns(registrationColumns.value);
        submit();
      });
    } else {
      nextTick(() => {
        setFieldsValue({ auditFlag: auditFlag.value });
        setProps({ immediate: true });
      });
    }
  });
</script>
<style lang="less" scoped>
  .file-actions {
    display: flex;
    align-items: center;

    a {
      margin-right: 8px;
      display: inline-flex;
      align-items: center;

      &:hover {
        color: @primary-color;
        text-decoration: underline;
      }

      .anticon {
        margin-right: 4px;
      }
    }
  }
  .xunda-content-wrapper-left {
    width: 350px;
    background-color: @component-background;
    flex-shrink: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    margin-right: 10px;
    border-radius: 8px;
    overflow: hidden;
  }

  /* 添加响应式调整 */
  @media (max-width: 768px) {
    .xunda-content-wrapper-left {
      width: 100%;
      margin-right: 0;
      margin-bottom: 10px;
    }
  }
</style>
