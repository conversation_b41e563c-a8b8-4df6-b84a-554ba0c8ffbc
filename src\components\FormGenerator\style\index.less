@prefix-cls: ~'@{namespace}-basic-generator';

.@{prefix-cls} {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  .common-board {
    height: 100%;
    border-radius: 8px;
    overflow: hidden;
  }
  .left-board {
    width: 250px;
    flex-shrink: 0;
    background-color: @component-background;
    display: flex;
    flex-direction: column;
    .ant-collapse-header {
      .ant-collapse-header-text {
        font-weight: 600;
      }
    }
    .components-list {
      .ant-collapse-content-box {
        padding: 0 10px;
      }
      .components-part {
        background-color: @component-background;
        border-radius: 4px;
        padding: 10px 10px 0;
        margin-bottom: 10px;

        &:last-child {
          margin-bottom: 0;
        }
      }
      .components-title {
        font-size: 14px;
        line-height: 30px;
        margin-bottom: 10px;
        font-weight: bold;
      }

      .components-draggable {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
      }

      .components-item {
        width: 110px;
        margin-bottom: 10px;
        transition: transform 0ms !important;

        &.disabled {
          .components-body {
            cursor: not-allowed;
            color: @text-color-secondary;
            &:hover {
              color: @text-color-secondary;
              border-color: @border-color-base;
            }
          }
        }
        .components-body {
          padding-left: 8px;
          font-size: 12px;
          height: 36px;
          cursor: move;
          border: 1px solid @border-color-base;
          border-radius: var(--border-radius);
          line-height: 34px;
          display: flex;
          align-items: center;
          color: @text-color;
          span {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          i {
            flex-shrink: 0;
            line-height: 16px;
            height: 16px;
            margin-right: 4px;
          }
          &:hover {
            border: 1px solid @primary-color;
            color: @primary-color;
          }
        }
      }
    }
  }
  .center-board {
    flex: 1;
    margin: 0 10px;
    background-color: @component-background;
    box-sizing: border-box;
  }
  .center-board-main {
    height: calc(100% - 42px);
    overflow: hidden;
    box-sizing: border-box;
    .scrollbar__view {
      padding: 10px;
    }
    .center-board-row {
      & > .ant-form {
        height: calc(100vh - 150px);
        width: 100%;
      }
    }
    .empty-info {
      position: absolute;
      top: 20%;
      left: calc(50% - 250px);
      pointer-events: none;
      &.app-empty-info {
        top: calc(50% - 150px);
        left: calc(50% - 150px);
        .empty-img {
          width: 300px;
          height: 300px;
        }
      }
      .empty-img {
        width: 500px;
        height: 500px;
      }
    }
    .drawing-board {
      height: 100%;
      position: relative;
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
      .components-body {
        padding: 0;
        margin: 0;
        font-size: 0;
      }
      .sortable-ghost {
        position: relative;
        display: block;
        overflow: hidden;
        i {
          display: none;
        }
        &::before {
          content: ' ';
          position: absolute;
          left: 0;
          right: 0;
          top: 0;
          height: 3px;
          background: @primary-color;
          z-index: 2;
        }
        .drawing-item-action {
          display: none !important;
        }
      }
      .components-item.sortable-ghost {
        width: 100%;
        height: 60px;
        background-color: @primary-1;
      }
      .drawing-item {
        position: relative;
        cursor: move;
        .ant-form-item {
          border: 1px dashed @border-color-base;
          padding: 10px;
          margin-bottom: 10px !important;
          border-radius: 8px;
          .common-container {
            .ant-form-item {
              border: unset;
              padding: 0;
              margin-bottom: 0 !important;
              border-radius: 0;
            }
          }
        }
      }
      .drawing-row-item {
        position: relative;
        cursor: move;
        box-sizing: border-box;
        border: 1px dashed @border-color-base;
        padding: 0 2px;
        margin-bottom: 10px;
        border-radius: 8px;
        &.drawing-row-item-table-grid {
          padding-top: 10px;
          padding-bottom: 10px;
        }
        .ant-card {
          width: 100%;
          .ant-card-body {
            & > .ant-row {
              width: 100%;
            }
          }
        }
        .ant-tabs {
          width: 100%;
          .ant-tabs-nav {
            margin-bottom: 0;
          }
        }
        .ant-collapse {
          width: 100%;
        }
        .child-drawing-row {
          position: relative;
        }
        &.drawing-row-item-row {
          position: relative;
          & > .ant-col {
            width: 100%;
          }
          .row-tip {
            top: 50px;
          }
          .drag-wrapper {
            min-height: 100px;
          }
        }
        &.drawing-row-item-table {
          .row-tip {
            top: 50px;
          }
          .ant-form {
            width: 100%;
          }
          .drag-wrapper {
            min-height: 100px;
            padding-top: 30px;
          }
          .table-wrapper-web {
            overflow: auto hidden;
            display: flex;
            width: 100%;
            flex-wrap: nowrap;
            padding-bottom: 10px;

            & > .ant-col {
              width: 200px !important;
              flex: none;
              flex-shrink: 0;
              height: auto;
              .ant-row {
                display: block;
              }
              .ant-form-item {
                margin-bottom: 0 !important;
                .ant-form-item-label {
                  width: auto !important;
                }
              }
            }
          }
        }
        .drawing-row-item {
          margin-bottom: 2px;
        }
        .drag-wrapper {
          width: 100%;
          min-height: 80px;
          display: flex;
          flex-wrap: wrap;
          align-content: flex-start;
          &.tableGrid-app-wrapper {
            .row-tip {
              margin-top: 20px;
            }
          }
        }
        &.active-from-item {
          border: 1px solid @primary-color;
        }
        .component-name {
          position: absolute;
          top: 0;
          left: 0;
          font-size: 18px;
          color: @text-color-secondary;
          display: inline-block;
          padding: 0 6px;
        }
      }
      .drawing-item,
      .drawing-row-item {
        &:hover {
          & > .ant-form-item {
            background: @primary-1;
          }

          & > .drawing-item-action {
            display: flex;
          }
          & > .drawing-item-cell {
            display: block;
          }
        }
        & > .drawing-item-action {
          position: absolute;
          top: -10px;
          height: 24px;
          line-height: 22px;
          border-radius: 24px;
          font-size: 12px;
          border: 1px solid @primary-color;
          background: @component-background;
          cursor: pointer;
          z-index: 1;
          right: var(--rightDistance);
          display: none;
          overflow: hidden;
          .drawing-item-action-item {
            display: block;
            width: 30px;
            height: 22px;
            height: 100%;
            text-align: center;
            position: relative;
            align-items: center;
            justify-content: flex-start;
            color: @text-color-label;
            &.drawing-item-delete {
              color: @text-color-label;
              &:hover {
                background: #ffe5e5;
                color: @error-color;
              }
            }
            &:hover {
              background: @primary-1;
              color: @primary-color;
            }
            &:first-child {
              &::after {
                display: none;
              }
            }
            &::after {
              display: block;
              content: '';
              position: absolute;
              top: 3px;
              left: 0;
              height: 16px;
              width: 1px;
              background-color: @border-color-base1;
            }
          }
        }

        & > .drawing-item-cell {
          display: none;
          position: absolute;
          bottom: 0;
          right: 0;
          width: 22px;
          height: 22px;
          line-height: 22px;
          text-align: center;
          font-size: 12px;
          cursor: pointer;
          z-index: 1;
          color: @component-background;
          background-color: @primary-color;
          i {
            color: #fff;
          }
          .anticon {
            vertical-align: top !important;
            margin-top: 5px;
          }
        }
      }
      .active-from-item {
        & > .ant-form-item {
          background-color: @primary-1;
          border: 1px solid @primary-color;
        }

        & > .drawing-item-action {
          display: flex;
        }
        & > .drawing-item-cell {
          display: block;
        }
        & > .component-name {
          color: @primary-color;
        }
      }
      .row-tip {
        width: 100%;
        color: @text-color-secondary;
        text-align: center;
        position: absolute;
        top: 30px;
        font-size: 14px;
      }
      .table-grid {
        width: 100%;
        text-align: center;
        border-collapse: collapse;
        table-layout: fixed;
        .table-cell {
          min-height: 75px !important;
          overflow: auto;
          padding-top: 10px;
        }
        .drawing-row-item {
          padding: 0 2px;
          text-align: left;
        }
      }
    }
    .ipad {
      height: calc(100% - 42px);
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 711px;

      .outerIpad {
        background: url('../../../assets/images/iphoneBg.png');
        width: 389px;
        height: 711px;
        padding: 65px 40px;

        .ipadBody {
          height: 100%;
          .center-board-row > .ant-form {
            height: 550px !important;
          }
        }
      }
    }
  }
}
html[data-theme='dark'] {
  .@{prefix-cls} {
    .center-board-main .ipad .outerIpad {
      background: url('../../../assets/images/iphoneBg-dark.png');
    }
  }
}
