import { xundaApproverConfig } from './element/approver';
import { xundaStartConfig } from './element/start';
import { xundaEndConfig } from './element/end';
import { xundaSubFlowConfig } from './element/subFlow';
import { xundaLabelConfig } from './element/label';
import { xundaExclusiveConfig } from './element/gateway/exclusive';
import { xundaInclusiveConfig } from './element/gateway/inclusive';
import { xundaParallelConfig } from './element/gateway/parallel';
import {
  bpmnTask,
  bpmnStart,
  bpmnEnd,
  bpmnTimer,
  bpmnSubFlow,
  bpmnLabel,
  bpmnInclusive,
  bpmnParallel,
  bpmnExclusive,
  typeStart,
  typeEnd,
  typeSubFlow,
  typeTimer,
  typeLabel,
  typeGateway,
  typeTask,
  bpmnSequenceFlow,
  bpmnGroup,
  typeGroup,
  bpmnTrigger,
  typeAddData,
  typeGetData,
  typeUpdateData,
  typeDelData,
  typeInterface,
  typeLaunchFlow,
  typeMessage,
  typeSchedule,
  bpmnAddData,
  bpmnGetData,
  bpmnUpdateData,
  bpmnDelData,
  bpmnInterface,
  bpmnLaunchFlow,
  bpmnMessage,
  bpmnSchedule,
  bpmnEvent,
  bpmnNotice,
  bpmnWebhook,
  bpmnTime,
  typeTrigger,
  typeEventTrigger,
  typeWebhookTrigger,
  typeTimeTrigger,
  typeNoticeTrigger,
  bpmnExecute,
  typeExecute,
  bpmnProcessing,
  bpmnChoose,
  typeExclusive,
  typeChoose,
  typeParallel,
  typeInclusion,
} from './variableName';
import { xundaSequenceFlow } from './element/sequenceFlow';
import { xundaGroupConfig } from './element/group';
import { xundaTriggerConfig } from './element/trigger';
import { xundaAddDataConfig } from './element/execute/addData';
import { xundaGetDataConfig } from './element/execute/getData';
import { xundaUpdateDataConfig } from './element/execute/updateData';
import { xundaDelDataConfig } from './element/execute/delData';
import { xundaInterfaceConfig } from './element/execute/interface';
import { xundaLaunchConfig } from './element/execute/launch';
import { xundaMessageConfig } from './element/execute/message';
import { xundaScheduleConfig } from './element/execute/schedule';
import { xundaWebhookConfig } from './element/trigger/webhook';
import { xundaEventConfig } from './element/trigger/event';
import { xundaTimeConfig } from './element/trigger/time';
import { xundaNoticeConfig } from './element/trigger/notice';
import { xundaProcessingConfig } from './element/processing';
import { xundaChooseConfig } from './element/gateway/choose';

const hasLabelElements: any = ['bpmn:StartEvent', 'bpmn:EndEvent', 'bpmn:InclusiveGateway']; // 一开始就有label标签的元素类型
const BpmnBusinessObjectKey = {
  id: 'wnId',
};
const typeConfig: any = {
  [bpmnTask]: xundaApproverConfig,
  [bpmnStart]: xundaStartConfig,
  [bpmnEnd]: xundaEndConfig,
  [bpmnSubFlow]: xundaSubFlowConfig,
  [bpmnLabel]: xundaLabelConfig,
  [bpmnInclusive]: xundaInclusiveConfig,
  [bpmnParallel]: xundaParallelConfig,
  [bpmnExclusive]: xundaExclusiveConfig,
  [bpmnSequenceFlow]: xundaSequenceFlow,
  [bpmnGroup]: xundaGroupConfig,
  [bpmnTrigger]: xundaTriggerConfig,
  [bpmnAddData]: xundaAddDataConfig,
  [bpmnGetData]: xundaGetDataConfig,
  [bpmnUpdateData]: xundaUpdateDataConfig,
  [bpmnDelData]: xundaDelDataConfig,
  [bpmnInterface]: xundaInterfaceConfig,
  [bpmnLaunchFlow]: xundaLaunchConfig,
  [bpmnMessage]: xundaMessageConfig,
  [bpmnSchedule]: xundaScheduleConfig,
  [bpmnEvent]: xundaEventConfig,
  [bpmnTime]: xundaTimeConfig,
  [bpmnNotice]: xundaNoticeConfig,
  [bpmnWebhook]: xundaWebhookConfig,
  [bpmnProcessing]: xundaProcessingConfig,
  [bpmnChoose]: xundaChooseConfig,
};
// 默认wnType值
const conversionWnType: any = {
  [bpmnStart]: typeStart,
  [bpmnEnd]: typeEnd,
  [bpmnTask]: typeTask,
  [bpmnSubFlow]: typeSubFlow,
  [bpmnTimer]: typeTimer,
  [bpmnLabel]: typeLabel,
  [bpmnInclusive]: typeInclusion,
  [bpmnParallel]: typeParallel,
  [bpmnExclusive]: typeExclusive,
  [bpmnGroup]: typeGroup,
};
// 任务节点类型
const changeTypeByTaskShape: any = {
  [typeAddData]: bpmnAddData,
  [typeGetData]: bpmnGetData,
  [typeUpdateData]: bpmnUpdateData,
  [typeDelData]: bpmnDelData,
  [typeInterface]: bpmnInterface,
  [typeLaunchFlow]: bpmnLaunchFlow,
  [typeMessage]: bpmnMessage,
  [typeSchedule]: bpmnSchedule,
};
// 判断是否为触发节点
const triggerTypeChange: any = {
  [bpmnTrigger]: typeTrigger,
  [bpmnEvent]: typeTrigger,
  [bpmnNotice]: typeTrigger,
  [bpmnTime]: typeTrigger,
  [bpmnWebhook]: typeTrigger,
};
const changeTypeByTrigger: any = {
  [typeEventTrigger]: bpmnEvent,
  [typeNoticeTrigger]: bpmnNotice,
  [typeTimeTrigger]: bpmnTime,
  [typeWebhookTrigger]: bpmnWebhook,
};
const hasGatewayType = new Set([typeInclusion, typeParallel, typeExclusive, typeChoose, typeGateway]);

export { typeConfig, BpmnBusinessObjectKey, hasLabelElements, conversionWnType, changeTypeByTaskShape, triggerTypeChange, changeTypeByTrigger, hasGatewayType };
