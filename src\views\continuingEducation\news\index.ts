import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { defHttp } from '@/utils/http/axios';
import { htmlToText } from '@/utils/xunda';

const { t } = useI18n();
// 基础Api
export const planNoticeApi = '/api/plan/notice';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: planNoticeApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: planNoticeApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: planNoticeApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: planNoticeApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: planNoticeApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: planNoticeApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: planNoticeApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: planNoticeApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: planNoticeApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: planNoticeApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: planNoticeApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: planNoticeApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: planNoticeApi + `/getForSelect`, data });
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'title',
    label: t('标题'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
];
/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('标题'),
    dataIndex: 'title',
    sorter: true,
    width: 120,
  },
  {
    title: t('内容'),
    dataIndex: 'content',
    width: 300,
    customRender: ({ value }) => {
      //  <p v-html="record['content']"></p>
      return htmlToText(value);
    },
    ellipsis: true,
  },
  {
    title: t('状态'),
    dataIndex: 'state',
    width: 120,
  },
];
