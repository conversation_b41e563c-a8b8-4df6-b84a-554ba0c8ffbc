import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';

// 基础Api
export const courseExtraApi = '/api/ea/courseExtra';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: courseExtraApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: courseExtraApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: courseExtraApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: courseExtraApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: courseExtraApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: courseExtraApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: courseExtraApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: courseExtraApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: courseExtraApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: courseExtraApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: courseExtraApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: courseExtraApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: courseExtraApi + `/getForSelect`, data });
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('主键'),
    dataIndex: 'id',
    width: 120,
  },
  {
    title: t('课程'),
    dataIndex: 'courseSelfName',
    width: 120,
  },
  {
    title: t('类型'),
    dataIndex: 'extraType',
    width: 120,
  },
  {
    title: t('前置/绑定课程'),
    dataIndex: 'courseExtraName',
    width: 120,
  },
];
