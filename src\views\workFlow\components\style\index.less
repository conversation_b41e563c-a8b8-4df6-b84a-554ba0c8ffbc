.basic-flow-parser {
  .header-title {
    font-size: 16px;
    max-width: 40vw;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .flow-urgent-value {
    line-height: 23px;
    font-size: 14px;
    position: relative;
    cursor: pointer;
    padding-left: 12px;
    &.urgent1 {
      color: #409eff;
      &::before {
        background: #409eff;
      }
    }
    &.urgent2 {
      color: #e6a23c;
      &::before {
        background: #e6a23c;
      }
    }
    &.urgent3 {
      color: #f56c6c;
      &::before {
        background: #f56c6c;
      }
    }
    &::before {
      display: block;
      content: '';
      position: absolute;
      left: 0;
      top: 8px;
      background: #333;
      width: 7px;
      height: 7px;
      border-radius: 50%;
    }
  }
  .header-options {
    .ant-btn {
      margin-left: 10px;
    }
  }
  .header-btn {
    .ant-btn {
      width: 32px;
      padding: 0;
    }
  }
  .approve-result {
    position: absolute;
    right: 10px;
    top: 100px;
    z-index: 100;
    width: 100px;
    height: 100px;
    opacity: 0.7;
    .approve-result-img {
      width: 100%;
      height: 100%;
      background-size: 100% !important;
      &.doing {
        background: url('@/assets/images/flowStatus/doing.png') no-repeat;
      }
      &.adopt {
        background: url('@/assets/images/flowStatus/adopt.png') no-repeat;
      }
      &.reject {
        background: url('@/assets/images/flowStatus/reject.png') no-repeat;
      }
      &.cancel {
        background: url('@/assets/images/flowStatus/cancel.png') no-repeat;
      }
      &.pause {
        background: url('@/assets/images/flowStatus/pause.png') no-repeat;
      }
      &.revoking {
        background: url('@/assets/images/flowStatus/revoking.png') no-repeat;
      }
      &.revoke {
        background: url('@/assets/images/flowStatus/revoke.png') no-repeat;
      }
      &.back {
        background: url('@/assets/images/flowStatus/back.png') no-repeat;
      }
      &.recall {
        background: url('@/assets/images/flowStatus/recall.png') no-repeat;
      }
    }
  }
  .flow-parser-tabs {
    height: 100%;
    &.no-head-margin > .ant-tabs-nav {
      margin-bottom: 0;
    }
    &.ant-tabs-card {
      & > .ant-tabs-nav > .ant-tabs-nav-wrap {
        padding: 0;
        & > .ant-tabs-nav-list {
          & > .ant-tabs-tab + .ant-tabs-tab {
            margin-left: 2px;
          }
        }
      }
      & > .ant-tabs-content-holder > .ant-tabs-content {
        & > .ant-tabs-tabpane {
          padding: 0;
        }
      }
    }
    & > .ant-tabs-nav > .ant-tabs-nav-wrap {
      padding: 0 20px;
      & > .ant-tabs-nav-list {
        & > .ant-tabs-tab + .ant-tabs-tab {
          margin-left: 40px;
        }
      }
    }
    & > .ant-tabs-content-holder > .ant-tabs-content {
      height: 100%;
      overflow: hidden;
      & > .ant-tabs-tabpane {
        height: 100%;
        overflow: auto;
        padding: 0 10px 10px;
        box-sizing: border-box;
      }
    }
  }
  .flow-parser-container {
    height: 100%;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    .flow-top-container {
      height: 42px;
      display: flex;
      align-items: center;
      flex-shrink: 0;
      border-bottom: 1px solid @border-color-base;
      padding: 0 10px;
      .item {
        width: 25%;
        display: flex;
        align-items: center;
        white-space: nowrap;
        color: @text-color-base;
        .content {
          margin-left: 8px;
        }
        .content,
        .text {
          flex: 1;
          line-height: 42px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          padding-right: 12px;
        }
      }
    }
    .flow-form-container {
      flex: 1;
      overflow-x: hidden;
      overflow-y: auto;
      padding: 10px 10px 0;
    }
    .flow-approval-form {
      border-top: 1px solid @border-color-base;
      width: 100%;
      background-color: @component-background;
      z-index: 99;
      position: relative;
      .opinion-btn {
        position: relative;
        padding: 20px 17.5px 0;
        cursor: pointer;
        .cover-btn {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
        }
      }
      .ant-drawer-content-wrapper {
        box-shadow: unset;
        border-top: 1px solid @border-color-base;
        .approval-form {
          padding: 10px 15px;
          background-color: @component-background;
          height: 100%;
          overflow-y: auto;
          .arrow-icon {
            position: absolute;
            font-size: 16px;
            padding: 10px;
            top: 5px;
            right: 7.5px;
            cursor: pointer;
          }
        }
        .ant-drawer-body {
          padding: 0;
        }
      }
    }
    .flow-btn-container {
      flex-shrink: 0;
      display: flex;
      height: 60px;
      align-items: center;
      justify-content: space-between;
      padding: 0 10px;
      border-top: 1px solid @border-color-base;
      box-sizing: border-box;
      background-color: @component-background;
      z-index: 1500;
      .flow-btn {
        flex-shrink: 0;
        .ant-btn {
          margin-left: 10px;
        }
      }
    }
  }
  .process-flow-container {
    .scale-slider {
      right: 10px;
    }
  }
}
.flow-file {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 10001;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(240, 242, 245, 0.4);
}
.task-flow-status {
  display: flex;
  align-items: center;
  flex: 1;
  .icon {
    width: 28px;
    height: 28px;
    display: inline-block;
    border-radius: 50%;
    padding: 2px;
    text-align: center;
    color: #fff;
    transform: scale(0.6);
  }
  .icon-ym-fail {
    background-color: #ff4d4d;
  }
  .icon-ym-success {
    background-color: #55d187;
  }
}
.stop-status {
  .icon-ym-fail,
  .icon-ym-success {
    background-color: #cbcbcc !important;
  }
}
html[data-theme='dark'] {
  .flow-file {
    background-color: rgba(0, 0, 0, 0.45);
  }
}
