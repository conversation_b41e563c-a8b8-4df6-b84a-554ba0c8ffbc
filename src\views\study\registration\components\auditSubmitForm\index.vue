<template>
  <div class="plan-result">
    <div class="status-content">
      <template v-if="auditSuccess">
        <div class="result-card success-card">
          <a-result status="success" :title="getStatusTitle" sub-title=" ">
            <template #extra>
              <div class="action-wrapper">
                <a-button type="error" class="action-button danger" @click="handleReSetRemark">
                  <span>撤销通过</span>
                </a-button>
              </div>
            </template>
          </a-result>
        </div>
      </template>

      <template v-if="state === '审核不通过'">
        <div class="result-card error-card">
          <a-result status="error" :title="getStatusTitle" sub-title="审核不通过">
            <template #extra>
              <div class="action-wrapper">
                <a-button type="primary" class="action-button" @click="handlePrev">
                  <span>上一步</span>
                </a-button>
                <a-button v-if="state === '审核不通过'" type="success" class="action-button success" @click="handleReAudit">
                  <SyncOutlined />
                  <span>重新审核</span>
                </a-button>
              </div>
            </template>
          </a-result>
        </div>
      </template>

      <template v-if="state === '待审核' || canNotAudit">
        <div class="result-card pending-card">
          <a-result status="warning" :title="getStatusTitle" sub-title="待审核">
            <template #extra>
              <div class="status-message" v-if="state === '电子档上传' || state === '填写资料'">
                <HourglassOutlined class="status-icon" />
                <span>{{ state === '电子档上传' ? '等待学员上传电子档资料' : '等待学员填写资料' }}</span>
              </div>
            </template>
          </a-result>
        </div>

        <a-form ref="formRef" :model="formState" :rules="rules" layout="vertical" class="audit-form">
          <div class="form-header">
            <h3>审核信息</h3>
            <div class="form-divider"></div>
          </div>

          <a-row :gutter="24">
            <a-col :span="24">
              <a-form-item label="审核结果" name="state" required>
                <a-radio-group v-model:value="formState.auditFlag" class="audit-radio-group">
                  <a-radio :value="1" v-if="!canNotAudit" class="custom-radio success">
                    <CheckCircleOutlined />
                    <span>通过</span>
                  </a-radio>
                  <a-radio :value="0" class="custom-radio danger">
                    <CloseCircleOutlined />
                    <span>不通过</span>
                  </a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="审核意见" name="remark">
                <a-textarea
                  v-model:value="formState.remark"
                  :rows="4"
                  placeholder="请输入审核意见"
                  class="custom-textarea"
                  :class="{ 'error-input': formState.auditFlag === 0 }" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="备注" name="note">
                <a-textarea v-model:value="formState.note" :rows="4" placeholder="请输入备注信息（选填）" class="custom-textarea" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>

        <div class="action-wrapper">
          <a-button type="primary" class="action-button" @click="handlePrev">
            <LeftOutlined />
            <span>上一步</span>
          </a-button>
          <a-button type="primary" class="action-button submit" @click="handleSetRemark">
            <CheckOutlined />
            <span>提交审核</span>
          </a-button>
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed, reactive, ref, toRefs } from 'vue';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useMessage } from '@/hooks/web/useMessage';
  import AResult from 'ant-design-vue/es/result';
  import { setRemarkAsync, getRemarkAsync, sendMessage } from '.';
  import { FormActionType } from '@/components/Form';
  import {
    LeftOutlined,
    CheckOutlined,
    SyncOutlined,
    RollbackOutlined,
    HourglassOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
    ExclamationCircleOutlined,
  } from '@ant-design/icons-vue';

  const emit = defineEmits(['prev', 'finish', 'changeLoading', 'update:auditState']);

  const props = defineProps({
    registrationId: {
      type: String,
      default: '',
    },
    auditState: { type: String, default: '' },
  });

  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const formRef = ref<FormActionType>();

  const auditSuccess = computed(() => {
    if (state.value === '完成') return true;
    if (state.value === '待支付') return true;
    if (state.value === '完成') return true;
    return false;
  });
  const canNotAudit = computed(() => {
    if (state.value === '选择计划') return true;
    if (state.value === '填写资料') return true;
    if (state.value === '资料上传') return true;
    if (state.value === '电子档上传') return true;
    return false;
  });

  interface FormState {
    id: string;
    state: string;
    remark: string;
    auditFlag: number;
    note: string;
  }

  const formState = reactive<FormState>({
    id: '',
    state: '',
    remark: '',
    auditFlag: 0,
    note: '',
  });
  const { state, auditFlag } = toRefs(formState);
  const rules = computed(() => {
    return {
      state: [{ required: true, message: '请选择审核结果' }],
      remark: [{ required: !formState.auditFlag ? true : false, message: '请输入审核意见' }],
    };
  });

  function init(data) {
    formState.id = props.registrationId;
    handleGetRemark(formState.id);
  }

  // 获取审核情况
  async function handleGetRemark(id) {
    try {
      emit('changeLoading', true);
      const res = await getRemarkAsync(id);
      const { id: resId, state, remark, note } = res.data;
      emit('update:auditState', state);
      // 更新表单状态
      Object.assign(formState, {
        id: resId,
        state,
        remark,
        note,
        auditFlag: state === '审核不通过' ? 0 : formState.auditFlag,
      });
    } catch (error) {
      createMessage.error('获取审核信息失败');
    } finally {
      emit('changeLoading', false);
    }
  }

  const handlePrev = () => {
    emit('prev');
  };

  // 显示操作确认对话框
  const showConfirmDialog = async (title: string, content: string, onConfirm: () => Promise<void>) => {
    createConfirm({
      iconType: 'warning',
      title: t(title),
      content,
      onOk: async () => {
        try {
          emit('changeLoading', true);
          await onConfirm();
        } catch (error) {
          createMessage.error('操作失败');
        } finally {
          emit('changeLoading', false);
        }
      },
    });
  };

  // 撤销审核结果
  async function handleReSetRemark() {
    await showConfirmDialog('common.tipTitle', '确认撤销之前该学员的审核结果吗?', async () => {
      await setRemarkAsync({
        ids: [formState.id],
        state: '待审核',
        remark: '管理员撤销审核',
        note: formState.note,
      });
      await handleGetRemark(formState.id);
    });
  }
  const handleSetRemark = async () => {
    const values = await formRef.value?.validate();
    if (!values) return;
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '确认提交该学员的审核结果吗?',
      onOk: () => {
        emit('changeLoading', true);
        var state = '审核不通过';
        if (formState.auditFlag === 1) {
          state = '完成';
        }
        setRemarkAsync({
          ids: [formState.id],
          state: state,
          remark: formState.remark,
          note: formState.note,
        })
          .then(res => {
            handleGetRemark(formState.id);
            emit('finish');
            sendMessage({ ids: res.data.messageIds }).then(res => {});
          })
          .catch(() => {
            emit('changeLoading', false);
          });
      },
      onCancel: () => {
        console.log('Cancel');
      },
    });
  };

  // 重新审核
  async function handleReAudit() {
    await showConfirmDialog('common.tipTitle', '确认重新审核该学员的报名吗?', async () => {
      await setRemarkAsync({
        ids: [formState.id],
        state: '待审核',
        remark: '管理员重新审核',
        note: formState.note,
      });
      createMessage.success('提交成功');
      await handleGetRemark(formState.id);
      emit('finish');
    });
  }

  // 添加状态标题计算属性
  const getStatusTitle = computed(() => {
    if (auditSuccess.value) return '审核已通过';
    if (state.value === '审核不通过') return '审核未通过';
    if (state.value === '待审核') return '等待审核';
    return state.value;
  });

  defineExpose({
    init,
  });
</script>

<style lang="less" scoped>
  .plan-result {
    width: 100%;
    padding: 32px;

    .custom-icon-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 72px;
      height: 72px;
      border-radius: 50%;
      margin-bottom: 16px;
      transition: all 0.3s ease;

      &.success {
        background-color: rgba(82, 196, 26, 0.1);
        border: 2px solid #52c41a;
        color: #52c41a;
      }

      &.error {
        background-color: rgba(245, 34, 45, 0.1);
        border: 2px solid #f5222d;
        color: #f5222d;
      }

      &.warning {
        background-color: rgba(250, 173, 20, 0.1);
        border: 2px solid #faad14;
        color: #faad14;
      }

      &:hover {
        transform: scale(1.05);
      }
    }

    .result-card {
      width: 100%;
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      padding: 24px;
      margin-bottom: 32px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
        transform: translateY(-3px);
      }

      &.success-card {
        border: 1px solid #52c41a;
      }

      &.error-card {
        border: 1px solid #f5222d;
      }

      &.pending-card {
        border: 1px solid #faad14;
      }

      &.info-card {
        border: 1px solid #1890ff;
      }
    }

    .status-content {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 24px 0;
      max-width: 800px;
      margin: 0 auto;

      :deep(.ant-result) {
        width: 100%;
        margin-bottom: 24px;
      }

      :deep(.ant-form-item) {
        margin-bottom: 28px;
      }
    }

    .audit-form {
      width: 100%;
      padding: 24px;
      background: #f9fafb;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

      :deep(.ant-form-item-label) {
        font-weight: 500;
        padding-bottom: 8px;
      }

      :deep(.ant-input) {
        border-radius: 8px;
        padding: 12px;
        &:hover,
        &:focus {
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
        }
      }

      .audit-radio-group {
        display: flex;
        gap: 24px;
        padding: 8px 0;

        :deep(.ant-radio-wrapper) {
          font-size: 15px;
        }
      }
    }

    .action-buttons {
      display: flex;
      gap: 20px;
      justify-content: center;
      margin-top: 32px;

      .ant-btn {
        min-width: 140px;
        height: 44px;
        font-size: 15px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        transition: all 0.3s;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }
</style>
