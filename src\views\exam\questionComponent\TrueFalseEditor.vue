<template>
    <div>
        <!-- 题干 -->
        <a-col :span="24" class="ant-col-item">
            <div class="form-item-wrapper">
                <div class="form-label">题干</div>
                <XundaEditor v-model:value="formData.content" :disabled="false" :allowClear="false" :height="300"
                    :style="{ width: '100%' }" />
            </div>
        </a-col>

        <!-- 答案选择 -->
        <a-col :span="24" class="ant-col-item">
            <div class="form-item-wrapper">
                <div class="form-label">正确答案</div>
                <a-radio-group v-model:value="formData.answer">
                    <a-radio value="1">正确</a-radio>
                    <a-radio value="0">错误</a-radio>
                </a-radio-group>
            </div>
        </a-col>
    </div>
</template>

<script setup>
import { defineExpose } from 'vue'
import { message } from 'ant-design-vue'

// 定义props，接收父组件的dataForm
const props = defineProps({
  formData: {
    type: Object,
    required: true
  }
})

// 直接使用父组件的formData
const formData = props.formData

// 获取数据
const getData = () => {
    return {
        content: formData.content,
        answer: formData.answer,
    }
}

// 校验方法
const validate = () => {
    if (!formData.content || !formData.content.trim()) {
        message.error('题干不能为空')
        return false
    }
    if (formData.answer === null || formData.answer === undefined || formData.answer === '') {
        message.error('请选择正确答案')
        return false
    }
    return true
}

defineExpose({ validate, getData })
</script>

<style scoped>
.ant-col-item {
    margin-bottom: 16px;
}

.form-item-wrapper {
  margin-bottom: 16px;
}

.form-label {
  font-weight: 500;
  margin-bottom: 8px;
  color: rgba(0, 0, 0, 0.85);
}
</style>
