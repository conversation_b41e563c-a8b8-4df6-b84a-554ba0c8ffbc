import { useI18n } from '@/hooks/web/useI18n';
import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();

// 基础Api
export const semesterApi = '/api/plan/semester';
export const noticeApi = '/api/plan/notice';

// 获取(转换数据)
export function getSemesterInfoAsync(id) {
  return defHttp.get({ url: semesterApi + `/detail/` + id });
}

export function getInfo(semesterId) {
  return defHttp.get({ url: noticeApi + `/semester/` + semesterId });
}
// 新建
export function create(data) {
  return defHttp.post({ url: noticeApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: noticeApi + `/` + data.id, data });
}
