import { defHttp } from '@/utils/http/axios';

/**
 * 自定义上传文件接口
 * @param data 上传数据
 * @param params 请求参数
 * @returns API响应
 */
export function customUploadFile(data, params = {}) {
  return defHttp.post(
    {
      url: '/api/custom/upload',
      params,
      data,
    },
    {
      isTransformResponse: false,
      isReturnNativeResponse: false,
    }
  );
}

/**
 * 获取文件下载链接
 * @param fileId 文件ID
 * @param params 请求参数
 * @returns API响应
 */
export function getCustomDownloadUrl(fileId, params = {}) {
  return defHttp.get({
    url: `/api/custom/download/${fileId}`,
    params,
  });
}

/**
 * 批量下载文件
 * @param fileIds 文件ID数组
 * @param params 请求参数
 * @returns API响应
 */
export function getBatchDownloadUrl(fileIds, params = {}) {
  return defHttp.post({
    url: '/api/custom/batchDownload',
    data: { fileIds },
    params,
  });
}

/**
 * 删除文件
 * @param fileId 文件ID
 * @param params 请求参数
 * @returns API响应
 */
export function deleteCustomFile(fileId, params = {}) {
  return defHttp.delete({
    url: `/api/custom/file/${fileId}`,
    params,
  });
}
