<template>
  <div>
    <div class="application-container">
      <div class="application-detail-form">
        <component :is="currentFormComponent" :formData="formData" :certificates="certificates" :apiUrl="apiUrl" @download="downloadAttachment" />
      </div>
      <div class="pdf-viewer" v-if="semesterCatogry === '0001' || semesterCatogry === '0002'">
        <PdfViewer :file="appFile" :fileName="getFileName" :min-height="1200" :showDownloadButton="true" :show-title="false" />
      </div>
    </div>
    <div v-if="semesterCatogry === '0001'">
      <div v-for="certificate in holdCertificates" :key="certificate.id" class="certificate-container">
        <div class="certificate-header">
          <div class="certificate-title"> 证书类型：{{ certificate.type }}</div>
          <div class="certificate-no">编号：{{ certificate.no }}</div>
          <div class="certificate-no">职业（工种）：{{ certificate.name }}</div>
          <div class="certificate-no">证书等级：{{ certificate.level }}</div>
          <div class="certificate-no">发证机构：{{ certificate.organization }}</div>
          <div class="certificate-no">发证日期：{{ xundaUtils.toDateString(certificate.date, 'yyyy-MM-dd') }}</div>
        </div>
        <div class="certificate-content">
          <div class="certificate-image">
            <FileViewer :file="certificate.attachment" :previewMode="true" :show-title="false" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { computed, ref } from 'vue';
  import { getApplicationFormInfoAsync } from '.';
  import { useGlobSetting } from '@/hooks/setting';
  import { PdfViewer } from '../pdfViewer';
  import ApplicationFormContent from './applicationformcontent.vue';
  import SafetyTrainingRegistration from './SafetyTrainingRegistration.vue';
  import applicationForm3 from './applicationForm3.vue';
  import FileViewer from '@/components/Bit/fileViewer';
  import { xundaUtils } from '@/utils/xunda';

  const formData = ref<any>({});
  const certificates = ref<any[]>([]);
  const globSetting = useGlobSetting();
  const apiUrl = ref(globSetting.apiUrl);
  const emit = defineEmits(['changeLoading', 'next', 'update:auditState', 'update:semesterCatogry']);
  const semesterCatogry = ref('');

  const changeLoading = loading => {
    emit('changeLoading', loading);
  };

  const holdCertificates = ref<any[]>([]);

  function init(data) {
    changeLoading(true);
    getApplicationFormInfoAsync(data.id)
      .then(res => {
        formData.value = res.data;
        holdCertificates.value = res.data.holdCertificates;
        semesterCatogry.value = res.data.semesterCatogry;
        changeLoading(false);
      })
      .catch(err => {
        changeLoading(false);
      });
  }

  const getFileName = computed(() => {
    if (semesterCatogry.value === '0001') {
      return '贵州省职业技能等级认定个人申请表';
    }
    if (semesterCatogry.value === '0002') {
      return "semesterCatogry === '0001'";
    }
    return '';
  });

  const appFile = computed(() => {
    if (formData.value && formData.value.apply && formData.value.apply[0]?.url) {
      return formData.value.apply[0];
    }
    return null;
  });

  // 根据semesterCatogry动态确定使用哪个表单组件
  const currentFormComponent = computed(() => {
    switch (semesterCatogry.value) {
      case '0001':
        return ApplicationFormContent;
      case '0002':
        return SafetyTrainingRegistration;
      case '0003':
      case '0004':
        return applicationForm3;
      default:
        return null;
    }
  });

  // 下载附件方法
  function downloadAttachment() {
    if (formData.value?.apply?.[0]?.url) {
      window.open(formData.value.apply[0].url, '_blank');
    }
  }

  defineExpose({
    init,
  });
</script>

<style lang="less" scoped>
  .application-container {
    display: flex;
    gap: 24px;
    background: #fff;
  }

  .application-detail-form {
    padding: 24px;
    background: #fff;
    font-size: 14px;
    flex: 1;
    max-width: 55%;
  }

  .pdf-viewer {
    flex: 1;
    padding: 24px 24px 24px 0;
  }

  .certificate-container {
    margin-bottom: 15px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #e8e8e8;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    overflow: hidden;
  }

  .certificate-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #e8e8e8;
  }

  .certificate-title {
    font-weight: 600;
    font-size: 14px;
    color: #1890ff;
  }

  .certificate-no {
    font-size: 14px;
    margin-bottom: 5px;
    line-height: 1.4;
    font-weight: 500;
    color: #333;
  }

  .certificate-content {
    display: flex;
    padding: 10px;
  }

  .certificate-info {
    flex: 1;
    padding-right: 12px;
  }

  .certificate-row {
    display: flex;
    margin-bottom: 5px;
    line-height: 1.4;
    font-size: 13px;
  }

  .certificate-label {
    color: #666;
    min-width: 80px;
  }

  .certificate-value {
    color: #333;
    font-weight: 500;
  }

  .certificate-image {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 160px;
    border-left: 1px dashed #e8e8e8;
    padding-left: 10px;
  }
</style>
