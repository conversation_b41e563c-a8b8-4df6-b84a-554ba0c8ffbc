<template>
  <a-card class="upload-card" :bordered="false" :bodyStyle="{ padding: '20px' }">
    <a-form :colon="false" size="middle" layout="horizontal" labelAlign="left" :labelCol="{ style: { width: '100px' } }">
      <a-row :gutter="24">
        <a-col :span="12" class="ant-col-item" :hidden="false">
          <a-form-item name="scoreConfig" class="course-upload-item">
            <template #label>
              <span class="form-label">课表附件</span>
            </template>
            <div class="upload-wrapper">
              <div class="button-group">
                <XundaUploadFile
                  v-model:value="courseFile"
                  @change="setCourseFile"
                  :fileSize="10"
                  sizeUnit="MB"
                  accept=".pdf"
                  :limit="9"
                  pathType="selfPath"
                  folder="plan/semester/courseConfig"
                  :sortRule="[3]"
                  timeFormat="YYYY"
                  buttonText="点击上传"
                  :showUploadList="false"
                  :showDownload="false"
                  :detailed="disabled"
                  :disabled="disabled" />
              </div>
              <div class="upload-tip" v-if="!disabled">只能上传PDF文件，单个文件大小不超过5MB</div>
            </div>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div class="file-list-container">
      <a-empty v-if="!courseFile || courseFile.length === 0" description="暂无课表附件" />
      <div v-else class="file-list">
        <div v-for="(file, index2) in courseFile" :key="index2" class="file-item">
          <Bit_KKFileView :file="file" :previewMode="false" :allowDelete="!disabled" @delete="handleDeleteFile" :height="'1000px'"></Bit_KKFileView>
        </div>
      </div>
    </div>
  </a-card>
</template>
<script setup lang="ts">
  import { ref } from 'vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { getSemesterCourseFile, setSemesterCourseFile } from '.';
  import Bit_KKFileView from '@/components/Bit/kkFileView/index';

  const { createMessage, createConfirm } = useMessage();

  const emit = defineEmits([]);
  const props = defineProps({
    semesterId: {
      type: String,
      required: true,
    },
    disabled: { type: Boolean, default: false },
  });

  const courseFile = ref<any>(null);

  function getCourseFile() {
    courseFile.value = null;
    getSemesterCourseFile({ semesterId: props.semesterId }).then(res => {
      courseFile.value = res.data;
    });
  }

  function handleDeleteFile(file) {
    courseFile.value = courseFile.value.filter(item => item.fileId !== file.fileId);
    setCourseFile(courseFile.value);
  }

  function setCourseFile(fileList) {
    setSemesterCourseFile({ semesterId: props.semesterId, courseFile: fileList }).then(res => {
      if (res.code == 200) {
        createMessage.success(res.msg);
      } else {
        createMessage.error(res.msg);
      }
    });
  }

  function init(data) {
    getCourseFile();
  }

  defineExpose({
    init,
  });
</script>

<style lang="less" scoped>
  .upload-card {
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    /* 移除过渡动画和悬停效果 */
  }

  .form-label {
    font-weight: 500;
    color: #333;
    font-size: 15px;
  }

  .upload-wrapper {
    display: flex;
    flex-direction: column;

    .button-group {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      :deep(.ant-upload-button) {
        background: linear-gradient(135deg, #1890ff, #096dd9);
        color: white;
        border: none;
        border-radius: 6px;
        padding: 10px 20px;
        height: auto;
        font-weight: 500;
        box-shadow: 0 2px 6px rgba(24, 144, 255, 0.3);
        transition: all 0.3s;

        /* 移除上传按钮的悬停和点击效果 */
      }
    }

    .upload-tip {
      background-color: #f9f9f9;
      border-left: 3px solid #1890ff;
      padding: 8px 12px;
      border-radius: 0 4px 4px 0;
      color: #666;
      font-size: 13px;
      margin-bottom: 15px;
    }
  }

  :deep(.file-viewer) {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    margin-bottom: 0;

    /* 移除文件查看器的悬停效果 */

    .file-actions {
      /* 保持文件操作按钮始终可见 */
      opacity: 1;
    }
  }

  .file-list-container {
    margin-top: 20px;

    .file-list {
      display: flex;
      flex-direction: column;
      gap: 16px;
      padding: 10px 0;
    }

    .file-item {
      background: white;
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;
      width: 100%;
      display: flex;
      flex-direction: column;
    }
  }
</style>
