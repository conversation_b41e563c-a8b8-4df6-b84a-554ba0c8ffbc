<template>
  <span :class="`${prefixCls}- flex items-center `" :title="getI18nName">
    <span v-if="getIcon" :class="`${prefixCls}-wrapper__icon mr-2 ${getIcon}`"></span>
    {{ getI18nName }}
  </span>
</template>
<script lang="ts">
  import { computed, defineComponent } from 'vue';

  import Icon from '@/components/Icon/index';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useDesign } from '@/hooks/web/useDesign';
  import { contentProps } from '../props';
  const { t } = useI18n();

  export default defineComponent({
    name: 'MenuItemContent',
    components: {
      Icon,
    },
    props: contentProps,
    setup(props) {
      const { prefixCls } = useDesign('basic-menu-item-content');
      const getI18nName = computed(() => t('routes.' + props.item?.enCode.replace(/\./g, '-'), props.item?.fullName));
      const getIcon = computed(() => props.item?.icon);

      return {
        prefixCls,
        getI18nName,
        getIcon,
      };
    },
  });
</script>
