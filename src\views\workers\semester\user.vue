<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" destroyOnClose :title="'分班'">
    <template #centerToolbar>
      <a-space :size="10">
        <a-button preIcon="icon-ym icon-ym-btn-print" type="primary" @click="generateExaminationAdmissionCardHandler"> {{ t('导出准考证') }}</a-button>
        <CustomUpload
          accept=".xlsx"
          buttonText="单位人员导入"
          tipContent="只能上传PDF文件，单个文件大小不超过5MB"
          :fileSize="5"
          sizeUnit="MB"
          customUploadUrl="/api/study/registration/importUnitUsers"
          :uploadParams="{ acceptType: 'xlsx', semesterId: semesterId }"
          @success="handleUploadSuccess"
          @error="handleUploadError"
          :showFileList="false" />
      </a-space>
    </template>

    <div class="xunda-content-wrapper">
      <div class="xunda-content-wrapper-left">
        <LeftTable ref="leftTableRef" @selection-change="handleLeftTableSelect" :semesterId="semesterId"> </LeftTable>
      </div>
      <div class="xunda-content-wrapper-center">
        <div class="xunda-content-wrapper-search-box">
          <BasicForm
            @register="registerSearchForm"
            :schemas="userSearchSchemas"
            @submit="handleLeftSearchSubmit"
            @reset="handleLeftSearchReset"
            class="search-form">
          </BasicForm>
        </div>
        <div class="xunda-content-wrapper-content bg-white section-card">
          <BasicTable @register="registerTableLeft" ref="tableLeftRef">
            <template #tableTitle>
              <div class="section-title">
                <span class="title-text">已分班人员</span>
              </div>
              <a-button type="primary" danger preIcon="icon-ym icon-ym-btn-clearn" @click="handelBatchRemove()"> {{ t('批量移出') }}</a-button>
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <TableAction
                  :actions="[
                    {
                      label: t('移出本班级'),
                      color: 'error',
                      tooltip: t('移出本班级'),
                      modelConfirm: {
                        onOk: handleRemove.bind(null, record.id),
                        content: '您确定要移出本班级吗, 是否继续?',
                      },
                    },
                  ]" />
              </template>
            </template>
          </BasicTable>
        </div>
      </div>
      <div class="xunda-content-wrapper-center">
        <div class="xunda-content-wrapper-search-box">
          <BasicForm
            @register="registerSearchForm"
            :schemas="userSearchSchemas"
            @submit="handleRightSearchSubmit"
            @reset="handleRightSearchReset"
            class="search-form">
          </BasicForm>
        </div>
        <div class="xunda-content-wrapper-content bg-white section-card">
          <BasicTable @register="registerTableRight" ref="tableRightRef">
            <template #tableTitle>
              <div class="section-title">
                <span class="title-text">未分班人员</span>
              </div>
              <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="handelBatchAdd()" :disabled="classesId == null || classesId === ''">
                {{ t('批量加入') }}</a-button
              >
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <TableAction
                  :actions="[
                    {
                      label: t('加入左侧班级'),
                      color: 'success',
                      disabled: classesId == null || classesId === '',
                      modelConfirm: {
                        content: '您确定要加入左侧班级吗, 是否继续?',
                        onOk: handleAdd.bind(null, record.id),
                      },
                    },
                  ]" />
              </template>
            </template>
          </BasicTable>
        </div>
      </div>
    </div>
  </BasicPopup>
</template>

<script lang="ts" setup>
  import { BasicPopup, usePopup } from '@/components/Popup';
  import { generateExaminationAdmissionCard, getUserList, setClassesId, userColumns, userSearchSchemas } from './user';
  import LeftTable from './classesTable.vue';
  import { ref, reactive, onMounted, nextTick } from 'vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { BasicForm, useForm } from '@/components/Form';
  import { BasicTable, useTable, TableAction, TableActionType } from '@/components/Table';
  import { useRoute } from 'vue-router';
  import { cloneDeep } from 'lodash-es';
  import { usePermission } from '@/hooks/web/usePermission';
  import { isArray } from '@/utils/is';
  import CustomUpload from '@/views/study/registration/components/CustomUpload/index.vue';
  import { downloadByUrl } from '@/utils/file/download';

  const [registerPopup, { openPopup, setPopupProps }] = usePopup();
  const emit = defineEmits(['reload']);
  defineExpose({ init });

  // 左侧表格相关
  const leftTableRef = ref<Nullable<TableActionType>>(null);

  // 左侧表格选择事件
  function handleLeftTableSelect(selectedRowKeys: string[], selectedRows: any[]) {
    classesId.value = selectedRowKeys[0];
    tableLeftRef.value?.reload();
    tableRightRef.value?.reload();
  }

  const { hasFormP } = usePermission();
  const route = useRoute();
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const tableLeftRef = ref<Nullable<TableActionType>>(null);
  const tableRightRef = ref<Nullable<TableActionType>>(null);
  const cacheList = ref<any>([]);
  const classesId = ref('');
  const semesterId = ref('');
  const defaultSearchInfo = {
    menuId: route.meta.modelId as string,
    moduleId: '***********', //模块ID
    superQueryJson: '',
    dataType: 0,
    semesterId: semesterId.value,
  };
  const searchLeftInfo = reactive({
    ...cloneDeep(defaultSearchInfo),
  });

  const [registerSearchForm] = useForm({
    baseColProps: { span: 8 },
    showActionButtonGroup: true,
    showAdvancedButton: true,
    autoAdvancedLine: 2,
    compact: true,
    submitOnReset: true,
  });
  const tableLeftGetList = params => {
    params.classesId = classesId.value;
    params.inClasses = true;
    params.semesterId = semesterId.value;
    return getUserList(params);
  };

  const leftUserColumns = [
    {
      title: t('班级'),
      dataIndex: 'classesName',
      width: 120,
    },
    {
      title: t('姓名'),
      dataIndex: 'name',
      width: 120,
    },
  ];

  const [registerTableLeft] = useTable({
    api: tableLeftGetList,
    columns: leftUserColumns,
    immediate: false,
    searchInfo: searchLeftInfo,
    clickToRowSelect: false,
    afterFetch: data => {
      const list = data.map(o => ({
        ...o,
      }));
      cacheList.value = cloneDeep(list);
      return list;
    },
    actionColumn: {
      width: 80,
      title: t('common.actionText', '操作'),
      dataIndex: 'action',
      fixed: 'right',
    },
    rowSelection: {
      type: 'checkbox',
      getCheckboxProps: record => ({
        disabled: record.top,
      }),
    },
  });

  function handleLeftSearchReset() {
    tableLeftRef.value?.clearSelectedRowKeys();
  }

  function handleLeftSearchSubmit(data) {
    let obj = {
      ...defaultSearchInfo,
      superQueryJson: searchLeftInfo.superQueryJson,
      ...data,
    };
    Object.keys(searchLeftInfo).map(key => {
      delete searchLeftInfo[key];
    });
    for (let [key, value] of Object.entries(obj)) {
      searchLeftInfo[key.replaceAll('-', '_')] = value;
    }
    console.log(searchLeftInfo);
    tableLeftRef.value?.reload({ page: 1 });
  }

  const searchRightInfo = reactive({
    ...cloneDeep(defaultSearchInfo),
  });

  const tableRightGetList = params => {
    params.excepClassesId = classesId.value;
    params.inClasses = false;
    params.semesterId = semesterId.value;
    return getUserList(params);
  };
  const [registerTableRight] = useTable({
    api: tableRightGetList,
    columns: userColumns,
    immediate: false,
    searchInfo: searchRightInfo,
    clickToRowSelect: false,
    afterFetch: data => {
      const list = data.map(o => ({
        ...o,
      }));
      cacheList.value = cloneDeep(list);
      return list;
    },
    actionColumn: {
      width: 80,
      title: t('common.actionText', '操作'),
      dataIndex: 'action',
      fixed: 'right',
    },
    rowSelection: {
      type: 'checkbox',
      getCheckboxProps: record => ({
        disabled: record.top,
      }),
    },
  });

  function handleRightSearchReset() {
    tableRightRef.value?.clearSelectedRowKeys();
  }

  function handleRightSearchSubmit(data) {
    let obj = {
      ...defaultSearchInfo,
      superQueryJson: searchRightInfo.superQueryJson,
      ...data,
    };
    Object.keys(searchRightInfo).map(key => {
      delete searchRightInfo[key];
    });
    for (let [key, value] of Object.entries(obj)) {
      searchRightInfo[key.replaceAll('-', '_')] = value;
    }
    console.log(searchRightInfo);
    tableRightRef.value?.reload({ page: 1 });
  }

  function handleUploadSuccess(data) {
    createMessage.success('上传成功', data);
    initAll();
  }
  function handleUploadError(e) {
    var data = JSON.parse(e.data?.toString() || '');
    const { createErrorModal } = useMessage();
    if (isArray(data)) {
      createErrorModal({
        title: '上传失败',
        content: data.join('<br>'),
      });
    } else {
      createErrorModal({
        title: '上传失败',
        content: data,
      });
    }
  }
  function initAll() {
    tableLeftRef.value?.clearSelectedRowKeys();
    tableRightRef.value?.clearSelectedRowKeys();
    tableLeftRef.value?.reload({ page: 1 });
    tableRightRef.value?.reload({ page: 1 });
  }

  function doAdd(query) {
    tableRightRef.value?.setLoading(true);
    setClassesId(query)
      .then(res => {
        createMessage.success(res.msg);
        initAll();
      })
      .finally(() => {
        tableRightRef.value?.setLoading(false);
      });
  }

  function handleAdd(id) {
    const query = { registrationIds: [id], classesId: classesId.value, isAdd: true };
    doAdd(query);
  }

  function handelBatchAdd() {
    const ids = tableRightRef.value?.getSelectRowKeys() ?? [];
    if (!ids.length) return createMessage.error('请选择一条数据');
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要加入左侧班级吗, 是否继续?',
      onOk: () => {
        const query = { registrationIds: ids, classesId: classesId.value, isAdd: true };
        doAdd(query);
      },
    });
  }

  function doRemove(query) {
    tableLeftRef.value?.setLoading(true);
    setClassesId(query)
      .then(res => {
        createMessage.success(res.msg);
        initAll();
      })
      .finally(() => {
        tableLeftRef.value?.setLoading(false);
      });
  }

  function handleRemove(id) {
    const query = { registrationIds: [id], classesId: classesId.value };
    doRemove(query);
  }

  function handelBatchRemove() {
    const ids = tableLeftRef.value?.getSelectRowKeys() ?? [];
    if (!ids.length) return createMessage.error('请选择一条数据');
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要移出本班级吗, 是否继续?',
      onOk: () => {
        const query = { registrationIds: ids, classesId: classesId.value };
        doRemove(query);
      },
    });
  }

  function init(data) {
    semesterId.value = data.id;
    openPopup();
    nextTick(() => {
      initAll();
    });
  }

  function generateExaminationAdmissionCardHandler() {
    setPopupProps({ loading: true });

    generateExaminationAdmissionCard({ semesterId: semesterId.value }).then(async res => {
      if (res.data && res.data.url) {
        await downloadByUrl({ url: res.data.url, fileName: res.data.name });
        setPopupProps({ loading: false });
      }
    });
  }

  // 设置查询表单
  function setSearchSchema() {}

  onMounted(() => {
    setSearchSchema();
  });
</script>
<style lang="less" scoped>
  .xunda-content-wrapper {
    display: flex;
    height: 100%;

    &-left {
      width: 300px;
      margin-right: 16px;
      background-color: #fff;
      border-radius: 2px;
    }
  }

  .section-card {
    border-radius: 6px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
    padding: 16px;
    margin-bottom: 16px;

    &:hover {
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    }
  }

  .section-title {
    display: flex;
    align-items: center;

    .title-text {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      position: relative;
      padding-left: 10px;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 16px;
        background-color: #1890ff;
        border-radius: 2px;
      }
    }
  }
</style>
