<template>
  <div class="xunda-content-wrapper">
    <div class="xunda-content-wrapper-center">
      <div class="xunda-content-wrapper-search-box">
        <BasicForm
          @register="registerSearchForm"
          :schemas="searchSchemas"
          @advanced-change="redoHeight"
          @submit="handleSearchSubmit"
          @reset="handleSearchReset"
          class="search-form">
        </BasicForm>
      </div>
      <div class="xunda-content-wrapper-content bg-white">
        <BasicTable @register="registerTable" ref="tableRef">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="HandleAdd()"> {{ t('common.add2Text', '新增') }}</a-button>
            <!-- <a-button
              type="link"
              preIcon="icon-ym icon-ym-btn-download"
              @click="openExportModal(true, { columnList: exportColumnList, selectIds: getSelectRowKeys() })">
              {{ t('common.exportText', '导出') }}</a-button
            >
            <a-button type="link" preIcon="icon-ym icon-ym-btn-upload" @click="openImportModal(true, { url: semesterApi, menuId: searchInfo.menuId })">
              {{ t('common.importText', '导入') }}</a-button
            > -->
            <a-button type="link" preIcon="icon-ym icon-ym-btn-clearn" @click="handelBatchRemove()"> {{ t('common.batchDelText', '批量删除') }}</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'category'">
              <p>{{ xundaUtils.optionText(record.category, categoryOptions) }}</p>
            </template>
            <template v-if="column.key === 'publishFlag'">
              <a-tag :color="record.publishFlag ? 'success' : ''">{{ record.publishFlag ? '已发布' : '未发布' }}</a-tag>
            </template>
            <template v-if="column.key === 'number'">
              <!--已审/未审/总数  分别用绿色 黄色 黑色显示 同时添加跳转  -->
              <a-tag color="orange" class="hover-underline" @click="handleCheckNumber(record, 3)"> {{ record.payNumber ?? 0 }}</a-tag> <span>/</span>
              <a-tag color="cyan" class="hover-underline" @click="handleCheckNumber(record, 4)">{{ record.noPayNumber ?? 0 }}</a-tag> <span>/</span>
              <a-tag color="success" class="hover-underline" @click="handleCheckNumber(record, 1)"> {{ record.auditNumber ?? 0 }}</a-tag> <span>/</span>
              <a-tag color="warning" class="hover-underline" @click="handleCheckNumber(record, 2)">{{ record.waitNumber ?? 0 }}</a-tag> <span>/</span>
              <a-tag class="hover-underline" @click="handleCheckNumber(record, 0)">{{ (record.auditNumber ?? 0) + (record.waitNumber ?? 0) }} </a-tag>
            </template>
            <template v-if="column.key === 'money'">
              <!--已审/未审/总数  分别用绿色 黄色 黑色显示 同时添加跳转  -->
              <a-tag color="success" class="hover-underline" @click="handleCheckNumber(record, 'payIn')">
                {{ xundaUtils.numtoAmount(record.payIn ?? 0, '元') }}</a-tag
              >
              <span>/</span>
              <a-tag color="error" class="hover-underline" @click="handleCheckNumber(record, 'payWait')">{{
                xundaUtils.numtoAmount((record.shouldPay ?? 0) - (record.payIn ?? 0), '元')
              }}</a-tag>
              <span>/</span>
              <a-tag class="hover-underline" @click="handleCheckNumber(record, 'showPay')">{{ xundaUtils.numtoAmount(record.shouldPay ?? 0, '元') }} </a-tag>
            </template>
            <template v-if="column.key === 'action'">
              <TableAction
                :actions="[
                  {
                    label: record.publishFlag ? '查看' : '发布',
                    onClick: handleEdit.bind(null, record),
                  },
                  {
                    label: t('common.delText', '删除'),
                    color: 'error',
                    modelConfirm: {
                      onOk: handleDelete.bind(null, record.id),
                    },
                  },
                  {
                    label: t('复制'),
                    onClick: handleCopy.bind(null, record),
                  },
                ]"
                :drop-down-actions="[
                  {
                    label: t('人员'),
                    onClick: openUser.bind(null, record),
                  },
                ]" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form ref="formRef" @reload="reload" />
    <ExportModal @register="registerExportModal" @download="handleDownload" />
    <ImportModal @register="registerImportModal" @reload="reload" />>
    <publish-popup @register="registerPublishPopup" @reload="reload" />
    <user-popup ref="userPopupRef" @reload="reload" />
  </div>
</template>

<script lang="ts" setup>
  import { semesterApi, getList, batchDelete, exportData, columns, searchSchemas } from '@/views/plan/semester';
  import { ref, reactive, onMounted, onBeforeUnmount, computed } from 'vue';
  import { useModal } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { BasicForm, useForm } from '@/components/Form';
  import { BasicTable, useTable, TableAction, TableActionType } from '@/components/Table';
  import Form from '@/views/plan/semester/form.vue';
  import { useRoute } from 'vue-router';
  import { cloneDeep } from 'lodash-es';
  import { ExportModal } from '@/components/CommonModal';
  import { downloadByUrl } from '@/utils/file/download';
  import { ImportModal } from '@/components/CommonModal';
  import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
  import { usePermission } from '@/hooks/web/usePermission';
  import { xundaUtils } from '@/utils/xunda';
  import PublishPopup from '@/views/plan/semester/publish.vue';
  import { router } from '@/router';
  import { isProdMode } from '@/utils/env';
  import UserPopup from './user.vue';

  const [registerPublishPopup, { openModal: openPublishPopup }] = useModal();

  const { hasFormP } = usePermission();
  const route = useRoute();
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const [registerExportModal, { openModal: openExportModal, closeModal: closeExportModal, setModalProps: setExportModalProps }] = useModal();
  const [registerImportModal, { openModal: openImportModal }] = useModal();
  const formRef = ref<any>(null);
  const tableRef = ref<Nullable<TableActionType>>(null);
  const cacheList = ref<any>([]);

  const props = defineProps({
    vueTitle: {
      type: String,
      default: '成人高等学历教育',
    },
    category: {
      type: String,
      default: '0005',
    },
  });

  const category = computed(() => props.category);
  const userPopupRef = ref<any>(null);
  const examPopupRef = ref<any>(null);

  const defaultSearchInfo = {
    menuId: route.meta.modelId as string,
    moduleId: '***********', //模块ID
    superQueryJson: '',
    category: category.value,
    dataType: 0,
  };
  const exportColumnList = computed(() => {
    return columns.map(o => {
      return {
        id: o.dataIndex,
        key: o.dataIndex,
        value: o.dataIndex,
        fullName: o.title,
        __config__: {
          xundakey: 'text',
        },
      };
    });
  });

  const searchInfo = reactive({
    ...cloneDeep(defaultSearchInfo),
  });

  const [registerSearchForm, { updateSchema }] = useForm({
    baseColProps: { span: 6 },
    showActionButtonGroup: true,
    showAdvancedButton: true,
    submitOnChange: true,
    submitOnReset: true,
    compact: true,
  });

  const [registerTable, { reload, setLoading, getFetchParams, redoHeight, getSelectRowKeys, clearSelectedRowKeys }] = useTable({
    api: getList,
    columns: columns,
    searchInfo: searchInfo,
    clickToRowSelect: false,
    beforeFetch: data => {
      var params = cloneDeep(data);
      if (params.year) params.year = xundaUtils.convertBetweenTimestampAndYear(params.year);
      params.category = category.value;
      return params;
    },
    afterFetch: data => {
      const list = data.map(o => ({
        ...o,
      }));
      cacheList.value = cloneDeep(list);
      return list;
    },
    actionColumn: {
      width: 200,
      title: t('common.actionText', '操作'),
      dataIndex: 'action',
      fixed: 'right',
    },
    rowSelection: {
      type: 'checkbox',
      getCheckboxProps: record => ({
        disabled: record.top,
      }),
    },
  });

  function handleSearchReset() {
    clearSelectedRowKeys();
  }

  function handleSearchSubmit(data) {
    clearSelectedRowKeys();
    let obj = {
      ...defaultSearchInfo,
      superQueryJson: searchInfo.superQueryJson,
      ...data,
    };
    Object.keys(searchInfo).map(key => {
      delete searchInfo[key];
    });
    for (let [key, value] of Object.entries(obj)) {
      searchInfo[key.replaceAll('-', '_')] = value;
    }
    console.log(searchInfo);
    reload({ page: 1 });
  }

  // 编辑
  function handleEdit(record) {
    // 不带工作流
    const data = {
      id: record.id,
      menuId: searchInfo.menuId,
      allList: cacheList.value,
      category: record.category,
    };
    // formRef.value?.init(data);
    openFormPopup(true, data);
  }
  // 删除
  function handleDelete(id) {
    const query = { ids: [id] };
    batchDelete(query).then(res => {
      createMessage.success(res.msg);
      clearSelectedRowKeys();
      reload();
    });
  }
  // 新增
  function HandleAdd() {
    if (!category.value) return createMessage.error('请选择左侧计划类型');
    // 不带流程新增
    const data = {
      id: '',
      menuId: searchInfo.menuId,
      allList: cacheList.value,
      category: category.value,
    };
    openFormPopup(true, data);
  }

  function handleCopy(record) {
    // 不带工作流
    const data = {
      id: record.id,
      menuId: searchInfo.menuId,
      allList: cacheList.value,
      category: record.category,
      isCopy: true,
    };
    openFormPopup(true, data);
  }

  function openFormPopup(show, data) {
    console.log('openFormPopup', show, data);
    openPublishPopup(show, data);
  }
  function openUser(data) {
    userPopupRef.value?.init(data);
  }

  function openExam(data) {
    examPopupRef.value?.init(data);
  }
  // 导出
  function handleDownload(data) {
    let query = { ...getFetchParams(), ...data };
    exportData(query)
      .then(res => {
        setExportModalProps({ confirmLoading: false });
        if (!res.data.url) return;
        downloadByUrl({ url: res.data.url });
        closeExportModal();
      })
      .catch(() => {
        setExportModalProps({ confirmLoading: false });
      });
  }

  // 批量删除
  function handelBatchRemove() {
    const ids = getSelectRowKeys();
    if (!ids.length) return createMessage.error('请选择一条数据');
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要删除这些数据吗, 是否继续?',
      onOk: () => {
        const query = { ids: ids };
        setLoading(true);
        batchDelete(query)
          .then(res => {
            createMessage.success(res.msg);
            clearSelectedRowKeys();
            reload();
          })
          .finally(() => {
            setLoading(false);
          });
      },
    });
  }

  const categoryOptions = ref([]);
  // 定时刷新相关变量和方法
  const refreshTimer = ref(null);
  const refreshInterval = ref(60); // 默认60秒刷新一次

  // 开始定时刷新
  function startAutoRefresh(seconds = 60) {
    // 先清除可能存在的定时器
    stopAutoRefresh();
    refreshInterval.value = seconds;
    refreshTimer.value = setInterval(() => {
      reload();
      console.log('自动刷新数据，间隔：' + refreshInterval.value + '秒');
    }, refreshInterval.value * 1000);
  }

  // 停止定时刷新
  function stopAutoRefresh() {
    if (refreshTimer.value) {
      clearInterval(refreshTimer.value);
      refreshTimer.value = null;
    }
  }

  // 修改刷新时间间隔
  function changeRefreshInterval(seconds) {
    refreshInterval.value = seconds;
    if (refreshTimer.value) {
      startAutoRefresh(seconds);
    }
  }

  // 设置查询表单
  function setSearchSchema() {
    getDictionaryDataSelector('planType').then(res => {
      categoryOptions.value = res.data.list;
      updateSchema({
        field: 'category',
        componentProps: {
          options: categoryOptions.value,
        },
      });
    });
  }

  function handleCheckNumber({ id, name, category }, type) {
    const data = {
      semesterId: id,
      semesterCategory: category,
      type: type,
      semesterName: name,
    };
    // 路由跳转到选课注册页面
    router.push({
      path: '/plan/registration',
      query: data,
    });
  }

  onMounted(() => {
    setSearchSchema();
    // 启动定时刷新，默认60秒刷新一次
    if (isProdMode()) startAutoRefresh();
  });

  // 组件卸载前清除定时器
  onBeforeUnmount(() => {
    stopAutoRefresh();
  });
</script>

<style lang="less" scoped>
  .hover-underline {
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: bold;
    &:hover {
      text-decoration: underline;
      cursor: pointer;
      transform: scale(1.05);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }

  .xunda-content-wrapper-left {
    width: 300px;
    background-color: @component-background;
    flex-shrink: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    margin-right: 10px;
    border-radius: 8px;
    overflow: hidden;
  }
</style>
