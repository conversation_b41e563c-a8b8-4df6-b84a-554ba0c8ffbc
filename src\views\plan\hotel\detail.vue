<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" width="600px" :minHeight="100" :showOkBtn="false">
    <template #insertFooter> </template>
    <!-- 表单 -->
    <a-row class="dynamic-form">
      <a-form :colon="false" size="middle" layout="horizontal" labelAlign="right" :labelCol="{ style: { width: '100px' } }" :model="dataForm" ref="formRef">
        <a-row :gutter="15">
          <!-- 具体表单 -->
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="name">
              <template #label>名称 </template>
              <p>{{ dataForm.name }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="address">
              <template #label>地址 </template>
              <!-- <p>{{ dataForm.address }} </p> -->
              <XundaLocation v-model:value="dataForm.location" detailed placeholder="请选择位置" :allowClear="true" :style="{ width: '100%' }"> </XundaLocation>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="description">
              <template #label>描述 </template>
              <p>{{ dataForm.description }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="latitude">
              <template #label>经度 </template>
              <p>{{ dataForm.latitude }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="longtitude">
              <template #label>纬度 </template>
              <p>{{ dataForm.longtitude }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="picture">
              <template #label>图片 </template>
              <p>{{ dataForm.picture }} </p>
            </a-form-item>
          </a-col>
          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { getDetailInfo } from '@/views/plan/hotel';
  import { reactive, toRefs, nextTick, ref } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { usePermission } from '@/hooks/web/usePermission';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { optionText } from '@/utils/xunda';
  import { toDateString, getDictionaryFullName, toFixedPercent } from '@/utils/myUtil';
  import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
  import { XundaLocation } from '@/components/Xunda';
  interface State {
    dataForm: any;
    title: string;
    optionsObj: any;
  }

  defineOptions({ name: 'Detail' });
  const emit = defineEmits(['reload']);
  const { createMessage, createConfirm } = useMessage();
  const [registerModal, { openModal, setModalProps, closeModal }] = useModal();

  const { t } = useI18n();
  const detailRef = ref<any>(null);
  const visitRecordGridRef = ref<any>(null);
  const state = reactive<State>({
    dataForm: {},
    title: t('common.detailText', '详情'),
    optionsObj: {
      //选项配置
      defaultProps: { label: 'fullName', value: 'enCode' }, // 默认下拉选择键值
    },
  });

  const { title, dataForm, optionsObj } = toRefs(state);
  // 表单权限
  const { hasFormP } = usePermission();

  defineExpose({ init });

  function init(data) {
    getAllSelectOptions();
    state.dataForm.id = data.id;
    openModal();
    nextTick(() => {
      setTimeout(initData, 0);
    });
  }
  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      closeModal();
    }
  }
  function getData(id) {
    getDetailInfo(id).then(res => {
      state.dataForm = res.data || {};
      if (state.dataForm.latitude && state.dataForm.longtitude) {
        state.dataForm.location = JSON.stringify({
          lat: state.dataForm.latitude,
          lng: state.dataForm.longtitude,
          fullAddress: state.dataForm.address,
          name: state.dataForm.name,
        });
      }
      nextTick(() => {
        changeLoading(false);
      });
    });
  }

  function setFormProps(data) {
    setModalProps(data);
  }
  function changeLoading(loading) {
    setFormProps({ loading });
  }

  function getAllSelectOptions() {}
</script>
