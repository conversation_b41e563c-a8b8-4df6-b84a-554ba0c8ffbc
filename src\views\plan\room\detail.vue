<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" width="600px" :minHeight="100" :showOkBtn="false">
    <template #insertFooter> </template>
    <!-- 表单 -->
    <a-row class="dynamic-form">
      <a-form :colon="false" size="middle" layout="horizontal" labelAlign="right" :labelCol="{ style: { width: '100px' } }" :model="dataForm" ref="formRef">
        <a-row :gutter="15">
          <!-- 具体表单 -->
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="hotelId">
              <template #label>酒店 </template>
              <p>{{ optionText(dataForm.hotelId, hotelNameList, { key: 'id', value: 'name' }) }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="name">
              <template #label>名称 </template>
              <p>{{ dataForm.name }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="currency">
              <template #label>币种 </template>
              <p>{{ optionText(dataForm.currency, currencyList, { key: 'enCode', value: 'fullName' }) }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="price">
              <template #label>参考价格 </template>
              <p>{{ dataForm.price }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="roomType">
              <template #label>房型 </template>
              <p>{{ optionText(dataForm.roomType, roomTypeList, { key: 'enCode', value: 'fullName' }) }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="description">
              <template #label>描述 </template>
              <p>{{ dataForm.description }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="picture">
              <template #label>图片 </template>
              <p>{{ dataForm.picture }} </p>
            </a-form-item>
          </a-col>
          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { getDetailInfo, getRoomType } from '@/views/plan/room';
  import { reactive, toRefs, nextTick, ref } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { usePermission } from '@/hooks/web/usePermission';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { optionText } from '@/utils/xunda';
  import { toDateString, getDictionaryFullName, toFixedPercent } from '@/utils/myUtil';
  import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
  import { getForSelect } from '../hotel';
  import { getCurrencyType } from '../feeConfig';
  import { cloneDeep } from 'lodash-es';
  interface State {
    dataForm: any;
    title: string;
    optionsObj: any;
  }

  defineOptions({ name: 'Detail' });
  const hotelNameList = ref<any>([]);
  const currencyList = ref<any>([]);
  const roomTypeList = ref<any>([]);
  const emit = defineEmits(['reload']);
  const { createMessage, createConfirm } = useMessage();
  const [registerModal, { openModal, setModalProps, closeModal }] = useModal();

  const { t } = useI18n();
  const detailRef = ref<any>(null);
  const visitRecordGridRef = ref<any>(null);
  const state = reactive<State>({
    dataForm: {},
    title: t('common.detailText', '详情'),
    optionsObj: {
      //选项配置
      defaultProps: { label: 'fullName', value: 'enCode' }, // 默认下拉选择键值
    },
  });

  const { title, dataForm, optionsObj } = toRefs(state);
  // 表单权限
  const { hasFormP } = usePermission();

  defineExpose({ init });

  function init(data) {
    getAllSelectOptions();
    state.dataForm.id = data.id;
    openModal();
    nextTick(() => {
      setTimeout(initData, 0);
    });
  }
  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      closeModal();
    }
  }
  function getData(id) {
    getDetailInfo(id).then(res => {
      state.dataForm = res.data || {};
      nextTick(() => {
        changeLoading(false);
      });
    });
  }

  function setFormProps(data) {
    setModalProps(data);
  }
  function changeLoading(loading) {
    setFormProps({ loading });
  }

  function getAllSelectOptions() {
    getForSelect({
      dataType: 1,
    }).then(res => {
      hotelNameList.value = cloneDeep(res.data.list);
    });
    getAllOption();
  }
  async function getAllOption() {
    const roomTypeData = await getRoomType();
    const currencyData = await getCurrencyType();
    roomTypeList.value = cloneDeep(roomTypeData);
    currencyList.value = cloneDeep(currencyData);
  }
</script>
