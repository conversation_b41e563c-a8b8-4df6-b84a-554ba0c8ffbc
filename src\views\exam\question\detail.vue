<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" width="800px" :minHeight="100" :showOkBtn="false">
    <template #insertFooter> </template>
    
    <!-- 题目详情 -->
    <div class="question-detail">
      <!-- 基本信息卡片 -->
      <a-card title="基本信息" class="info-card" :bordered="false">
        <a-row :gutter="16">
          <a-col :span="8">
            <div class="info-item">
              <span class="label">题序：</span>
              <span class="value">{{ dataForm.displayOrder || '-' }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <span class="label">题型：</span>
              <span class="value">{{ getQuestionTypeName(dataForm.questionTypeId) }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="info-item">
              <span class="label">难度：</span>
              <span class="value">
                <a-rate :value="dataForm.difficulty" disabled :count="5" allow-half />
              </span>
            </div>
          </a-col>
        </a-row>
        <a-row :gutter="16" style="margin-top: 16px;">
          <a-col :span="12">
            <div class="info-item">
              <span class="label">课程：</span>
              <span class="value">{{ getCourseName(dataForm.courseId) }}</span>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="info-item">
              <span class="label">知识点：</span>
              <span class="value">{{ dataForm.knowledgeTag || '-' }}</span>
            </div>
          </a-col>
        </a-row>
      </a-card>

      <!-- 题目内容卡片 -->
      <a-card title="题目内容" class="content-card" :bordered="false">
        <div class="question-content">
          <div class="content-section">
            <h4 class="section-title">题干</h4>
            <div class="content-body" v-html="formatContent(dataForm.content)"></div>
          </div>
          
          <div class="content-section" v-if="dataForm.option">
            <h4 class="section-title">选项</h4>
            <div class="options-list">
              <div v-for="(option, index) in parseOptions(dataForm.option)" :key="index" class="option-item">
                <span class="option-label">{{ String.fromCharCode(65 + index) }}.</span>
                <span class="option-text" v-html="formatContent(option)"></span>
              </div>
            </div>
          </div>
          
          <div class="content-section">
            <h4 class="section-title">答案</h4>
            <div class="answer-content" v-if="dataForm.questionTypeId === 'fillblank'">
              <div v-for="(blank, index) in parseFillBlankAnswerArray(dataForm.answer)" :key="index" class="blank-answer-item">
                <span class="blank-label">{{ blank.label }}</span>
                <span class="blank-value">{{ blank.answer }}</span>
              </div>
            </div>
            <div class="answer-content" v-else v-html="formatContent(dataForm.answer)"></div>
          </div>
          
          <div class="content-section" v-if="dataForm.analysis">
            <h4 class="section-title">解析</h4>
            <div class="analysis-content" v-html="formatContent(dataForm.analysis)"></div>
          </div>
        </div>
      </a-card>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { getDetailInfo } from '@/views/exam/question';
  import { reactive, toRefs, nextTick, ref } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { useI18n } from '@/hooks/web/useI18n';
  import { getForSelect as getCourseForSelect } from '@/views/ea/course';
  
  interface State {
    dataForm: any;
    title: string;
    courseOptions: any[];
  }
  
  defineOptions({ name: 'Detail' });
  const emit = defineEmits(['reload']);
  const [registerModal, { openModal, setModalProps, closeModal }] = useModal();
  const { t } = useI18n();
  
  const state = reactive<State>({
    dataForm: {},
    title: t('common.detailText', '详情'),
    courseOptions: [],
  });

  const { title, dataForm, courseOptions } = toRefs(state);

  defineExpose({ init });

  // 题型映射
  const questionTypeMap = {
    'single': '单选题',
    'multi': '多选题', 
    'truefalse': '判断题',
    'fillblank': '填空题',
    'shortanswer': '简答题'
  }

  // 获取题型名称
  function getQuestionTypeName(typeId: string): string {
    return questionTypeMap[typeId] || typeId || '-'
  }

  // 获取课程名称
  function getCourseName(courseId: string): string {
    if (!courseId) return '-'
    const course = courseOptions.value.find(c => c.id === courseId)
    return course ? course.name : courseId
  }

  // 格式化内容（处理HTML）
  function formatContent(content: string): string {
    if (!content) return '-'
    // 保留HTML标签，但确保安全显示
    return content.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
  }

  // 解析选项JSON
  function parseOptions(optionStr: string): string[] {
    if (!optionStr) return []
    
    try {
      const options = JSON.parse(optionStr)
      if (Array.isArray(options)) {
        return options.map(opt => opt.text || opt.label || opt)
      } else if (typeof options === 'object') {
        const optionList = options.options || options.choices || Object.values(options)
        if (Array.isArray(optionList)) {
          return optionList.map(opt => opt.text || opt.label || opt)
        }
      }
      return [JSON.stringify(options)]
    } catch (error) {
      // 如果不是JSON格式，按换行符分割
      return optionStr.split('\n').filter(item => item.trim())
    }
  }

  // 解析填空题答案JSON
  function parseFillBlankAnswer(answerStr: string): string {
    if (!answerStr) return '-'
    
    try {
      const blanks = JSON.parse(answerStr)
      if (Array.isArray(blanks)) {
        return blanks.map((blank, index) => {
          const blankIndex = blank.index || index + 1
          const answer = blank.answer || ''
          return `第${blankIndex}空：${answer}`
        }).join('\n')
      }
      return answerStr
    } catch (error) {
      // 如果不是JSON格式，直接返回原字符串
      return answerStr
    }
  }

  // 解析填空题答案为数组格式（用于模板渲染）
  function parseFillBlankAnswerArray(answerStr: string): Array<{label: string, answer: string}> {
    if (!answerStr) return []
    
    try {
      const blanks = JSON.parse(answerStr)
      if (Array.isArray(blanks)) {
        return blanks.map((blank, index) => {
          const blankIndex = blank.index || index + 1
          const answer = blank.answer || ''
          return {
            label: `第${blankIndex}空：`,
            answer: answer
          }
        })
      }
      return []
    } catch (error) {
      return []
    }
  }

  // 格式化答案内容（根据题型处理）
  function formatAnswer(answer: string, questionTypeId: string): string {
    if (!answer) return '-'
    
    // 填空题特殊处理
    if (questionTypeId === 'fillblank') {
      return parseFillBlankAnswer(answer)
    }
    
    // 其他题型直接格式化HTML
    return formatContent(answer)
  }

  function init(data) {
    loadCourseOptions();
    state.dataForm.id = data.id;
    openModal();
    nextTick(() => {
      setTimeout(initData, 0);
    });
  }

  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      closeModal();
    }
  }

  function getData(id) {
    getDetailInfo(id).then(res => {
      state.dataForm = res.data || {};
      nextTick(() => {
        changeLoading(false);
      });
    });
  }

  // 加载课程选项
  function loadCourseOptions() {
    getCourseForSelect({
      dataType: 1,
    }).then(res => {
      state.courseOptions = res.data.list || [];
    });
  }

  function setFormProps(data) {
    setModalProps(data);
  }

  function changeLoading(loading) {
    setFormProps({ loading });
  }
</script>

<style lang="less" scoped>
.question-detail {
  .info-card {
    margin-bottom: 16px;
    
    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      .label {
        font-weight: 500;
        color: #666;
        min-width: 60px;
        margin-right: 8px;
      }
      
      .value {
        color: #333;
        flex: 1;
      }
    }
  }
  
  .content-card {
    .question-content {
      .content-section {
        margin-bottom: 24px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .section-title {
          font-size: 16px;
          font-weight: 600;
          color: #1890ff;
          margin-bottom: 12px;
          padding-bottom: 8px;
          border-bottom: 2px solid #f0f0f0;
        }
        
        .content-body,
        .answer-content,
        .analysis-content {
          line-height: 1.6;
          color: #333;
          background: #fafafa;
          padding: 12px;
          border-radius: 6px;
          border-left: 4px solid #1890ff;
        }
        
        .blank-answer-item {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          padding: 8px 12px;
          background: #fff;
          border-radius: 4px;
          border: 1px solid #d9d9d9;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .blank-label {
            font-weight: 600;
            color: #1890ff;
            margin-right: 8px;
            min-width: 60px;
          }
          
          .blank-value {
            flex: 1;
            color: #333;
            font-weight: 500;
          }
        }
        
        .options-list {
          .option-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 8px;
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #e8e8e8;
            
            &:hover {
              background: #f0f0f0;
            }
            
            .option-label {
              font-weight: 600;
              color: #1890ff;
              margin-right: 8px;
              min-width: 20px;
            }
            
            .option-text {
              flex: 1;
              line-height: 1.5;
              color: #333;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .question-detail {
    .info-card {
      .info-item {
        flex-direction: column;
        align-items: flex-start;
        
        .label {
          margin-bottom: 4px;
        }
      }
    }
  }
}
</style>
