<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    width="1000px"
    :minHeight="100"
    :cancelText="t('common.cancelText', '取消')"
    :okText="t('common.okText', '确定')"
    :footer="null"
    :closeFunc="onClose">
    <template #title>
      <a-space :size="10">
        <div class="text-16px font-medium">{{ title }}</div>
        <a-space-compact size="small" block v-if="dataForm.id">
          <a-tooltip :title="t('common.prevRecord')">
            <a-button size="small" :disabled="getPrevDisabled" @click="handlePrev">
              <i class="icon-ym icon-ym-caret-left text-10px"></i>
            </a-button>
          </a-tooltip>
          <a-tooltip :title="t('common.nextRecord')">
            <a-button size="small" :disabled="getNextDisabled" @click="handleNext">
              <i class="icon-ym icon-ym-caret-right text-10px"></i>
            </a-button>
          </a-tooltip>
        </a-space-compact>
      </a-space>
    </template>
    <a-steps :current="currentStep" style="margin-bottom: 24px">
      <a-step title="基础信息" />
      <a-step title="试卷配置" />
      <a-step title="防作弊配置" />
    </a-steps>
    <div v-show="currentStep === 0">
      <a-form
        :colon="false"
        size="middle"
        layout="horizontal"
        labelAlign="left"
        :labelCol="{ style: { width: '100px' } }"
        :model="state.dataForm"
        :rules="state.dataRule"
        ref="formRef">
        <a-row :gutter="15">
          <a-col :span="12" class="ant-col-item">
            <a-form-item name="name" :rules="[{ required: true, message: '名称不能为空' }]">
              <template #label>名称</template>
              <XundaInput
                v-model:value="state.dataForm.name"
                placeholder="请输入名称"
                :allowClear="true"
                :style="{ width: '100%' }"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12" class="ant-col-item">
            <a-form-item name="semesterId" :rules="[{ required: !state.dataForm.courseId, message: '计划或课程必须填写其一' }]">
              <template #label>计划</template>
              <XundaSelect
                v-model:value="state.dataForm.semesterId"
                placeholder="请选择计划"
                :allowClear="true"
                :style="{ width: '100%' }"
                showSearch
                :options="state.optionsObj.semesterIdOptions"
                :fieldNames="state.optionsObj.semesterIdFieldNames"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12" class="ant-col-item">
            <a-form-item name="courseId" :rules="[{ required: !state.dataForm.semesterId, message: '计划或课程必须填写其一' }]">
              <template #label>课程</template>
              <XundaSelect
                v-model:value="state.dataForm.courseId"
                placeholder="请选择课程"
                :allowClear="true"
                :style="{ width: '100%' }"
                showSearch
                :options="state.optionsObj.courseIdOptions"
                :fieldNames="{ label: 'name', value: 'id' }"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12" class="ant-col-item">
            <a-form-item name="beginTime" :rules="[{ required: true, message: '开始时间不能为空' }]">
              <template #label>开始时间</template>
              <a-date-picker
                v-model:value="state.dataForm.beginTime"
                show-time
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DDTHH:mm:ss"
                placeholder="请选择开始时间"
                style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12" class="ant-col-item">
            <a-form-item name="endTime" :rules="[{ required: true, message: '结束时间不能为空' }]">
              <template #label>结束时间</template>
              <a-date-picker
                v-model:value="state.dataForm.endTime"
                show-time
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DDTHH:mm:ss"
                placeholder="请选择结束时间"
                style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12" class="ant-col-item">
            <a-form-item name="prepareTime" :rules="[{ required: true, message: '准备时间不能为空' }]">
              <template #label>准备时间</template>
              <XundaInputNumber v-model:value="state.dataForm.prepareTime" placeholder="准备时间" :allowClear="true" style="width: 100%" :addonAfter="'分钟'" />
            </a-form-item>
          </a-col>
          <a-col :span="12" class="ant-col-item">
            <a-form-item name="limitTime" :rules="[{ required: true, message: '考试限时不能为空' }]">
              <template #label>考试限时</template>
              <XundaInputNumber v-model:value="state.dataForm.limitTime" placeholder="考试限时" :allowClear="true" style="width: 100%" :addonAfter="'分钟'" />
            </a-form-item>
          </a-col>
          <a-col :span="12" class="ant-col-item">
            <a-form-item name="submitTime" :rules="[{ required: true, message: '可交卷时间不能为空' }]">
              <template #label>可交卷时间</template>
              <XundaInputNumber v-model:value="state.dataForm.submitTime" placeholder="可交卷时间" :allowClear="true" style="width: 100%" :addonAfter="'分钟'" />
            </a-form-item>
          </a-col>
          <a-col :span="12" class="ant-col-item">
            <a-form-item name="lateTime" :rules="[{ required: true, message: '允许迟到时间不能为空' }]">
              <template #label>允许迟到时间</template>
              <XundaInputNumber v-model:value="state.dataForm.lateTime" placeholder="允许迟到时间" :allowClear="true" style="width: 100%" :addonAfter="'分钟'" />
            </a-form-item>
          </a-col>
          <a-col :span="12" class="ant-col-item">
            <a-form-item name="passScore" :rules="[{ required: true, message: '及格分不能为空' }]">
              <template #label>及格分</template>
              <XundaInput v-model:value="state.dataForm.passScore" placeholder="及格分" :allowClear="true" :style="{ width: '100%' }" />
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="note">
              <template #label>考试须知</template>
              <Tinymce v-model:value="state.dataForm.note" placeholder="考试须知" :style="{ width: '100%' }" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div v-show="currentStep === 1">
      <!-- 试卷配置表单，最终序列化为JSON存dataForm.examConfig -->
      <a-form layout="vertical" :model="examConfigForm">
        <a-form-item>
          <XundaCheckboxSingle v-model:value="examConfigForm.allowViewPaper" label="允许学生考后查看试卷" />
        </a-form-item>
        <a-form-item v-if="examConfigForm.allowViewPaper">
          <a-select v-model:value="examConfigForm.viewPaperTime" style="width: 180px">
            <a-select-option value="afterSubmit">学生提交后</a-select-option>
            <a-select-option value="afterEnd">考试结束后</a-select-option>
          </a-select>
          <XundaCheckboxSingle v-model:value="examConfigForm.allowViewAnswer" label="允许查看答案" style="margin-left: 16px" />
        </a-form-item>
        <a-form-item>
          <XundaCheckboxSingle v-model:value="examConfigForm.allowViewWrong" label="允许学生查看题目对错" />
        </a-form-item>
        <a-form-item>
          <XundaCheckboxSingle v-model:value="examConfigForm.allowViewScore" label="允许学生查看分数" />
        </a-form-item>
        <a-form-item>
          <XundaCheckboxSingle v-model:value="examConfigForm.allowViewTypeScore" label="允许学生查看题型分数" />
        </a-form-item>
        <a-form-item>
          <XundaCheckboxSingle v-model:value="examConfigForm.allowViewLevel" label="允许学生查看考试等级" />
        </a-form-item>
        <a-form-item>
          <XundaCheckboxSingle v-model:value="examConfigForm.allowViewRank" label="允许学生查看排名" />
        </a-form-item>
        <a-form-item>
          <XundaCheckboxSingle v-model:value="examConfigForm.showFinishTip" label="学生考试完成后展示提示信息" />
        </a-form-item>
      </a-form>
    </div>
    <div v-show="currentStep === 2">
      <!-- 防作弊配置表单，最终序列化为JSON存dataForm.chatConfig -->
      <a-form layout="vertical" :model="chatConfigForm">
        <a-form-item>
          <XundaCheckboxSingle v-model:value="chatConfigForm.randomQuestion" label="题目乱序" />
        </a-form-item>
        <a-form-item>
          <XundaCheckboxSingle v-model:value="chatConfigForm.randomOption" label="选项乱序" />
        </a-form-item>
        <a-form-item>
          <XundaCheckboxSingle v-model:value="chatConfigForm.forbidMultiDevice" label="禁止学生多终端考试" />
        </a-form-item>
        <a-form-item>
          <XundaCheckboxSingle v-model:value="chatConfigForm.snapMonitor" label="考试过程中抓拍监控" />
        </a-form-item>
        <a-form-item>
          <XundaCheckboxSingle v-model:value="chatConfigForm.screenControl" label="考试过程中切屏控制" />
        </a-form-item>
        <a-form-item>
          <XundaCheckboxSingle v-model:value="chatConfigForm.leaveLimit" label="学生离开作答页面" />
          <a-input-number v-if="chatConfigForm.leaveLimit" v-model:value="chatConfigForm.leaveLimitCount" min="1" style="width: 100px; margin-left: 8px" />
          <span v-if="chatConfigForm.leaveLimit" style="margin-left: 8px">次，系统强制收卷</span>
        </a-form-item>
        <a-form-item>
          <XundaCheckboxSingle v-model:value="chatConfigForm.onlyApp" label="只允许" />
          <a-select v-if="chatConfigForm.onlyApp" v-model:value="chatConfigForm.onlyAppType" style="width: 120px; margin-left: 8px">
            <a-select-option value="app">手机APP</a-select-option>
            <a-select-option value="web">网页</a-select-option>
          </a-select>
          <span v-if="chatConfigForm.onlyApp" style="margin-left: 8px">考试</span>
        </a-form-item>
      </a-form>
    </div>
    <div style="margin-top: 24px; margin-bottom: 24px; text-align: right">
      <a-button v-if="currentStep > 0" @click="prevStep" style="margin-right: 8px">上一步</a-button>
      <a-button v-if="currentStep < 2" type="primary" @click="nextStep">下一步</a-button>
      <a-button v-if="currentStep === 2" type="primary" @click="handleSubmit">完成</a-button>
    </div>
    <template #insertFooter>
      <div class="loat-left mt-5px">
        <XundaCheckboxSingle v-model:value="submitType" :label="continueText" />
      </div>
    </template>
    <ExamConfigForm ref="examConfigFormRef" @reload="reload" />
  </BasicModal>
</template>
<script lang="ts" setup>
import { create, update, getInfo } from '@/views/exam/examConfig';
import { reactive, nextTick, ref, unref, computed, inject, watch } from 'vue';
import { BasicModal, useModal } from '@/components/Modal';
import { useMessage } from '@/hooks/web/useMessage';
import { useI18n } from '@/hooks/web/useI18n';
import { useUserStore } from '@/store/modules/user';
import type { FormInstance } from 'ant-design-vue';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
import { usePermission } from '@/hooks/web/usePermission';
import { Tinymce } from '@/components/Tinymce';
import { XundaInput } from '@/components/Xunda/Input';
import { XundaCheckbox } from '@/components/Xunda/Checkbox';
import { XundaSwitch } from '@/components/Xunda/Switch';
import { XundaCheckboxSingle } from '@/components/Xunda/Checkbox';
import { XundaSelect } from '@/components/Xunda/Select';
import { getForSelect as getSemesterForSelect } from '@/views/plan/semester';
import { getForSelect as getCourseForSelect } from '@/views/ea/course';
import ExamConfigForm from '@/views/exam/examConfig/form.vue';
const examConfigFormRef = ref();
const emit = defineEmits(['reload']);
const getLeftTreeActiveInfo: (() => any) | null = inject('getLeftTreeActiveInfo', null);
const userStore = useUserStore();
const userInfo = userStore.getUserInfo;
const { createMessage, createConfirm } = useMessage();
const { t } = useI18n();
const [registerModal, { openModal, setModalProps }] = useModal();
const formRef = ref<FormInstance>();
const currentStep = ref(0);
const state = reactive({
  dataForm: {
    id: '',
    name: '',
    courseId: '',
    semesterId: '',
    beginTime: undefined,
    endTime: undefined,
    prepareTime: 15,
    limitTime: 120,
    submitTime: 15,
    lateTime: 10,
    note: '',
    totalScore: undefined,
    passScore: 60,
    questionCount: undefined,
    examConfig: '',
    chatConfig: '',
    paperMode: '',
    randomOrderFlag: undefined,
    randomOpitonFlag: undefined,
    paperIds: '',
    questionConfig: '',
    examState: '',
  },
  currIndex: 0,
  allList: [] as any[],
  isContinue: false,
  submitType: 0,
  showContinueBtn: true,
  title: '',
  continueText: '',
  childIndex: -1,
  isEdit: false,
  interfaceRes: {},
  ableAll: {},
  maskConfig: {},
  locationScope: { faddressDetail: [] },
  dataRule: {},
  optionsObj: {
    defaultProps: { label: 'fullName', value: 'enCode' },
    courseIdOptions: [],
    semesterIdOptions: [],
    semesterIdFieldNames: { label: 'name', value: 'id' },
  },
});
const examConfigForm = reactive({
  allowViewPaper: false,
  viewPaperTime: 'afterSubmit',
  allowViewAnswer: false,
  allowViewWrong: false,
  allowViewScore: false,
  allowViewTypeScore: false,
  allowViewLevel: false,
  allowViewRank: false,
  showFinishTip: false,
});
const chatConfigForm = reactive({
  randomQuestion: false,
  randomOption: false,
  forbidMultiDevice: false,
  snapMonitor: false,
  screenControl: false,
  leaveLimit: false,
  leaveLimitCount: 1,
  onlyApp: false,
  onlyAppType: 'app',
});
const { title, continueText, dataRule, dataForm, submitType } = state;
const getPrevDisabled = computed(() => state.currIndex === 0);
const getNextDisabled = computed(() => state.currIndex === state.allList.length - 1);
const { hasFormP } = usePermission();

defineExpose({ init });

function init(data) {
  state.submitType = 0;
  state.isContinue = false;
  state.title = !data.id ? t('common.add2Text', '新增') : t('common.editText', '编辑');
  state.continueText = !data.id ? t('common.continueAndAddText', '确定并新增') : t('common.continueText', '确定并继续');
  setFormProps({ continueLoading: false });
  state.dataForm.id = data.id;
  if (data.paperIds) {
    state.dataForm.paperIds = data.paperIds;
  }
  openModal();
  state.allList = data.allList;
  state.currIndex = state.allList.length && data.id ? state.allList.findIndex(item => item.id === data.id) : 0;
  getAllSelectOptions();
  nextTick(() => {
    getForm().resetFields();
    setTimeout(initData, 0);
  });
}
function initData() {
  changeLoading(true);
  if (state.dataForm.id) {
    getData(state.dataForm.id);
  } else {
    // 设置默认值
    const oldPaperIds = state.dataForm.paperIds;
    state.dataForm = {
      id: '',
      name: '',
      courseId: '',
      semesterId: '',
      beginTime: undefined,
      endTime: undefined,
      prepareTime: 15,
      limitTime: 120,
      submitTime: 15,
      lateTime: 10,
      note: '', 
      totalScore: undefined,
      passScore: 60,
      questionCount: undefined,
      examConfig: '',
      chatConfig: '',
      paperMode: '',
      randomOrderFlag: undefined,
      randomOpitonFlag: undefined,
      paperIds: oldPaperIds ? String(oldPaperIds) : '',
      questionConfig: '',
      examState: '',
    };
    if (getLeftTreeActiveInfo) state.dataForm = { ...state.dataForm, ...(getLeftTreeActiveInfo() || {}) };
    // 新建时重置配置表单
    Object.assign(examConfigForm, {
      allowViewPaper: false,
      viewPaperTime: 'afterSubmit',
      allowViewAnswer: false,
      allowViewWrong: false,
      allowViewScore: false,
      allowViewTypeScore: false,
      allowViewLevel: false,
      allowViewRank: false,
      showFinishTip: false,
    });
    Object.assign(chatConfigForm, {
      randomQuestion: false,
      randomOption: false,
      forbidMultiDevice: false,
      snapMonitor: false,
      screenControl: false,
      leaveLimit: false,
      leaveLimitCount: 1,
      onlyApp: false,
      onlyAppType: 'app',
    });
    state.childIndex = -1;
    changeLoading(false);
  }
}
function getData(id) {
  getInfo(id).then(res => {
    state.dataForm = res.data || {};
    // 回显试卷配置
    if (state.dataForm.examConfig) {
      try {
        const config = JSON.parse(state.dataForm.examConfig);
        Object.assign(examConfigForm, config);
      } catch {}
    } else {
      // 没有配置时重置
      Object.assign(examConfigForm, {
        allowViewPaper: false,
        viewPaperTime: 'afterSubmit',
        allowViewAnswer: false,
        allowViewWrong: false,
        allowViewScore: false,
        allowViewTypeScore: false,
        allowViewLevel: false,
        allowViewRank: false,
        showFinishTip: false,
      });
    }
    // 回显防作弊配置
    if (state.dataForm.chatConfig) {
      try {
        const config = JSON.parse(state.dataForm.chatConfig);
        Object.assign(chatConfigForm, config);
      } catch {}
    } else {
      Object.assign(chatConfigForm, {
        randomQuestion: false,
        randomOption: false,
        forbidMultiDevice: false,
        snapMonitor: false,
        screenControl: false,
        leaveLimit: false,
        leaveLimitCount: 1,
        onlyApp: false,
        onlyAppType: 'app',
      });
    }
    state.childIndex = -1;
    changeLoading(false);
  });
}
function handlePrev() {
  state.currIndex--;
  handleGetNewInfo();
}
function handleNext() {
  state.currIndex++;
  handleGetNewInfo();
}
function handleGetNewInfo() {
  changeLoading(true);
  getForm().resetFields();
  const id = state.allList[state.currIndex].id;
  getData(id);
}
function setFormProps(data) {
  setModalProps(data);
}
function changeLoading(loading) {
  setModalProps({ loading });
}
async function onClose() {
  if (state.isContinue) emit('reload');
  return true;
}
function changeData(model, index) {
  state.isEdit = false;
  state.childIndex = index;
  for (let key in state.interfaceRes) {
    if (key != model) {
      let faceReList = state.interfaceRes[key];
      for (let i = 0; i < faceReList.length; i++) {
        let relationField = faceReList[i].relationField;
        if (relationField) {
          let modelAll = relationField.split('-');
          let faceMode = '';
          let faceMode2 = modelAll.length == 2 ? modelAll[0].substring(0, modelAll[0].length - 4) + modelAll[1] : '';
          for (let i = 0; i < modelAll.length; i++) {
            faceMode += modelAll[i];
          }
          if (faceMode == model || faceMode2 == model) {
            let options = 'get' + key + 'Options';
            eval(options)(true);
            changeData(key, index);
          }
        }
      }
    }
  }
}
function getAllSelectOptions() {
  state.optionsObj.semesterIdFieldNames = { label: 'name', value: 'id' };
  getSemesterForSelect({ dataType: 1 }).then(res => {
    state.optionsObj.semesterIdOptions = res.data.list;
  });
}
watch(() => state.dataForm.semesterId, (val) => {
  if (val) {
    getCourseForSelect({ semesterId: val, dataType: 1 }).then(res => {
      state.optionsObj.courseIdOptions = res.data.list;
    });
  } else {
    state.optionsObj.courseIdOptions = [];
  }
});
function nextStep() {
  if (currentStep.value === 0) {
    getForm().validate().then(() => {
      currentStep.value++;
    });
  } else {
    currentStep.value++;
  }
}
function prevStep() {
  currentStep.value--;
}
function handleSubmit() {
  // 步骤2、3表单序列化
  state.dataForm.examConfig = JSON.stringify(examConfigForm);
  state.dataForm.chatConfig = JSON.stringify(chatConfigForm);
  // 原有提交逻辑
  submitOrigin();
}
function submitOrigin() {
  // 判断是新增还是编辑
  const isEdit = !!state.dataForm.id;
  const api = isEdit ? update : create;
  setFormProps({ confirmLoading: true });
  api(state.dataForm)
    .then(res => {
      createMessage.success(res.msg || '操作成功');
      setFormProps({ open: false, confirmLoading: false });
      emit('reload');
    })
    .catch(() => {
      setFormProps({ confirmLoading: false });
    });
}
function getForm() {
  const form = unref(formRef);
  if (!form) {
    throw new Error('form is null!');
  }
  return form;
}
function handlePublish(record) {
  // 弹出 examConfig 的 form，并传 paperIds
  examConfigFormRef.value?.init({
    id: '', // 新建
    paperIds: record.id, // 传递 paper 的 id
    allList: [], // 可选，通常传空数组
  });
}
</script>
