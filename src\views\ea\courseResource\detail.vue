<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" width="600px" :minHeight="100" :showOkBtn="false">
    <template #insertFooter> </template>
    <!-- 表单 -->
    <a-row class="dynamic-form">
      <a-form :colon="false" size="middle" layout="horizontal" labelAlign="right" :labelCol="{ style: { width: '100px' } }" :model="dataForm" ref="formRef">
        <a-row :gutter="15">
          <!-- 具体表单 -->
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="courseId">
              <template #label>课程 </template>
              <p>{{ xundaUtils.optionText(dataForm.courseId, optionsObj.courseIdOptions, optionsObj.courseIdFieldNames) }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="courseId">
              <template #label>父级 </template>
              <XundaInput v-model:value="dataForm.courseId" placeholder="请输入" disabled detailed allowClear :style="{ width: '100%' }"> </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="displayOrder">
              <template #label>显示顺序 </template>
              <XundaInputNumber v-model:value="dataForm.displayOrder" :precision="2" :controls="true" disabled detailed allowClear :style="{ width: '100%' }">
              </XundaInputNumber>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="viewRole">
              <template #label>公开模式 </template>
              <XundaInput v-model:value="dataForm.viewRole" placeholder="请输入" disabled detailed allowClear :style="{ width: '100%' }"> </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="leafFlag">
              <template #label>叶子节点 </template>
              <p>{{ xundaUtils.isRender(dataForm.leafFlag) }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="attachment">
              <template #label>附件 </template>
              <XundaUploadFile v-model:value="dataForm.attachment" disabled detailed />
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="description">
              <template #label>描述 </template>
              <p>{{ dataForm.description }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="viewCount">
              <template #label>访问人数 </template>
              <XundaInputNumber v-model:value="dataForm.downloadCount" :precision="2" :controls="true" disabled detailed allowClear :style="{ width: '100%' }">
              </XundaInputNumber>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="downloadCount">
              <template #label>下载次数 </template>
              <XundaInputNumber v-model:value="dataForm.downloadCount" :precision="2" :controls="true" disabled detailed allowClear :style="{ width: '100%' }">
              </XundaInputNumber>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="size">
              <template #label>大小 </template>
              <XundaInputNumber v-model:value="dataForm.size" :precision="2" :controls="true" disabled detailed allowClear :style="{ width: '100%' }">
              </XundaInputNumber>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="creator">
              <template #label>创建人 </template>
              <XundaInput v-model:value="dataForm.creator" placeholder="请输入" disabled detailed allowClear :style="{ width: '100%' }"> </XundaInput>
            </a-form-item>
          </a-col>
          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { getDetailInfo } from '@/views/ea/courseResource';
  import { reactive, toRefs, nextTick, ref } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { usePermission } from '@/hooks/web/usePermission';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
  import { xundaUtils } from '@/utils/xunda';
  interface State {
    dataForm: any;
    title: string;
    maskConfig: any;
    optionsObj: any;
  }
  defineOptions({ name: 'Detail' });
  const emit = defineEmits(['reload']);
  const { createMessage, createConfirm } = useMessage();
  const [registerModal, { openModal, setModalProps, closeModal }] = useModal();
  const { t } = useI18n();
  const detailRef = ref<any>(null);
  const visitRecordGridRef = ref<any>(null);
  const state = reactive<State>({
    dataForm: {},
    maskConfig: {},
    title: t('common.detailText', '详情'),
    optionsObj: {
      //选项配置
      defaultProps: { key: 'enCode', value: 'fullName' }, // 默认下拉选择键值
    },
  });

  const { title, dataForm, optionsObj, maskConfig } = toRefs(state);
  // 表单权限
  const { hasFormP } = usePermission();

  defineExpose({ init });

  function init(data) {
    getAllSelectOptions();
    state.dataForm.id = data.id;
    openModal();
    nextTick(() => {
      setTimeout(initData, 0);
    });
  }

  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      closeModal();
    }
  }

  function getData(id) {
    getDetailInfo(id).then(res => {
      state.dataForm = res.data || {};
      state.dataForm.attachment = res.data.attachment ? JSON.parse(res.data.attachment) : [];
      nextTick(() => {
        changeLoading(false);
      });
    });
  }

  function setFormProps(data) {
    setModalProps(data);
  }

  function changeLoading(loading) {
    setFormProps({ loading });
  }

  function getAllSelectOptions() {
    state.optionsObj.courseIdFieldNames = { label: 'name', value: 'id' };
    state.optionsObj.courseIdOptions = [];
  }
</script>
