import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';

// 基础Api
export const certificationApi = '/api/study/certification';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: certificationApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: certificationApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: certificationApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: certificationApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: certificationApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: certificationApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: certificationApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: certificationApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: certificationApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: certificationApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: certificationApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: certificationApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: certificationApi + `/getForSelect`, data });
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'name',
    label: t('收件人'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('证书'),
    dataIndex: 'id',
    width: 120,
  },
  {
    title: t('报名信息'),
    dataIndex: 'registrationId',
    width: 120,
  },
  {
    title: t('地址'),
    dataIndex: 'addressJson',
    width: 120,
  },
  {
    title: t('证书类型'),
    dataIndex: 'certificationType',
    width: 120,
  },
  {
    title: t('证书份数'),
    dataIndex: 'numOfCopies',
    width: 120,
  },
  {
    title: t('快递费币种'),
    dataIndex: 'expressCurrency',
    width: 120,
  },
  {
    title: t('快递费'),
    dataIndex: 'expressFee',
    width: 120,
  },
  {
    title: t('快递商家'),
    dataIndex: 'expressType',
    width: 120,
  },
  {
    title: t('快递号'),
    dataIndex: 'expressNo',
    width: 120,
  },
  {
    title: t('邮箱地址'),
    dataIndex: 'email',
    width: 120,
  },
  {
    title: t('发送时间'),
    dataIndex: 'sentTime',
    width: 120,
  },
  {
    title: t('电子证书校验码'),
    dataIndex: 'checkCode',
    width: 120,
  },
  {
    title: t('国家'),
    dataIndex: 'country',
    width: 120,
  },
  {
    title: t('省'),
    dataIndex: 'province',
    width: 120,
  },
  {
    title: t('市'),
    dataIndex: 'city',
    width: 120,
  },
  {
    title: t('区'),
    dataIndex: 'district',
    width: 120,
  },
  {
    title: t('地址'),
    dataIndex: 'address',
    width: 120,
  },
  {
    title: t('邮编'),
    dataIndex: 'postcode',
    width: 120,
  },
  {
    title: t('收件人电话'),
    dataIndex: 'phone',
    width: 120,
  },
  {
    title: t('收件人'),
    dataIndex: 'name',
    width: 120,
  },
  {
    title: t('证书模板'),
    dataIndex: 'certTemplateId',
    width: 120,
  },
  {
    title: t('证书信息'),
    dataIndex: 'certInfo',
    width: 120,
  },
  {
    title: t('支付记录'),
    dataIndex: 'paymentId',
    width: 120,
  },
  {
    title: t('支付状态'),
    dataIndex: 'payState',
    width: 120,
  },
  {
    title: t('币种'),
    dataIndex: 'currency',
    width: 120,
  },
  {
    title: t('费用调整流程'),
    dataIndex: 'flowId',
    width: 120,
  },
  {
    title: t('原价'),
    dataIndex: 'originalPrice',
    width: 120,
  },
  {
    title: t('实价'),
    dataIndex: 'realPrice',
    width: 120,
  },
  {
    title: t('费用调整流程实例'),
    dataIndex: 'flowTaskId',
    width: 120,
  },
];
