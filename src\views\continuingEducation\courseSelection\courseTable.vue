<template>
  <div class="xunda-content-wrapper">
    <div class="xunda-content-wrapper-center">
      <div class="xunda-content-wrapper-search-box">
        <BasicForm
          @register="registerSearchForm"
          :schemas="searchSchemas"
          @advanced-change="redoHeight"
          @submit="handleSearchSubmit"
          @reset="handleSearchReset"
          class="search-form">
        </BasicForm>
      </div>
      <div class="xunda-content-wrapper-content bg-white">
        <BasicTable @register="registerTable" ref="tableRef"> </BasicTable>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { getList, columns, searchSchemas } from './courseTable';
  import { ref, reactive, onMounted, nextTick } from 'vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { BasicForm, useForm } from '@/components/Form';
  import { BasicTable, useTable, TableActionType } from '@/components/Table';
  import { useRoute } from 'vue-router';
  import { cloneDeep } from 'lodash-es';
  import { useUserStore } from '@/store/modules/user';
  import { usePermission } from '@/hooks/web/usePermission';
  import { getCourseTypeForSelect } from '../course';

  const emit = defineEmits(['selection-change', 'reload']);

  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;
  const { hasFormP } = usePermission();
  const route = useRoute();
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const formRef = ref<any>(null);
  const tableRef = ref<Nullable<TableActionType>>(null);
  const detailRef = ref<any>(null);
  const cacheList = ref<any>([]);
  const defaultSearchInfo = {
    menuId: route.meta.modelId as string,
    moduleId: '***********', //模块ID
    superQueryJson: '',
    dataType: 0,
    semesterId: '1969226028783226882',
  };

  const searchInfo = reactive({
    ...cloneDeep(defaultSearchInfo),
  });

  const [registerSearchForm, { updateSchema }] = useForm({
    baseColProps: { span: 18 },
    autoAdvancedLine: 1,
    wrapperCol: { span: 24 },
    submitOnChange: true,
    autoAdvancedLine: 2,
    alwaysShowLines: 1,
    showActionButtonGroup: true,
    showAdvancedButton: true,
    compact: false,
  });

  const getTableList = params => {
    emit('reload', params);
    return getList(params);
  };

  const [registerTable, { reload, setLoading, getFetchParams, redoHeight, getSelectRowKeys, clearSelectedRowKeys, getDataSource }] = useTable({
    api: getTableList,
    columns: columns,
    searchInfo: searchInfo,
    clickToRowSelect: true,
    afterFetch: data => {
      const list = data.map(o => ({
        ...o,
      }));
      cacheList.value = cloneDeep(list);
      return list;
    },
    rowSelection: {
      type: 'checkbox',
      onChange: (selectedRowKeys, selectedRows) => {
        emit('selection-change', selectedRowKeys, selectedRows);
      },
      getCheckboxProps: record => ({
        disabled: record.top,
      }),
    },
  });

  function handleSearchReset() {
    clearSelectedRowKeys();
  }

  function handleSearchSubmit(data) {
    clearSelectedRowKeys();
    let obj = {
      ...defaultSearchInfo,
      superQueryJson: searchInfo.superQueryJson,
      ...data,
    };
    Object.keys(searchInfo).map(key => {
      delete searchInfo[key];
    });
    for (let [key, value] of Object.entries(obj)) {
      searchInfo[key.replaceAll('-', '_')] = value;
    }
    reload({ page: 1 });
  }

  // 设置查询表单
  function setSearchSchema() {
    getCourseTypeForSelect().then(res => {
      updateSchema({
        field: 'courseType',
        componentProps: { options: res.data?.list || [], fieldNames: { label: 'fullName', value: 'enCode', children: 'children' } },
      });
    });
  }
  defineExpose({ getDataSource });

  onMounted(() => {
    setSearchSchema();
    setTimeout(() => {
      nextTick(() => {
        emit('reload');
      });
    }, 500);
  });
</script>
