import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { defHttp } from '@/utils/http/axios';

const { t } = useI18n();

// 基础Api
export const registrationApi = '/api/study/registration';

/**
 * 获取列表
 * @param data
 * @returns
 */
export function getUserList(data) {
  return defHttp.post({ url: registrationApi + `/getList`, data });
}

export function batchSimpleAudit(data) {
  return defHttp.post({ url: registrationApi + `/batchSimpleAudit`, data });
}

export function exportThePreRegistrationData(data) {
  return defHttp.post({ url: registrationApi + `/exportThePreRegistrationData`, data });
}

/**
 * 搜索表单配置
 */
export const userSearchSchemas: FormSchema[] = [
  {
    field: 'name',
    label: t('姓名'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
  {
    field: 'idNo',
    label: t('身份证号'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
];

/**
 * 表格列配置
 */
export const userColumns: BasicColumn[] = [
  {
    title: t('身份证号'),
    dataIndex: 'idNo',
    width: 120,
  },
  {
    title: t('姓名'),
    dataIndex: 'name',
    width: 120,
  },
];
