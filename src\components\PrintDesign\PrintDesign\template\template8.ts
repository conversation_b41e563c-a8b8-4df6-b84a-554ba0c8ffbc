export default [
  {
    options: {
      left: 30,
      top: 48,
      height: 52.5,
      width: 534,
      title: '费用报销单',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 21.75,
      fontWeight: 'bold',
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      right: 565.5,
      bottom: 96.74218368530273,
      vCenter: 298.5,
      hCenter: 82.49218368530273,
      backgroundColor: '#ffffff',
      letterSpacing: 12,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 184.5,
      top: 88.5,
      height: 9,
      width: 213,
      borderWidth: 0.75,
      right: 320.9948043823242,
      bottom: 97.48958587646484,
      vCenter: 253.49480438232422,
      hCenter: 92.98958587646484,
    },
    printElementType: { title: '横线', type: 'hline' },
  },
  {
    options: {
      left: 184.5,
      top: 91.5,
      height: 9,
      width: 213,
      borderWidth: 0.75,
      right: 342.7448043823242,
      bottom: 102,
      vCenter: 263.9948043823242,
      hCenter: 97.5,
    },
    printElementType: { title: '横线', type: 'hline' },
  },
  {
    options: {
      left: 333,
      top: 109.5,
      height: 13.5,
      width: 21,
      title: '日',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      right: 278.49478912353516,
      bottom: 543.2395935058594,
      vCenter: 267.99478912353516,
      hCenter: 536.4895935058594,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 426,
      top: 109.5,
      height: 13.5,
      width: 27,
      title: 'NO：                            ',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      color: '#ff0000',
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      right: 449.49739837646484,
      bottom: 116.48959350585938,
      vCenter: 438.99739837646484,
      hCenter: 109.73959350585938,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 280.5,
      top: 109.5,
      height: 13.5,
      width: 21,
      title: '月',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      right: 218.49481201171875,
      bottom: 543.9895935058594,
      vCenter: 207.99481201171875,
      hCenter: 537.2395935058594,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 228,
      top: 109.5,
      height: 13.5,
      width: 21,
      title: '年',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      right: 272.9948043823242,
      bottom: 544.4974365234375,
      vCenter: 212.99480438232422,
      hCenter: 537.7474365234375,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 453,
      top: 121.5,
      height: 9,
      width: 111,
      borderWidth: 0.75,
      right: 563.9921951293945,
      bottom: 124.49739074707031,
      vCenter: 509.24219512939453,
      hCenter: 119.99739074707031,
    },
    printElementType: { title: '横线', type: 'hline' },
  },
  {
    options: {
      left: 294,
      top: 132,
      height: 32,
      width: 132,
      title: '所属部门',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      qrCodeLevel: 0,
      right: 425.2474136352539,
      bottom: 163.24219512939453,
      vCenter: 359.2474136352539,
      hCenter: 147.24219512939453,
      fontSize: 11.25,
      backgroundColor: '#dbdbdb',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 162,
      top: 132,
      height: 32,
      width: 133.5,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      qrCodeLevel: 0,
      right: 295.4948043823242,
      bottom: 162.49478149414062,
      vCenter: 228.74480438232422,
      hCenter: 146.49478149414062,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 426,
      top: 132,
      height: 32,
      width: 138,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      qrCodeLevel: 0,
      right: 563.989631652832,
      bottom: 132.49478149414062,
      vCenter: 494.98963165283203,
      hCenter: 116.49478149414062,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 132,
      height: 32,
      width: 132,
      title: '申请人',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      qrCodeLevel: 0,
      right: 161.2474136352539,
      bottom: 163.99219512939453,
      vCenter: 95.2474136352539,
      hCenter: 147.99219512939453,
      fontSize: 11.25,
      backgroundColor: '#dbdbdb',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 354,
      top: 162.5,
      height: 32,
      width: 210,
      title: ' 金额',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderRight: 'solid',
      qrCodeLevel: 0,
      right: 379.74222564697266,
      bottom: 194.24478912353516,
      vCenter: 364.74222564697266,
      hCenter: 178.24478912353516,
      fontSize: 11.25,
      backgroundColor: '#dbdbdb',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 162,
      top: 162.5,
      height: 64,
      width: 132,
      title: '报销内容',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 291.74219512939453,
      bottom: 225.24219512939453,
      vCenter: 225.74219512939453,
      hCenter: 193.24219512939453,
      fontSize: 11.25,
      backgroundColor: '#dbdbdb',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 93,
      top: 162.5,
      height: 64,
      width: 69,
      title: '费用项目',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 163.49480438232422,
      bottom: 227.48958587646484,
      vCenter: 128.99480438232422,
      hCenter: 195.48958587646484,
      fontSize: 11.25,
      backgroundColor: '#dbdbdb',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 294,
      top: 162.5,
      height: 64,
      width: 60,
      title: ' 单据张数',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 353.2474136352539,
      bottom: 227.48958587646484,
      vCenter: 323.2474136352539,
      hCenter: 195.48958587646484,
      fontSize: 11.25,
      backgroundColor: '#dbdbdb',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 162.5,
      height: 64,
      width: 63,
      title: '序号',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 93.75,
      bottom: 228.99219512939453,
      vCenter: 62.25,
      hCenter: 196.99219512939453,
      fontSize: 11.25,
      backgroundColor: '#dbdbdb',
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 384,
      top: 193.5,
      height: 33,
      width: 30,
      title: ' 千',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      qrCodeLevel: 0,
      right: 413.9974136352539,
      bottom: 227,
      vCenter: 398.9974136352539,
      hCenter: 211,
      fontSize: 11.25,
      borderBottom: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 474,
      top: 193.5,
      height: 33,
      width: 30,
      title: ' 元',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      qrCodeLevel: 0,
      right: 510.23961639404297,
      bottom: 194.99478912353516,
      vCenter: 495.23961639404297,
      hCenter: 178.99478912353516,
      fontSize: 11.25,
      borderBottom: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 444,
      top: 193.5,
      height: 33,
      width: 30,
      title: ' 十',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      qrCodeLevel: 0,
      right: 473.9974136352539,
      bottom: 227,
      vCenter: 458.9974136352539,
      hCenter: 211,
      fontSize: 11.25,
      borderBottom: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 504,
      top: 193.5,
      height: 33,
      width: 30,
      title: ' 角',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      qrCodeLevel: 0,
      right: 533.9974136352539,
      bottom: 227,
      vCenter: 518.9974136352539,
      hCenter: 211,
      fontSize: 11.25,
      borderBottom: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 414,
      top: 193.5,
      height: 33,
      width: 30,
      title: ' 百',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      qrCodeLevel: 0,
      right: 443.9974136352539,
      bottom: 227,
      vCenter: 428.9974136352539,
      hCenter: 211,
      fontSize: 11.25,
      borderBottom: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 534,
      top: 193.5,
      height: 33,
      width: 30,
      title: ' 分',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      qrCodeLevel: 0,
      right: 563.9974136352539,
      bottom: 227,
      vCenter: 548.9974136352539,
      hCenter: 211,
      fontSize: 11.25,
      borderBottom: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 354,
      top: 193.5,
      height: 33,
      width: 30,
      title: ' 万',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      qrCodeLevel: 0,
      right: 383.994140625,
      bottom: 226.9892578125,
      vCenter: 368.994140625,
      hCenter: 210.4892578125,
      fontSize: 11.25,
      borderBottom: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 354,
      top: 225,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      qrCodeLevel: 0,
      right: 383.994140625,
      bottom: 258.494140625,
      vCenter: 368.994140625,
      hCenter: 242.494140625,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 384,
      top: 225,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      qrCodeLevel: 0,
      right: 414.23961639404297,
      bottom: 258.7395935058594,
      vCenter: 399.23961639404297,
      hCenter: 242.73959350585938,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 414,
      top: 225,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      qrCodeLevel: 0,
      right: 436.5000228881836,
      bottom: 269,
      vCenter: 421.5000228881836,
      hCenter: 253,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 444,
      top: 225,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      qrCodeLevel: 0,
      right: 476.99219512939453,
      bottom: 258.4973907470703,
      vCenter: 461.99219512939453,
      hCenter: 242.4973907470703,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 294,
      top: 225,
      height: 32,
      width: 60,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      qrCodeLevel: 0,
      right: 353.9974136352539,
      bottom: 257,
      vCenter: 323.9974136352539,
      hCenter: 241,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 474,
      top: 225,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      qrCodeLevel: 0,
      right: 510.23961639404297,
      bottom: 194.99478912353516,
      vCenter: 495.23961639404297,
      hCenter: 178.99478912353516,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 162,
      top: 225,
      height: 32,
      width: 132,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      qrCodeLevel: 0,
      right: 294.74219512939453,
      bottom: 257.7473907470703,
      vCenter: 228.74219512939453,
      hCenter: 241.7473907470703,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 504,
      top: 225,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      qrCodeLevel: 0,
      right: 533.489616394043,
      bottom: 259.4895935058594,
      vCenter: 518.489616394043,
      hCenter: 243.48959350585938,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 93,
      top: 225,
      height: 32,
      width: 69,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      qrCodeLevel: 0,
      right: 191.9974136352539,
      bottom: 197.73958587646484,
      vCenter: 142.4974136352539,
      hCenter: 181.73958587646484,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 534,
      top: 225,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderRight: 'solid',
      qrCodeLevel: 0,
      right: 563.489616394043,
      bottom: 260.2395935058594,
      vCenter: 548.489616394043,
      hCenter: 244.23959350585938,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 225,
      height: 32,
      width: 64.5,
      title: ' 1',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      qrCodeLevel: 0,
      right: 94.5,
      bottom: 257,
      vCenter: 62.25,
      hCenter: 241,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 384,
      top: 256.5,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 413.9974136352539,
      bottom: 289.9948043823242,
      vCenter: 398.9974136352539,
      hCenter: 273.9948043823242,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 414,
      top: 256.5,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 443.48961639404297,
      bottom: 289.50000762939453,
      vCenter: 428.48961639404297,
      hCenter: 273.50000762939453,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 354,
      top: 256.5,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 385.4948043823242,
      bottom: 289.9948043823242,
      vCenter: 370.4948043823242,
      hCenter: 273.9948043823242,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 444,
      top: 256.5,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 473.48961639404297,
      bottom: 289.50000762939453,
      vCenter: 458.48961639404297,
      hCenter: 273.50000762939453,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 294,
      top: 256.5,
      height: 32,
      width: 60,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 353.48961639404297,
      bottom: 289.50000762939453,
      vCenter: 323.48961639404297,
      hCenter: 273.50000762939453,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 474,
      top: 256.5,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 503.48961639404297,
      bottom: 289.50000762939453,
      vCenter: 488.48961639404297,
      hCenter: 273.50000762939453,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 162,
      top: 256.5,
      height: 32,
      width: 132,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 292.4974136352539,
      bottom: 385.25,
      vCenter: 226.4974136352539,
      hCenter: 369.25,
      fontSize: 11.25,
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 504,
      top: 256.5,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 533.9974136352539,
      bottom: 290.4896011352539,
      vCenter: 518.9974136352539,
      hCenter: 274.4896011352539,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 93,
      top: 256.5,
      height: 32,
      width: 69,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 164.24219512939453,
      bottom: 287.75,
      vCenter: 129.74219512939453,
      hCenter: 271.75,
      fontSize: 11.25,
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 534,
      top: 256.5,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 563.489616394043,
      bottom: 260.2395935058594,
      vCenter: 548.489616394043,
      hCenter: 244.23959350585938,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 256.5,
      height: 32,
      width: 64.5,
      title: ' 2',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 94.5,
      bottom: 288.494140625,
      vCenter: 62.25,
      hCenter: 272.494140625,
      fontSize: 11.25,
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 384,
      top: 288,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 413.9974136352539,
      bottom: 289.9948043823242,
      vCenter: 398.9974136352539,
      hCenter: 273.9948043823242,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 414,
      top: 288,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 443.48961639404297,
      bottom: 289.50000762939453,
      vCenter: 428.48961639404297,
      hCenter: 273.50000762939453,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 354,
      top: 288,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 385.74739837646484,
      bottom: 350.99742126464844,
      vCenter: 370.74739837646484,
      hCenter: 334.99742126464844,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 444,
      top: 288,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 473.48961639404297,
      bottom: 289.50000762939453,
      vCenter: 458.48961639404297,
      hCenter: 273.50000762939453,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 294,
      top: 288,
      height: 32,
      width: 60,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 355.4974136352539,
      bottom: 322.2422180175781,
      vCenter: 325.4974136352539,
      hCenter: 306.2422180175781,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 474,
      top: 288,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 503.48961639404297,
      bottom: 289.50000762939453,
      vCenter: 488.48961639404297,
      hCenter: 273.50000762939453,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 162,
      top: 288,
      height: 32,
      width: 132,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 295.49219512939453,
      bottom: 290.7448043823242,
      vCenter: 229.49219512939453,
      hCenter: 274.7448043823242,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 504,
      top: 288,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 533.489616394043,
      bottom: 289.50000762939453,
      vCenter: 518.489616394043,
      hCenter: 273.50000762939453,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 93,
      top: 288,
      height: 32,
      width: 69,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 162.74219512939453,
      bottom: 321.4973907470703,
      vCenter: 128.24219512939453,
      hCenter: 305.4973907470703,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 534,
      top: 288,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 565.4974136352539,
      bottom: 317.7447814941406,
      vCenter: 550.4974136352539,
      hCenter: 301.7447814941406,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 288,
      height: 32,
      width: 64.5,
      title: ' 3',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 94.5,
      bottom: 319.98828125,
      vCenter: 62.25,
      hCenter: 303.98828125,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 384,
      top: 319.5,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 413.9974136352539,
      bottom: 289.9948043823242,
      vCenter: 398.9974136352539,
      hCenter: 273.9948043823242,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 414,
      top: 319.5,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 443.50000762939453,
      bottom: 350.7422180175781,
      vCenter: 428.50000762939453,
      hCenter: 334.7422180175781,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 354,
      top: 319.5,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 385.4948043823242,
      bottom: 289.9948043823242,
      vCenter: 370.4948043823242,
      hCenter: 273.9948043823242,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 444,
      top: 319.5,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 473.48961639404297,
      bottom: 289.50000762939453,
      vCenter: 458.48961639404297,
      hCenter: 273.50000762939453,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 294,
      top: 319.5,
      height: 32,
      width: 60,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 353.48961639404297,
      bottom: 289.50000762939453,
      vCenter: 323.48961639404297,
      hCenter: 273.50000762939453,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 474,
      top: 319.5,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 503.9974136352539,
      bottom: 353.24481201171875,
      vCenter: 488.9974136352539,
      hCenter: 337.24481201171875,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 162,
      top: 319.5,
      height: 32,
      width: 132,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 295.49219512939453,
      bottom: 290.7448043823242,
      vCenter: 229.49219512939453,
      hCenter: 274.7448043823242,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 504,
      top: 319.5,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 533.2500228881836,
      bottom: 350.7422180175781,
      vCenter: 518.2500228881836,
      hCenter: 334.7422180175781,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 93,
      top: 319.5,
      height: 32,
      width: 69,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 160.4974136352539,
      bottom: 355.98960876464844,
      vCenter: 125.9974136352539,
      hCenter: 339.98960876464844,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 534,
      top: 319.5,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 565.5000228881836,
      bottom: 351.4922180175781,
      vCenter: 550.5000228881836,
      hCenter: 335.4922180175781,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 319.5,
      height: 32,
      width: 64.5,
      title: ' 4',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 94.5,
      bottom: 351.4970703125,
      vCenter: 62.25,
      hCenter: 335.4970703125,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 384,
      top: 351,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      qrCodeLevel: 0,
      right: 413.50000762939453,
      bottom: 381.74481201171875,
      vCenter: 398.50000762939453,
      hCenter: 365.74481201171875,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 414,
      top: 351,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      qrCodeLevel: 0,
      right: 443.48961639404297,
      bottom: 289.50000762939453,
      vCenter: 428.48961639404297,
      hCenter: 273.50000762939453,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 354,
      top: 351,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      qrCodeLevel: 0,
      right: 382.75000762939453,
      bottom: 381.74481201171875,
      vCenter: 367.75000762939453,
      hCenter: 365.74481201171875,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 444,
      top: 351,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      qrCodeLevel: 0,
      right: 473.48961639404297,
      bottom: 289.50000762939453,
      vCenter: 458.48961639404297,
      hCenter: 273.50000762939453,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 294,
      top: 351,
      height: 32,
      width: 60,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      qrCodeLevel: 0,
      right: 353.48961639404297,
      bottom: 384.74220275878906,
      vCenter: 323.48961639404297,
      hCenter: 368.74220275878906,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 474,
      top: 351,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      qrCodeLevel: 0,
      right: 504.73963165283203,
      bottom: 382.98960876464844,
      vCenter: 489.73963165283203,
      hCenter: 366.98960876464844,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 162,
      top: 351,
      height: 32,
      width: 132,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      qrCodeLevel: 0,
      right: 293.98960876464844,
      bottom: 382.98960876464844,
      vCenter: 227.98960876464844,
      hCenter: 366.98960876464844,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 504,
      top: 351,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      qrCodeLevel: 0,
      right: 531.7500228881836,
      bottom: 357.4947814941406,
      vCenter: 516.7500228881836,
      hCenter: 341.4947814941406,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 93,
      top: 351,
      height: 32,
      width: 69,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      qrCodeLevel: 0,
      right: 161.24219512939453,
      bottom: 382.98960876464844,
      vCenter: 126.74219512939453,
      hCenter: 366.98960876464844,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 534,
      top: 351,
      height: 32,
      width: 30,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderRight: 'solid',
      qrCodeLevel: 0,
      right: 561.7500228881836,
      bottom: 379.98960876464844,
      vCenter: 546.7500228881836,
      hCenter: 363.98960876464844,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 351,
      height: 32,
      width: 64.5,
      title: ' 5',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      qrCodeLevel: 0,
      right: 94.5,
      bottom: 382.9912109375,
      vCenter: 62.25,
      hCenter: 366.9912109375,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 354,
      top: 382.5,
      height: 32,
      width: 210,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      qrCodeLevel: 0,
      right: 496.4974136352539,
      bottom: 416.75,
      vCenter: 425.2474136352539,
      hCenter: 400.75,
      fontSize: 11.25,
      borderRight: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 162,
      top: 382.5,
      height: 32,
      width: 132,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      qrCodeLevel: 0,
      right: 293.99219512939453,
      bottom: 414.5,
      vCenter: 227.99219512939453,
      hCenter: 398.5,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 294,
      top: 382.5,
      height: 32,
      width: 60,
      title: '小写',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      qrCodeLevel: 0,
      right: 425.23958587646484,
      bottom: 413.75,
      vCenter: 359.23958587646484,
      hCenter: 397.75,
      fontSize: 11.25,
      backgroundColor: '#dbdbdb',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 382.5,
      height: 32,
      width: 132,
      title: '合计（大写）',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      qrCodeLevel: 0,
      right: 162,
      bottom: 414.5,
      vCenter: 96,
      hCenter: 398.5,
      fontSize: 11.25,
      backgroundColor: '#dbdbdb',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 162,
      top: 414,
      height: 32,
      width: 133.5,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      qrCodeLevel: 0,
      right: 297.7448043823242,
      bottom: 445.9973907470703,
      vCenter: 230.99480438232422,
      hCenter: 429.9973907470703,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 294,
      top: 414,
      height: 32,
      width: 132,
      title: '财务主管意见',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      qrCodeLevel: 0,
      right: 425.9974136352539,
      bottom: 445.9973907470703,
      vCenter: 359.9974136352539,
      hCenter: 429.9973907470703,
      fontSize: 11.25,
      backgroundColor: '#dbdbdb',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 426,
      top: 414,
      height: 32,
      width: 138,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      qrCodeLevel: 0,
      right: 563.989631652832,
      bottom: 444.4973907470703,
      vCenter: 494.98963165283203,
      hCenter: 428.4973907470703,
      fontSize: 11.25,
      borderRight: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 414,
      height: 32,
      width: 132,
      title: '主管意见',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      qrCodeLevel: 0,
      right: 164.25,
      bottom: 448.2473907470703,
      vCenter: 98.25,
      hCenter: 432.2473907470703,
      fontSize: 11.25,
      backgroundColor: '#dbdbdb',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 162,
      top: 445.5,
      height: 32,
      width: 133.5,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 293.9970703125,
      bottom: 478.994140625,
      vCenter: 227.2470703125,
      hCenter: 462.994140625,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 294,
      top: 445.5,
      height: 32,
      width: 132,
      title: '出纳',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 425.994140625,
      bottom: 475.994140625,
      vCenter: 359.994140625,
      hCenter: 459.994140625,
      fontSize: 11.25,
      backgroundColor: '#dbdbdb',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 426,
      top: 445.5,
      height: 32,
      width: 138,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 565.4912109375,
      bottom: 475.994140625,
      vCenter: 496.4912109375,
      hCenter: 459.994140625,
      fontSize: 11.25,
      borderRight: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 445.5,
      height: 32,
      width: 132,
      title: '财务复核',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 162,
      bottom: 475.994140625,
      vCenter: 96,
      hCenter: 459.994140625,
      fontSize: 11.25,
      backgroundColor: '#dbdbdb',
    },
    printElementType: { title: '文本', type: 'text' },
  },
];
