<template>
  <a-table :dataSource="questions" :columns="columns" rowKey="id" size="small" bordered>
    <template #bodyCell="{ column, record, index }">
      <template v-if="column.key === 'score'">
        <a-input-number v-model:value="record.score" @change="emitEdit(record, index)" :min="0" style="width: 80px" />
      </template>
      <template v-else-if="column.key === 'sort'">
        <span>{{ record.sort }}</span>
      </template>
      <template v-else-if="column.key === 'action'">
        <a @click="emitEdit(record, index)">编辑</a>
        <a-divider type="vertical" />
        <a @click="emitDelete(record, index)">删除</a>
      </template>
    </template>
  </a-table>
</template>
<script setup lang="ts">
const props = defineProps<{ questions: any[] }>();
const emit = defineEmits(['edit', 'delete']);
function emitEdit(record, index) { emit('edit', record, index); }
function emitDelete(record, index) { emit('delete', record, index); }
const columns = [
  { title: '题型', dataIndex: 'questionType', key: 'questionType' },
  { title: '题干', dataIndex: 'content', key: 'content' },
  { title: '分值', dataIndex: 'score', key: 'score' },
  { title: '排序', dataIndex: 'sort', key: 'sort' },
  { title: '操作', key: 'action' }
];
</script> 