import { Injector, Univer, IUniverInstanceService, ICommandService, FUniver, LifecycleService } from '@univerjs/core';
import '@univerjs/sheets/facade';
import '@univerjs/ui/facade';
import '@univerjs/docs-ui/facade';
import '@univerjs/sheets-ui/facade';

import '@univerjs/sheets-filter/facade';
import '@univerjs/sheets-data-validation/facade';
import '@univerjs/sheets-hyper-link/facade';
import '@univerjs/sheets-hyper-link-ui/facade';
import '@univerjs/watermark/facade';
import '@univerjs/sheets-thread-comment/facade';
import '@univerjs/sheets-crosshair-highlight/facade';

import { XundaFacadeSheetsCell } from '../facade/f-sheet-cell';
import { XundaSheetsCellService } from '../services/sheet-cell.service';

import { XundaFacadeSheetsRange } from '../facade/f-sheet-range';
import { XundaSheetsRangeService } from '../services/sheet-range.service';

import { XundaFacadeSheetsFloatDom } from '../facade/f-sheet-float-dom';
import { XundaSheetsFloatDomService } from '../services/sheet-float-dom.service';

import { XundaFacadeSheetsFloatEchart } from './f-sheet-float-echart';
import { XundaSheetsFloatEchartService } from '../services/sheet-float-echart.service';

import { XundaFacadeSheetsCellEchart } from '../facade/f-sheet-cell-echart';
import { XundaSheetsCellEchartService } from '../services/sheet-cell-echart.service';

import { XundaFacadeSheetsFloatImage } from './f-sheet-float-image';
import { XundaSheetsFloatImageService } from '../services/sheet-float-image.service';

import { XundaFacadeSheetsPrint } from '../facade/f-sheet-print';
import { XundaSheetsPrintService } from '../services/sheet-print.service';

export class XundaFUniver extends FUniver {
  static newAPI(wrapped: Univer | Injector): XundaFUniver {
    const injector = wrapped instanceof Univer ? wrapped.__getInjector() : wrapped;
    const commandService = injector.get<ICommandService>(ICommandService);
    const instanceService = injector.get<IUniverInstanceService>(IUniverInstanceService);

    if (!commandService || !instanceService) {
      throw new Error('注入ICommandService或IUniverInstanceService发生错误');
    }

    return new XundaFUniver(injector, commandService, instanceService);
  }

  constructor(injector: Injector, commandService: ICommandService, instanceService: IUniverInstanceService) {
    const lifecycleService = injector.get<LifecycleService>(LifecycleService);
    if (!lifecycleService) {
      throw new Error('LifecycleService 未正确注入');
    }

    super(injector, commandService, instanceService, lifecycleService);
  }

  getInjector() {
    return this._injector;
  }

  getSheetsCell(): XundaFacadeSheetsCell | null {
    const xundaSheetsCellService = this._injector.get(XundaSheetsCellService);

    if (!xundaSheetsCellService) {
      return null;
    }

    return this._injector.createInstance(XundaFacadeSheetsCell, xundaSheetsCellService);
  }

  getSheetsRange(): XundaFacadeSheetsRange | null {
    const xundaSheetsRangeService = this._injector.get(XundaSheetsRangeService);

    if (!xundaSheetsRangeService) {
      return null;
    }

    return this._injector.createInstance(XundaFacadeSheetsRange, xundaSheetsRangeService);
  }

  getSheetsFloatDom(): XundaFacadeSheetsFloatDom | null {
    const xundaSheetsFloatDomService = this._injector.get(XundaSheetsFloatDomService);

    if (!xundaSheetsFloatDomService) {
      return null;
    }

    return this._injector.createInstance(XundaFacadeSheetsFloatDom, xundaSheetsFloatDomService);
  }

  getSheetsFloatEchart(): XundaFacadeSheetsFloatEchart | null {
    const xundaSheetsFloatEchartService = this._injector.get(XundaSheetsFloatEchartService);

    if (!xundaSheetsFloatEchartService) {
      return null;
    }

    return this._injector.createInstance(XundaFacadeSheetsFloatEchart, xundaSheetsFloatEchartService);
  }

  getSheetsCellEchart(): XundaFacadeSheetsCellEchart | null {
    const xundaSheetsCellEchartService = this._injector.get(XundaSheetsCellEchartService);

    if (!xundaSheetsCellEchartService) {
      return null;
    }

    return this._injector.createInstance(XundaFacadeSheetsCellEchart, xundaSheetsCellEchartService);
  }

  getSheetsFloatImage(): XundaFacadeSheetsFloatImage | null {
    const xundaSheetsFloatImageService = this._injector.get(XundaSheetsFloatImageService);

    if (!xundaSheetsFloatImageService) {
      return null;
    }

    return this._injector.createInstance(XundaFacadeSheetsFloatImage, xundaSheetsFloatImageService);
  }

  getSheetsPrint(): XundaFacadeSheetsPrint | null {
    const xundaSheetsPrintService = this._injector.get(XundaSheetsPrintService);

    if (!xundaSheetsPrintService) {
      return null;
    }

    return this._injector.createInstance(XundaFacadeSheetsPrint, xundaSheetsPrintService);
  }
}
