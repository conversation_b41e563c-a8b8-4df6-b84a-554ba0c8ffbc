import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';

// 基础Api
export const paymentRecordApi = '/api/study/paymentRecord';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: paymentRecordApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: paymentRecordApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: paymentRecordApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: paymentRecordApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: paymentRecordApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: paymentRecordApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: paymentRecordApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: paymentRecordApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: paymentRecordApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: paymentRecordApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: paymentRecordApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: paymentRecordApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: paymentRecordApi + `/getForSelect`, data });
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'name',
    label: t('费用名'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  // {
  //   title: t('优惠'),
  //   dataIndex: 'id',
  //   width: 120,
  // },
  {
    title: t('学员'),
    dataIndex: 'registrationName',
    width: 120,
  },
  {
    title: t('费用名'),
    dataIndex: 'name',
    width: 120,
  },
  {
    title: t('实收币种'),
    dataIndex: 'currency',
    width: 120,
  },
  {
    title: t('原价'),
    dataIndex: 'originalPrice',
    width: 120,
  },
  {
    title: t('实价'),
    dataIndex: 'realPrice',
    width: 120,
  },
  {
    title: t('本币金额'),
    dataIndex: 'homeCurrencyPrice',
    width: 120,
  },
  {
    title: t('本币'),
    dataIndex: 'homeCurrency',
    width: 120,
  },
  {
    title: t('汇率'),
    dataIndex: 'rate',
    width: 120,
  },
  {
    title: t('支付状态'),
    dataIndex: 'payState',
    width: 120,
  },
  {
    title: t('支付时间'),
    dataIndex: 'payTime',
    width: 120,
    customRender: ({ record }) => {
      return xundaUtils.toDateString(record.payTime);
    },
  },
  {
    title: t('支付类型'),
    dataIndex: 'payType',
    width: 120,
  },
  {
    title: t('交易记录'),
    dataIndex: 'transcationId',
    width: 120,
  },
  {
    title: t('说明'),
    dataIndex: 'description',
    width: 120,
  },
  {
    title: t('备注'),
    dataIndex: 'note',
    width: 120,
  },
  {
    title: t('附件'),
    dataIndex: 'attachment',
    width: 120,
  },
  {
    title: t('优惠码币种'),
    dataIndex: 'promotionCodeCurrency',
    width: 120,
  },
  {
    title: t('优惠码'),
    dataIndex: 'promotionCode',
    width: 120,
  },
  {
    title: t('优惠金额'),
    dataIndex: 'promotionMoney',
    width: 120,
  },
];
