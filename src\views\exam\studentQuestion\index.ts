import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';
import { stripHtml, parseOptions, parseFillBlankAnswer } from '../utils';

// 基础Api
export const studentQuestionApi = '/api/exam/studentQuestion';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: studentQuestionApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: studentQuestionApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: studentQuestionApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: studentQuestionApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: studentQuestionApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: studentQuestionApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: studentQuestionApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: studentQuestionApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: studentQuestionApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: studentQuestionApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: studentQuestionApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: studentQuestionApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: studentQuestionApi + `/getForSelect`, data });
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'no',
    label: t('序号'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('题序'),
    dataIndex: 'displayOrder',
    width: 60,
  },
  {
    title: t('试卷'),
    dataIndex: 'studetnExamId',
    width: 120,
  },
  {
    title: t('题型'),
    dataIndex: 'questionTypeId',
    width: 80,
  },
  {
    title: t('题干'),
    dataIndex: 'content',
    width: 200,
    customRender: ({ record }) => {
      const content = stripHtml(record.content || '');
      return content;
    },
  },
  {
    title: t('选项'),
    dataIndex: 'option',
    width: 200,
    customRender: ({ record }) => {
      const options = parseOptions(record.option || '');
      return options;
    },
  },
  {
    title: t('参考答案'),
    dataIndex: 'answer',
    width: 120,
    customRender: ({ text, record }) => {
      if (record.questionTypeId === 'fillblank') {
        return parseFillBlankAnswer(text || '');
      }
      return text;
    },
  },
  {
    title: t('学生答案'),
    dataIndex: 'studentAnswer',
    width: 120,
    customRender: ({ text, record }) => {
      if (record.questionTypeId === 'fillblank') {
        return parseFillBlankAnswer(text || '');
      }
      return text;
    },
  },
  {
    title: t('分值'),
    dataIndex: 'score',
    width: 80,
  },
  {
    title: t('是否正确'),
    dataIndex: 'rightFlag',
    width: 80,
    customRender: xundaUtils.customRenderBoolean,
  },
  {
    title: t('实际得分'),
    dataIndex: 'realScore',
    width: 80,
  },
];
