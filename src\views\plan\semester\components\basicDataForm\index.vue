<template>
  <a-row class="mt-20px h-full" :class="'overflow-auto'">
    <a-col :span="14" :offset="5">
      <a-row class="dynamic-form">
        <a-form
          :colon="false"
          size="middle"
          layout="horizontal"
          labelAlign="left"
          :labelCol="{ style: { width: '100px' } }"
          :model="dataForm"
          :rules="dataRule"
          :disabled="disabled"
          ref="formRef">
          <a-row :gutter="15">
            <!-- 具体表单 --><a-col :span="12" class="ant-col-item" :hidden="false">
              <a-form-item name="category">
                <template #label>计划类型 </template>
                <p>{{ xundaUtils.optionText(dataForm.category, optionsObj.categoryOptions, optionsObj.defaultProps) }}</p>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item" :hidden="false">
              <a-form-item name="name">
                <template #label>计划名称 </template>
                <XundaInput
                  v-model:value="dataForm.name"
                  :disabled="disabled"
                  placeholder="计划名称"
                  :allowClear="true"
                  :style="{ width: '100%' }"
                  v-if="!disabled">
                </XundaInput>
                <p v-else>{{ dataForm.name }}</p>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item" :hidden="false">
              <a-form-item name="year">
                <template #label>学年 </template>
                <a-date-picker
                  v-model:value="dataForm.year"
                  :disabled="disabled"
                  placeholder="请选择学年"
                  :allowClear="true"
                  :style="{ width: '100%' }"
                  picker="year"
                  format="YYYY"
                  value-format="YYYY"
                  v-if="!disabled">
                </a-date-picker>
                <p v-else>{{ dataForm.year }}</p>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item" :hidden="false">
              <a-form-item name="term">
                <template #label>期次 </template>
                <XundaInputNumber
                  v-model:value="dataForm.term"
                  :disabled="disabled"
                  placeholder="期次"
                  :allowClear="true"
                  :style="{ width: '100%' }"
                  :controls="true"
                  v-if="!disabled">
                </XundaInputNumber>
                <p v-else>{{ dataForm.term }}</p>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item" :hidden="false">
              <a-form-item name="planLevel">
                <template #label>级别 </template>
                <XundaSelect
                  v-model:value="dataForm.planLevel"
                  :disabled="disabled"
                  :options="optionsObj.planLevelOptions"
                  :fieldNames="{
                    label: 'fullName',
                    value: 'key',
                  }"
                  placeholder="请选择认定职业"
                  :allowClear="true"
                  :style="{ width: '100%' }"
                  v-if="!disabled">
                </XundaSelect>
                <p v-else>{{ xundaUtils.optionText(dataForm.planLevel, optionsObj.planLevelOptions, { label: 'fullName', value: 'key' }) }}</p>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item" :hidden="false">
              <a-form-item name="weeks">
                <template #label>周数 </template>
                <XundaInputNumber
                  v-model:value="dataForm.weeks"
                  :disabled="disabled"
                  placeholder="周数"
                  :allowClear="true"
                  :style="{ width: '100%' }"
                  :controls="true"
                  v-if="!disabled">
                </XundaInputNumber>
                <p v-else>{{ dataForm.weeks }}</p>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item">
              <a-form-item name="startTime">
                <template #label>开始日期 </template>
                <XundaDatePicker
                  v-model:value="dataForm.startTime"
                  placeholder="开始日期"
                  :allowClear="true"
                  :style="{ width: '100%' }"
                  format="yyyy-MM-dd"
                  v-if="!disabled">
                </XundaDatePicker>
                <p v-else>{{ xundaUtils.toDateString(dataForm.startTime) }}</p>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item">
              <a-form-item name="endTime">
                <template #label>结束日期 </template>
                <XundaDatePicker
                  v-model:value="dataForm.endTime"
                  placeholder="结束日期"
                  :allowClear="true"
                  :style="{ width: '100%' }"
                  format="yyyy-MM-dd"
                  v-if="!disabled">
                </XundaDatePicker>
                <p v-else>{{ xundaUtils.toDateString(dataForm.endTime) }}</p>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item" :hidden="false">
              <a-form-item name="minNumber">
                <template #label>最小开班人数 </template>
                <XundaInputNumber
                  v-model:value="dataForm.minNumber"
                  :disabled="disabled"
                  placeholder="最小开班人数"
                  :allowClear="true"
                  :style="{ width: '100%' }"
                  :controls="true"
                  v-if="!disabled">
                </XundaInputNumber>
                <p v-else>{{ dataForm.minNumber ?? 0 }}人</p>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item" :hidden="false">
              <a-form-item name="maxNumber">
                <template #label>最大班级人数 </template>
                <XundaInputNumber
                  v-model:value="dataForm.maxNumber"
                  :disabled="disabled"
                  placeholder="最大班级人数"
                  :allowClear="true"
                  :style="{ width: '100%' }"
                  :controls="true"
                  v-if="!disabled">
                </XundaInputNumber>
                <p v-else>{{ dataForm.maxNumber ?? 0 }}人</p>
              </a-form-item>
            </a-col>

            <a-col :span="24" class="ant-col-item">
              <a-form-item name="description">
                <template #label>说明 </template>
                <XundaTextarea
                  v-model:value="dataForm.description"
                  placeholder="说明"
                  :allowClear="true"
                  :style="{ width: '100%' }"
                  :autoSize="{ minRows: 4, maxRows: 4 }"
                  :showCount="true"
                  v-if="!disabled">
                </XundaTextarea>
                <p v-else style="white-space: pre-wrap">{{ dataForm.description }}</p>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item" :hidden="true">
              <a-form-item name="examFlag">
                <template #label>需要考试 </template>
                <XundaSwitch v-model:value="dataForm.examFlag" :disabled="disabled"> </XundaSwitch>
              </a-form-item>
            </a-col>
            <a-col :span="24" class="ant-col-item">
              <a-form-item name="photo">
                <template #label>列表图片 </template>
                <XundaUploadImgSingle
                  v-model:value="dataForm.photo"
                  :fileSize="1"
                  sizeUnit="MB"
                  :limit="1"
                  pathType="selfPath"
                  :sortRule="[3]"
                  timeFormat="YYYY"
                  :disabled="disabled"
                  :detailed="disabled"
                  folder="plan/semester"
                  :tipText="!disabled ? '请上传图片，仅限一张图片' : '暂未上传图片'">
                </XundaUploadImgSingle>
                <div class="image-description" style="margin-top: 8px; color: #666; font-size: 13px" v-if="!disabled"
                  >图片建议尺寸: 320 x 240px，支持jpg、png格式，大小不超过1MB</div
                >
              </a-form-item>
            </a-col>
            <a-col :span="24" class="ant-col-item">
              <a-form-item name="photo2">
                <template #label>轮播图片 </template>
                <XundaUploadImgSingle
                  v-model:value="dataForm.photo2"
                  :fileSize="1"
                  sizeUnit="MB"
                  :limit="1"
                  pathType="selfPath"
                  :sortRule="[3]"
                  timeFormat="YYYY"
                  :disabled="disabled"
                  :detailed="disabled"
                  folder="plan/semester"
                  :tipText="!disabled ? '请上传图片，仅限一张图片' : '暂未上传图片'">
                  <template #tipText v-if="!disabled"> 请上传图片，仅限一张图片 </template>
                </XundaUploadImgSingle>
                <div class="image-description" style="margin-top: 8px; color: #666; font-size: 13px" v-if="!disabled"
                  >图片建议尺寸: 1440 x 300px，支持jpg、png格式，大小不超过1MB</div
                >
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-row>
    </a-col>
  </a-row>
</template>

<script setup lang="ts">
  import { ref, reactive, toRefs, unref, nextTick, watch } from 'vue';
  import { FormActionType } from '@/components/Form';
  import { create, getInfo, update } from './index';
  import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useMessage } from '@/hooks/web/useMessage';
  import { xundaUtils } from '@/utils/xunda';
  import { XundaText, XundaTextarea, XundaInputNumber, XundaUploadImgSingle } from '@/components/Xunda';
  // import { error } from '../../../../../utils/log'; // 未使用的导入

  const { t } = useI18n();

  interface State {
    dataForm: Record<string, any>;
    tableRows: Record<string, any>;
    dataRule: Record<string, any[]>;
    isEdit: boolean;
    optionsObj: {
      defaultProps: { label: string; value: string };
      categoryOptions?: any[];
      planLevelOptions?: any[];
    };
  }
  const { createMessage, createErrorModal } = useMessage();

  // 定义组件的 emits
  const emit = defineEmits(['changeLoading', 'getBasicData', 'handleStep', 'update:semesterId', 'update:isCopy']);
  const changeLoading = (loading: boolean) => {
    emit('changeLoading', loading);
  };
  const props = defineProps({
    semesterId: {
      type: String,
      required: true,
    },
    disabled: { type: Boolean, default: false },
    isCopy: { type: Boolean, default: false },
  });
  const state = reactive<State>({
    dataForm: {},
    tableRows: {},
    dataRule: {
      name: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '不能为空'),
          trigger: 'blur',
        },
      ],
      year: [
        {
          required: true,
          message: t('sys.validate.textRequiredSuffix', '不能为空'),
          trigger: ['blur', 'change'],
        },
      ],
      term: [
        {
          required: true,
          message: t('sys.validate.textRequiredSuffix', '不能为空'),
          trigger: ['blur', 'change'],
        },
      ],
      planLevel: [
        {
          required: true,
          message: t('sys.validate.textRequiredSuffix', '不能为空'),
          trigger: ['blur', 'change'],
        },
      ],
      weeks: [
        {
          required: true,
          message: t('sys.validate.textRequiredSuffix', '不能为空'),
          trigger: ['blur', 'change'],
        },
      ],
      startTime: [
        {
          required: true,
          message: t('sys.validate.textRequiredSuffix', '不能为空'),
          trigger: ['change'],
        },
        {
          validator: (rule, value) => {
            if (value && state.dataForm.endTime && value > state.dataForm.endTime) {
              return Promise.reject('开始日期不能大于结束日期');
            }
            return Promise.resolve();
          },
          trigger: ['change'],
        },
      ],
      endTime: [
        {
          required: true,
          message: t('sys.validate.textRequiredSuffix', '不能为空'),
          trigger: ['change'],
        },
        {
          validator: (rule, value) => {
            if (value && state.dataForm.startTime && value < state.dataForm.startTime) {
              return Promise.reject('结束日期不能小于开始日期');
            }
            return Promise.resolve();
          },
          trigger: ['change'],
        },
      ],
      minNumber: [
        {
          required: true,
          message: t('sys.validate.textRequiredSuffix', '不能为空'),
          trigger: ['blur', 'change'],
        },
        {
          validator: (rule, value) => {
            if (value && state.dataForm.maxNumber && value > state.dataForm.maxNumber) {
              return Promise.reject('最小开班人数不能大于最大班级人数');
            }
            return Promise.resolve();
          },
          trigger: ['blur', 'change'],
        },
      ],
      maxNumber: [
        {
          required: true,
          message: t('sys.validate.textRequiredSuffix', '不能为空'),
          trigger: ['blur', 'change'],
        },
        {
          validator: (rule, value) => {
            if (value && state.dataForm.minNumber && value < state.dataForm.minNumber) {
              return Promise.reject('最大班级人数不能小于最小开班人数');
            }
            return Promise.resolve();
          },
          trigger: ['blur', 'change'],
        },
      ],
    },
    optionsObj: {
      //选项配置
      defaultProps: { label: 'fullName', value: 'enCode' }, // 默认下拉选择键值
      planLevelOptions: [
        { key: '1', fullName: '1级' },
        { key: '2', fullName: '2级' },
        { key: '3', fullName: '3级' },
        { key: '4', fullName: '4级' },
        { key: '5', fullName: '5级' },
      ],
    },
    isEdit: false,
  });

  const { dataRule, dataForm, optionsObj } = toRefs(state);
  const formRef = ref<FormActionType>();
  function getFormRef() {
    const form = unref(formRef);
    if (!form) throw new Error('form is null!');
    return form;
  }

  // 监听开始时间和周次变化，自动计算结束时间
  watch([() => state.dataForm.startTime, () => state.dataForm.weeks], ([startTime, weeks]) => {
    if (startTime && weeks) {
      // 确保两个值都存在才进行计算
      const startDate = new Date(startTime);
      // 计算结束日期：开始日期 + (周数 * 7天) - 1天
      // 减1是因为开始日期已经算一天了
      const endDate = new Date(startDate.getTime());
      endDate.setDate(startDate.getDate() + weeks * 7 - 1);
      state.dataForm.endTime = endDate;
    }
  });

  // 初始化表单数据
  function init(data: Record<string, any>) {
    getFormRef().resetFields();
    getPlanType();
    if (data.id) {
      initData(data.id);
    } else {
      // 设置默认值
      state.dataForm.category = data.category;
      state.dataForm.year = new Date().getFullYear().toString();
      state.dataForm.startTime = new Date();
    }
  }
  function getPlanType() {
    getDictionaryDataSelector('planType').then(res => {
      const options = res.data.list as any[];
      state.optionsObj.categoryOptions = options;
    });
  }

  // 获取基础数据
  function initData(id: string) {
    changeLoading(true);
    getInfo(id)
      .then(res => {
        state.dataForm = res.data || {};
        if (state.dataForm.photo) {
          state.dataForm.photo = JSON.parse(state.dataForm.photo);
        }
        if (state.dataForm.photo2) {
          state.dataForm.photo2 = JSON.parse(state.dataForm.photo2);
        }
        getFormRef().setFieldsValue(state.dataForm);
        changeLoading(false);
      })
      .catch(() => {
        changeLoading(false);
      });
  }
  function setFormData(data: Record<string, any>) {
    getPlanType();
    nextTick(() => {
      getFormRef().resetFields();
      changeLoading(false);
      state.dataForm = data;
      if (state.dataForm.photo) {
        state.dataForm.photo = JSON.parse(state.dataForm.photo);
      }
      if (state.dataForm.photo2) {
        state.dataForm.photo2 = JSON.parse(state.dataForm.photo2);
      }
    });
  }

  function getDataForm() {
    const dataForm = { ...state.dataForm, ...getFormRef().getFieldsValue() };
    dataForm.photo = JSON.stringify(dataForm.photo);
    dataForm.photo2 = JSON.stringify(dataForm.photo2);
    return dataForm;
  }

  async function handleSubmit(type: string) {
    try {
      const values = await getFormRef().validate();
      if (!values) return;
      const dataForm = getDataForm();
      const formMethod = dataForm.id ? update : create;
      if (props.isCopy) {
        dataForm.publishFlag = false;
        dataForm.copyFlag = true;
        dataForm.id = null;
      } else {
        dataForm.copyFlag = false;
        dataForm.copyId = null;
      }
      changeLoading(true);
      formMethod(dataForm)
        .then(res => {
          if (res.code === 200) {
            createMessage.success('基本信息配置成功');
            changeLoading(false);
            if (!dataForm.id) {
              emit('update:semesterId', res.data);
            }
            emit('update:isCopy', false);
            emit('handleStep', type);
          }
        })
        .catch(error => {
          changeLoading(false);
          createErrorModal({
            iconType: 'warning',
            title: '温馨提示',
            content: error.message,
            onOk: () => {},
          });
        });
    } catch (_) {
      console.log('error:', _);
    }
  }

  // 暴露表单方法给父组件
  defineExpose({
    init,
    setFormData,
    handleSubmit,
  });
</script>
