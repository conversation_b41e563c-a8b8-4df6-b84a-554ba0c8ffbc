<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    width="1000px"
    :minHeight="100"
    :cancelText="t('common.cancelText', '取消')"
    :okText="t('common.okText', '确定')"
    @ok="handleSubmit"
    :closeFunc="onClose">
    <template #title>
      <a-space :size="10">
        <div class="text-16px font-medium">{{ title }}</div>
        <a-space-compact size="small" block v-if="dataForm.id">
          <a-tooltip :title="t('common.prevRecord')">
            <a-button size="small" :disabled="getPrevDisabled" @click="handlePrev">
              <i class="icon-ym icon-ym-caret-left text-10px"></i>
            </a-button>
          </a-tooltip>
          <a-tooltip :title="t('common.nextRecord')">
            <a-button size="small" :disabled="getNextDisabled" @click="handleNext">
              <i class="icon-ym icon-ym-caret-right text-10px"></i>
            </a-button>
          </a-tooltip>
        </a-space-compact>
      </a-space>
    </template>
    <template #insertFooter>
      <div class="loat-left mt-5px">
        <XundaCheckboxSingle v-model:value="submitType" :label="continueText" />
      </div>
    </template>
    <a-row class="dynamic-form">
      <a-form
        :colon="false"
        size="middle"
        layout="horizontal"
        labelAlign="left"
        :labelCol="{ style: { width: '100px' } }"
        :model="dataForm"
        :rules="dataRule"
        ref="formRef">
        <a-row :gutter="15">
          <!-- 题型按钮 -->
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-space style="margin-bottom: 16px;">
              <a-button v-for="(item, index) in questionTypes" :key="item.type"
                :type="activeType === item.type ? 'primary' : 'default'" @click="handleTypeChange(item.type)">
                {{ item.label }}
              </a-button>
            </a-space>
          </a-col>
          
          <!-- 基础信息 -->
          <a-col :span="12" class="ant-col-item" :hidden="false">
            <a-form-item name="studetnExamId">
              <template #label>试卷 </template>
              <XundaInput
                v-model:value="dataForm.studetnExamId"
                :disabled="false"
                @change="changeData('studetnExamId', -1)"
                placeholder="请输入试卷"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="12" class="ant-col-item" :hidden="false">
            <a-form-item name="displayOrder">
              <template #label>题序 </template>
              <XundaInput
                v-model:value="dataForm.displayOrder"
                :disabled="false"
                @change="changeData('displayOrder', -1)"
                placeholder="题序"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="12" class="ant-col-item" :hidden="false">
            <a-form-item name="score">
              <template #label>分值 </template>
              <XundaInput
                v-model:value="dataForm.score"
                :disabled="false"
                @change="changeData('score', -1)"
                placeholder="请输入分值"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="12" class="ant-col-item" :hidden="false">
            <a-form-item name="rightFlag">
              <template #label>是否正确 </template>
              <XundaSwitch
                v-model:value="dataForm.rightFlag"
                :disabled="false"
                @change="changeData('rightFlag', -1)"
                placeholder="是否正确"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaSwitch>
            </a-form-item>
          </a-col>

          <!-- 动态题型组件 -->
          <component :is="activeComponent" ref="currentEditor" :formData="state.dataForm" />

          <a-divider />
          
          <!-- 学生答案和实际得分 -->
          <a-col :span="12" class="ant-col-item" :hidden="false">
            <a-form-item name="studentAnswer">
              <template #label>学生答案 </template>
              <XundaInput
                v-model:value="dataForm.studentAnswer"
                :disabled="false"
                @change="changeData('studentAnswer', -1)"
                placeholder="学生答案"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="12" class="ant-col-item" :hidden="false">
            <a-form-item name="realScore">
              <template #label>实际得分 </template>
              <XundaInput
                v-model:value="dataForm.realScore"
                :disabled="false"
                @change="changeData('realScore', -1)"
                placeholder="实际得分"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          
          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { create, update, getInfo } from '@/views/exam/studentQuestion';
  import { reactive, toRefs, nextTick, ref, unref, computed, inject, watch } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useUserStore } from '@/store/modules/user';
  import type { FormInstance } from 'ant-design-vue';
  import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
  // 表单权限
  import { usePermission } from '@/hooks/web/usePermission';

  // 导入题型组件
  import SingleChoiceEditor from '../questionComponent/SingleChoiceEditor.vue'
  import MultipleChoiceEditor from '../questionComponent/MultipleChoiceEditor.vue'
  import TrueFalseEditor from '../questionComponent/TrueFalseEditor.vue'
  import FillBlankEditor from '../questionComponent/FillBlankEditor.vue'
  import ShortAnswerEditor from '../questionComponent/ShortAnswerEditor.vue'

  interface State {
    dataForm: any;
    tableRows: any;
    dataRule: any;
    optionsObj: any;
    childIndex: any;
    isEdit: any;
    interfaceRes: any;
    //可选范围默认值
    ableAll: any;
    //掩码配置
    maskConfig: any;
    //定位属性
    locationScope: any;
    title: string;
    continueText: string;
    allList: any[];
    currIndex: number;
    isContinue: boolean;
    submitType: number;
    showContinueBtn: boolean;
  }

  // 题型配置
  const questionTypes = [
    { type: 'single', label: '单选题', component: SingleChoiceEditor },
    { type: 'multi', label: '多选题', component: MultipleChoiceEditor },
    { type: 'truefalse', label: '判断题', component: TrueFalseEditor },
    { type: 'fillblank', label: '填空', component: FillBlankEditor },
    { type: 'shortanswer', label: '简答', component: ShortAnswerEditor },
  ]

  // 当前激活题型
  const activeType = ref('single')

  // 当前组件引用（ref 方式访问其方法）
  const currentEditor = ref()

  // 动态获取当前组件
  const activeComponent = computed(() => {
    const found = questionTypes.find(q => q.type === activeType.value)
    return found ? found.component : null
  })

  // 监听题型变化，自动设置questionTypeId
  watch(activeType, (newType) => {
    if (newType) {
      state.dataForm.questionTypeId = newType
    }
  })

  const emit = defineEmits(['reload']);
  const getLeftTreeActiveInfo: (() => any) | null = inject('getLeftTreeActiveInfo', null);
  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const [registerModal, { openModal, setModalProps }] = useModal();
  const formRef = ref<FormInstance>();
  const state = reactive<State>({
    dataForm: {
      id: '',
      studetnExamId: '',
      parentId: '',
      questionId: '',
      questionTypeId: '',
      paperQuestionId: '',
      no: '',
      displayOrder: undefined,
      content: '',
      option: '',
      answer: '',
      studentAnswer: '',
      score: undefined,
      rightFlag: undefined,
      realScore: undefined,
    },
    tableRows: {},
    dataRule: {
      studetnExamId: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '试卷不能为空'),
          trigger: 'blur',
        },
      ],
      questionTypeId: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '题型不能为空'),
          trigger: 'blur',
        },
      ],
      content: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '题干不能为空'),
          trigger: 'blur',
        },
      ],
      answer: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '参考答案不能为空'),
          trigger: 'blur',
        },
      ],
    },
    optionsObj: {
      //选项配置
      defaultProps: { label: 'fullName', value: 'enCode' }, // 默认下拉选择键值
    },
    childIndex: -1,
    isEdit: false,
    interfaceRes: {},
    //可选范围默认值
    ableAll: {},

    //掩码配置
    maskConfig: {},
    //定位属性
    locationScope: {
      faddressDetail: [],
    },
    title: '',
    continueText: '',
    allList: [],
    currIndex: 0,
    isContinue: false,
    submitType: 0,
    showContinueBtn: true,
  });
  const { title, continueText, showContinueBtn, dataRule, dataForm, optionsObj, ableAll, maskConfig, submitType } = toRefs(state);

  const getPrevDisabled = computed(() => state.currIndex === 0);
  const getNextDisabled = computed(() => state.currIndex === state.allList.length - 1);
  // 表单权限
  const { hasFormP } = usePermission();

  defineExpose({ init });

  function init(data) {
    state.submitType = 0;
    state.isContinue = false;
    state.title = !data.id ? t('common.add2Text', '新增') : t('common.editText', '编辑');
    state.continueText = !data.id ? t('common.continueAndAddText', '确定并新增') : t('common.continueText', '确定并继续');
    setFormProps({ continueLoading: false });
    state.dataForm.id = data.id;
    openModal();
    state.allList = data.allList;
    state.currIndex = state.allList.length && data.id ? state.allList.findIndex(item => item.id === data.id) : 0;
    getAllSelectOptions();
    nextTick(() => {
      getForm().resetFields();
      setTimeout(initData, 0);
    });
  }
  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      // 设置默认值
      state.dataForm = {
        id: '',
        studetnExamId: '',
        parentId: '',
        questionId: '',
        questionTypeId: 'single',
        paperQuestionId: '',
        no: '',
        displayOrder: undefined,
        content: '',
        option: '',
        answer: '',
        studentAnswer: '',
        score: undefined,
        rightFlag: undefined,
        realScore: undefined,
      };

      if (getLeftTreeActiveInfo) state.dataForm = { ...state.dataForm, ...(getLeftTreeActiveInfo() || {}) };
      state.childIndex = -1;
      changeLoading(false);
    }
  }
  function getForm() {
    const form = unref(formRef);
    if (!form) {
      throw new Error('form is null!');
    }
    return form;
  }
  function getData(id) {
    getInfo(id).then(res => {
      const data = res.data || {};
      if (data.questionTypeId) activeType.value = data.questionTypeId;
      Object.assign(state.dataForm, data);
      state.childIndex = -1;
      changeLoading(false);
    });
  }
  async function handleSubmit(type) {
    try {
      const values = await getForm()?.validate();
      if (!values) return;
      if (currentEditor.value && currentEditor.value.validate) {
        const childValid = currentEditor.value.validate();
        if (!childValid) return;
      }
      if (currentEditor.value && currentEditor.value.getData) {
        const childData = currentEditor.value.getData();
        Object.assign(state.dataForm, childData);
      }
      if (!state.dataForm.questionTypeId) {
        state.dataForm.questionTypeId = activeType.value;
      }
      setFormProps({ confirmLoading: true });
      const formMethod = state.dataForm.id ? update : create;
      console.log('values', state.dataForm);
      formMethod(state.dataForm)
        .then(res => {
          createMessage.success(res.msg);
          setFormProps({ confirmLoading: false });
          if (state.submitType == 1) {
            initData();
            state.isContinue = true;
          } else {
            setFormProps({ open: false });
            emit('reload');
          }
        })
        .catch(() => {
          setFormProps({ confirmLoading: false });
        });
    } catch (_) {}
  }

  // 跳转上一个
  function handlePrev() {
    state.currIndex--;
    handleGetNewInfo();
  }

  // 跳转下一个
  function handleNext() {
    state.currIndex++;
    handleGetNewInfo();
  }

  // 重新获取编辑信息
  function handleGetNewInfo() {
    changeLoading(true);
    getForm().resetFields();
    const id = state.allList[state.currIndex].id;
    getData(id);
  }

  function setFormProps(data) {
    setModalProps(data);
  }

  //
  function changeLoading(loading) {
    setModalProps({ loading });
  }

  // 关闭弹窗
  async function onClose() {
    if (state.isContinue) emit('reload');
    return true;
  }

  function changeData(model, index) {
    state.isEdit = false;
    state.childIndex = index;
    for (let key in state.interfaceRes) {
      if (key != model) {
        let faceReList = state.interfaceRes[key];
        for (let i = 0; i < faceReList.length; i++) {
          let relationField = faceReList[i].relationField;
          if (relationField) {
            let modelAll = relationField.split('-');
            let faceMode = '';
            let faceMode2 = modelAll.length == 2 ? modelAll[0].substring(0, modelAll[0].length - 4) + modelAll[1] : '';
            for (let i = 0; i < modelAll.length; i++) {
              faceMode += modelAll[i];
            }
            if (faceMode == model || faceMode2 == model) {
              let options = 'get' + key + 'Options';
              eval(options)(true);
              changeData(key, index);
            }
          }
        }
      }
    }
  }
  function getAllSelectOptions() {}

  // 题型切换
  const handleTypeChange = (type) => {
    activeType.value = type
    state.dataForm.option = ''
    state.dataForm.answer = ''
    state.dataForm.questionTypeId = type
  }
</script>
