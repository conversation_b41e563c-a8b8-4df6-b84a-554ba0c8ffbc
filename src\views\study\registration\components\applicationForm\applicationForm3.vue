<template>
  <div class="form-wrapper">
    <div class="form-header">
      <div class="header-title">开课计划：自主培训和考试业务</div>
    </div>
    <div class="form-content">
      <div class="detail-view">
        <div class="form-row">
          <div class="detail-item">
            <div class="detail-label">姓名</div>
            <div class="detail-value">{{ formData.name || '暂无' }}</div>
          </div>

          <div class="detail-item">
            <div class="detail-label">性别</div>
            <div class="detail-value">{{ formData.sex || '暂无' }}</div>
          </div>
        </div>

        <div class="form-row">
          <div class="detail-item">
            <div class="detail-label">身份证号</div>
            <div class="detail-value">{{ formData.idNo || '暂无' }}</div>
          </div>

          <div class="detail-item">
            <div class="detail-label">出生日期</div>
            <div class="detail-value">{{ formData.birthday || '暂无' }}</div>
          </div>
        </div>

        <div class="form-row">
          <div class="detail-item">
            <div class="detail-label">联系电话</div>
            <div class="detail-value">{{ formData.phone1 || '暂无' }}</div>
          </div>

          <div class="detail-item">
            <div class="detail-label">专业</div>
            <div class="detail-value">{{ formData.major || '暂无' }}</div>
          </div>
        </div>

        <div class="form-row photo-row">
          <div class="detail-item photo-item">
            <div class="detail-label">证件照</div>
            <div class="detail-value">
              <div v-if="formData.userImg" class="photo-preview">
                <a-image
                  :src="apiUrl + formData.userImg.url"
                  :preview="{
                    src: apiUrl + formData.userImg.url,
                    mask: '点击预览',
                  }" />
              </div>
              <div v-else class="no-photo">暂无照片</div>
            </div>
          </div>

          <div class="detail-item photo-item">
            <div class="detail-label">学历证书</div>
            <div class="detail-value">
              <div v-if="formData.diploma" class="photo-preview">
                <a-image
                  :src="apiUrl + formData.diploma.url"
                  :preview="{
                    src: apiUrl + formData.diploma.url,
                    mask: '点击预览',
                  }" />
              </div>
              <div v-else class="no-photo">暂无证书照片</div>
            </div>
          </div>
        </div>

        <div class="form-row">
          <div class="detail-item training-type">
            <div class="detail-label">培训类型</div>
            <div class="detail-value highlight">
              <span class="training-badge" :class="getTrainingTypeClass(formData.learnType)">
                {{ getTrainingTypeText(formData.learnType) }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { PlusOutlined } from '@ant-design/icons-vue';

  const props = defineProps({
    formData: {
      type: Object,
      default: () => ({}),
    },
    apiUrl: {
      type: String,
      default: '',
    },
  });

  const emit = defineEmits(['update:formData']);

  // 培训类型转换函数
  const getTrainingTypeText = type => {
    if (!type) return '暂无';

    switch (type) {
      case '1':
        return '仅培训服务';
      case '2':
        return '仅考试服务';
      case '3':
        return '培训服务+考试服务';
      default:
        return type;
    }
  };

  // 获取培训类型对应的样式类
  const getTrainingTypeClass = type => {
    if (!type) return '';

    switch (type) {
      case '1':
        return 'training-only';
      case '2':
        return 'exam-only';
      case '3':
        return 'training-exam';
      default:
        return '';
    }
  };
</script>

<style lang="less" scoped>
  .form-wrapper {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    margin-bottom: 24px;
    overflow: hidden;
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    }

    .form-header {
      background: linear-gradient(135deg, #1890ff, #36cfc9);
      padding: 16px 20px;
      border-bottom: 1px solid #eee;
      
      .header-title {
        font-size: 18px;
        font-weight: 600;
        color: #fff;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }
    }
    
    .form-content {
      padding: 24px;
      
      .detail-view {
        .form-row {
          display: flex;
          margin-bottom: 20px;
          flex-wrap: wrap;
          gap: 40px;
          
          &.photo-row {
            justify-content: flex-start;
            gap: 60px;
            
            .photo-item {
              min-width: 200px;
              
              .photo-preview {
                border: 1px solid #e8e8e8;
                border-radius: 4px;
                padding: 4px;
                background-color: #fafafa;
                width: 180px;
                height: 220px;
                display: flex;
                justify-content: center;
                align-items: center;
                overflow: hidden;
                
                img {
                  max-width: 100%;
                  max-height: 100%;
                  object-fit: contain;
                }
              }
              
              .no-photo {
                width: 180px;
                height: 220px;
                display: flex;
                justify-content: center;
                align-items: center;
                color: #999;
                background-color: #f5f5f5;
                border: 1px dashed #d9d9d9;
                border-radius: 4px;
              }
            }
          }
        }
        
        .detail-item {
          display: flex;
          align-items: flex-start;
          min-width: 300px;
          
          &.training-type {
            margin-top: 10px;
            
            .detail-value {
              margin-top: 0;
            }
          }
          
          .detail-label {
            font-weight: 600;
            color: #333;
            margin-right: 16px;
            min-width: 80px;
            text-align: right;
          }
          
          .detail-value {
            color: #555;
            flex: 1;
            
            &.highlight {
              font-weight: 500;
            }
          }
          
          .training-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 14px;
            font-weight: 500;
            color: white;
            
            &.training-only {
              background-color: #52c41a;
            }
            
            &.exam-only {
              background-color: #faad14;
            }
            
            &.training-exam {
              background-color: #722ed1;
            }
          }
        }
      }
    }
  }
</style>
