<template>
  <div class="id-card-form">
    <div class="form-layout">
      <div class="info-section">
        <div class="info-title">证件信息</div>
        <div class="info-content">
          <a-form :model="dataForm" ref="formRef" labelAlign="left">
            <div class="first-row">
              <div class="info-items">
                <a-form-item label="姓名" name="name">
                  <p>{{ dataForm.name }} </p>
                </a-form-item>
                <a-form-item label="性别" name="sex">
                  <p>{{ dataForm.sex }} </p>
                </a-form-item>
                <a-form-item label="民族" name="nation">
                  <p>{{ dataForm.nation }} </p>
                </a-form-item>
                <a-form-item label="出生" name="birthday">
                  <p>{{ dataForm.birthday }} </p>
                </a-form-item>
                <a-form-item label="住址" name="permanentAddress">
                  <p>{{ dataForm.permanentAddress }} </p>
                </a-form-item>
                <a-form-item label="公民身份证号码" name="idNo">
                  <p>{{ dataForm.idNo }} </p>
                </a-form-item>
              </div>
              <div class="person-photo">
                <a-image
                  v-if="dataForm.userImg"
                  :src="apiUrl + dataForm.userImg.url"
                  :preview="{
                    src: apiUrl + dataForm.userImg.url,
                    mask: '点击预览',
                  }" />
                <div v-else class="empty-photo">暂无照片</div>
              </div>
            </div>
          </a-form>
        </div>
      </div>
      <div class="photo-section">
        <div class="photo-container">
          <div class="photo-item">
            <div class="photo-wrapper">
              <a-image
                v-if="dataForm.idCardFront"
                :src="apiUrl + dataForm.idCardFront.url"
                :preview="{
                  src: apiUrl + dataForm.idCardFront.url,
                  mask: '点击预览',
                }" />
              <div v-else class="empty-placeholder">暂无照片</div>
            </div>
          </div>
          <div class="photo-item">
            <div class="photo-wrapper">
              <a-image
                v-if="dataForm.idCardReverse"
                :src="apiUrl + dataForm.idCardReverse.url"
                :preview="{
                  src: apiUrl + dataForm.idCardReverse.url,
                  mask: '点击预览',
                }" />
              <div v-else class="empty-placeholder">暂无照片</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import type { FormInstance } from 'ant-design-vue';
  import { getAuditIdCardInfoAsync } from '.';
  import { xundaUtils } from '@/utils/xunda';
  import { useGlobSetting } from '@/hooks/setting';

  const emit = defineEmits(['changeLoading', 'next', 'update:auditState', 'update:semesterCatogry']);
  const changeLoading = loading => {
    emit('changeLoading', loading);
  };
  const formRef = ref<FormInstance>();
  const dataForm = ref<any>({} as any);
  const globSetting = useGlobSetting();
  const apiUrl = ref(globSetting.apiUrl);

  const init = (record: any) => {
    changeLoading(true);
    getAuditIdCardInfoAsync(record.id)
      .then(res => {
        dataForm.value.name = res.data.name;
        dataForm.value.idNo = res.data.idNo;
        dataForm.value.idCardReverse = res.data.idCardReverse;
        dataForm.value.idCardFront = res.data.idCardFront;
        dataForm.value.userImg = res.data.userImg;
        dataForm.value.birthday = res.data.birthday;
        dataForm.value.sex = res.data.sex;
        dataForm.value.nation = res.data.nation;
        dataForm.value.permanentAddress = res.data.permanentAddress;
        emit('update:auditState', res.data.state);
        emit('update:semesterCatogry', res.data.semesterCatogry);
        changeLoading(false);
      })
      .catch(() => {
        changeLoading(false);
      });
  };

  defineExpose({
    init,
  });
</script>

<style lang="less" scoped>
  .id-card-form {
    padding: 20px;
    background: linear-gradient(to bottom, #ffffff, #f8f9fa);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;

    @media screen and (max-width: 768px) {
      padding: 15px;
    }

    .form-layout {
      display: flex;
      flex-direction: column;
      gap: 20px;
      align-items: stretch;

      @media screen and (max-width: 1200px) {
        flex-direction: column;
        gap: 15px;
      }

      .info-section {
        flex: 1;
        min-width: 360px;
        width: 100%;
        background: #ffffff;
        border-radius: 16px;
        overflow: hidden;
        transition: transform 0.3s ease;

        &:hover {
          transform: translateY(-2px);
        }

        .info-title {
          font-size: 16px;
          font-weight: 600;
          color: #1a1a1a;
          text-align: center;
          padding: 10px 0;
        }

        .info-content {
          padding: 15px;
          background: #ffffff;
          border-radius: 10px;
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.03);

          .first-row {
            display: flex;
            margin-bottom: 15px;
            gap: 15px;
            align-items: flex-start;

            .person-photo {
              width: 110px;
              height: 140px;
              flex-shrink: 0;
              border: 2px solid #e8e8e8;
              border-radius: 8px;
              overflow: hidden;
              display: flex;
              align-items: center;
              justify-content: center;
              background: #f8f9fa;
              transition: all 0.3s ease;

              &:hover {
                border-color: #1890ff;
                box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
              }

              :deep(.ant-image) {
                width: 100%;
                height: 100%;

                img {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                }
              }

              .empty-photo {
                color: #bfbfbf;
                font-size: 14px;
                text-align: center;
                padding: 12px;
              }
            }

            .info-items {
              flex: 1;
            }
          }
        }

        @media screen and (max-width: 1200px) {
          width: 100%;
        }

        :deep(.ant-form-item) {
          position: relative;
          margin-bottom: 10px;

          @media screen and (max-width: 576px) {
            margin-bottom: 12px;
          }

          &:last-child {
            margin-bottom: 0;
          }

          .ant-form-item-label > label {
            color: #595959;
            font-size: 14px;
            font-weight: 500;
          }

          p {
            font-size: 14px;
            margin-bottom: 0;
            color: #262626;
            line-height: 1.5;
            padding: 2px 0;
          }
        }
      }

      .photo-section {
        flex: 2;
        width: 100%;
        @media screen and (max-width: 1200px) {
          width: 100%;
        }
      }
    }

    .photo-container {
      display: flex;
      gap: 15px;
      justify-content: space-between;
      flex-wrap: wrap;

      @media screen and (max-width: 992px) {
        flex-direction: column;
        align-items: center;
        gap: 28px;
      }
    }

    .photo-item {
      flex: 1;
      width: 100%;
      background: #ffffff;
      border-radius: 12px;
      padding: 15px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.06);
      }

      @media screen and (max-width: 992px) {
        width: 100%;
      }

      .photo-title {
        font-size: 17px;
        font-weight: 600;
        color: #262626;
        margin-bottom: 20px;
        text-align: center;
        padding-bottom: 12px;
      }

      .photo-wrapper {
        width: 100%;
        height: 320px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        background: linear-gradient(to bottom, #ffffff, #fafafa);
        transition: all 0.3s ease;

        @media screen and (max-width: 576px) {
          height: 250px;
        }

        &:hover {
          border-color: #40a9ff;
          box-shadow: 0 6px 16px rgba(24, 144, 255, 0.12);
        }

        :deep(.ant-image) {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 12px;

          img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            border-radius: 8px;
          }
        }

        .empty-placeholder {
          color: #bfbfbf;
          font-size: 15px;
          text-align: center;
          padding: 20px;
        }
      }

      .id-photo {
        width: 100%;
        height: 100%;
        object-fit: contain;
        border-radius: 8px;
      }

      .upload-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;
        color: #bfbfbf;
        padding: 24px;

        .anticon {
          font-size: 32px;
          color: #8c8c8c;
        }
      }
    }
  }
</style>
