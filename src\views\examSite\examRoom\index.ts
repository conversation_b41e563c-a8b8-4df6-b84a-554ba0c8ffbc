import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';

// 基础Api
export const examPlaceApi = '/api/exam/examPlace';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: examPlaceApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: examPlaceApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: examPlaceApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: examPlaceApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: examPlaceApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: examPlaceApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: examPlaceApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: examPlaceApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: examPlaceApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: examPlaceApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: examPlaceApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: examPlaceApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: examPlaceApi + `/getForSelect`, data });
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'name',
    label: t('名称'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('考场名称'),
    dataIndex: 'name',
    sorter: true,
    width: 120,
  },
  {
    title: t('考场编号'),
    dataIndex: 'code',
    sorter: true,
    width: 120,
  },
  {
    title: t('考场位置'),
    dataIndex: 'location',
    sorter: true,
    width: 150,
  },
  {
    title: t('容量'),
    dataIndex: 'capacity',
    sorter: true,
    width: 80,
  },
  {
    title: t('设备信息'),
    dataIndex: 'equipmentInfo',
    sorter: true,
    width: 150,
  },
  {
    title: t('联系人'),
    dataIndex: 'contactPerson',
    sorter: true,
    width: 100,
  },
  {
    title: t('联系电话'),
    dataIndex: 'contactPhone',
    sorter: true,
    width: 120,
  },
  {
    title: t('状态'),
    dataIndex: 'status',
    sorter: true,
    width: 80,
    customRender: ({ value }) => {
      if (value === 1) {
        return t('启用');
      } else if (value === 0) {
        return t('禁用');
      }
      return value;
    },
  },
  {
    title: t('备注'),
    dataIndex: 'note',
    sorter: true,
    width: 150,
  },
];
