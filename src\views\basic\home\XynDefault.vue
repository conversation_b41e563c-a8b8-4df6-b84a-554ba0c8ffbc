<template>
  <div :class="prefixCls" class="flex flex-col">
    <div :class="`${prefixCls}-top`" class="flex justify-between">
      <div :class="`${prefixCls}-top-left`">
        <div>快速开始引导</div>
        <div>欢迎来到我们的后台管理系统</div>
      </div>
      <div :class="`${prefixCls}-top-right`">
        <div>你好,{{ userInfo.userName }}</div>
        <div>我们的一天又开始了</div>
      </div>
    </div>
    <div :class="`${prefixCls}-bottom`" class="flex-1 flex justify-between">
      <div :class="`${prefixCls}-bottom-left`" class="flex flex-col">
        <div :class="`${prefixCls}-bottom-left-top`" class="flex justify-between">
          <div>
            <div :class="`${prefixCls}-bottom-title`" class="flex justify-between">
              <div :class="`${prefixCls}-bottom-title-left`">常用菜单</div>
            </div>
            <XundaEmpty :image="emptyImage" v-if="menuList.length === 0" />
            <div v-if="menuList.length > 0" class="flex flex-wrap pt-30px">
              <div
                v-for="(item, index) in menuList"
                :key="index"
                :class="`${prefixCls}-bottom-left-top-menu`"
                @click="router.push(item.urlAddress)"
                class="flex items-center">
                <div
                  class="icon-box flex items-center justify-center"
                  :style="{
                    backgroundColor: menuColorList[(index + 6) / 6],
                  }">
                  <span :class="item.icon"></span>
                </div>
                <div>{{ item.fullName }}</div>
              </div>
            </div>
          </div>
          <div class="w-full">
            <div :class="`${prefixCls}-bottom-title`" class="flex justify-between">
              <div :class="`${prefixCls}-bottom-title-left`">公告</div>
              <div :class="`${prefixCls}-bottom-title-right`" @click="router.push('/system/notice')">
                <span>查看更多</span>
                <RightOutlined />
              </div>
            </div>
            <XundaEmpty :image="emptyImage" v-if="noticeList.length === 0" />
            <div v-else style="overflow: auto" class="w-full">
              <div :class="`${prefixCls}-bottom-notice`" class="flex justify-between w-full" v-for="item in noticeList" :key="item.id">
                <img src="@/assets/images/xynDefault/notice.png" />
                <div :class="`${prefixCls}-bottom-notice-right`">
                  <div :class="`${prefixCls}-bottom-notice-top`" class="flex">
                    <div class="flex-1">{{ item.title }}</div>
                    <div>{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }} </div>
                  </div>
                  <div :class="`${prefixCls}-bottom-notice-desc`"> {{ item.category }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div :class="`${prefixCls}-bottom-left-bottom`">
          <div :class="`${prefixCls}-bottom-title`" class="flex justify-between">
            <div :class="`${prefixCls}-bottom-title-left`">热门应用</div>
            <div :class="`${prefixCls}-bottom-title-right`" @click="router.push('/xynStore/index')">
              <span>应用市场</span>
              <RightOutlined />
            </div>
          </div>
          <XundaEmpty :image="emptyImage" v-if="AppList.length === 0" />
          <div :class="`${prefixCls}-bottom-left-bottom-app`" class="flex">
            <div v-for="(item, index) in AppList" :key="index" class="flex-col justify-center items-center">
              <img :src="item.icon" />
              <div> {{ item.name }}</div>
            </div>
          </div>
        </div>
      </div>
      <div :class="`${prefixCls}-bottom-right`" class="flex flex-col w-full h-full">
        <div :class="`${prefixCls}-bottom-title`" class="flex justify-between">
          <div :class="`${prefixCls}-bottom-title-left`">日程安排</div>
          <div :class="`${prefixCls}-bottom-title-right`" @click="router.push('/workFlow/schedule')">
            <span>查看日程</span>
            <RightOutlined />
          </div>
        </div>
        <!-- <div class="schedule-top flex pt-15px">
          <a-date-picker v-model:value="yearMonth" size="large" picker="month" class="flex-1" @change="handleDatePickerChange" />
          <a-button
            type="primary"
            size="large"
            class="ml-15px"
            @click="
              handleDateClick({
                date: new Date(),
              })
            "
            >添加日程</a-button
          >
        </div> -->
        <div class="schedule-container">
          <FullCalendar ref="calendarRef" :options="calendarOptions">
            <!-- <template v-slot:eventContent="arg">
               {{ arg }}
              <div>{{ Object.keys(arg.event).length > 0 ? 1 : 0 }}</div>
            </template> -->
          </FullCalendar>
        </div>
        <!-- <div :class="`${prefixCls}-bottom-right-tabs`" class="flex justify-around">
          <div
            :class="{
              active: state.tabsId === 1,
            }"
            @click="handleTbaChange(1)"
            >我的日程</div
          >
          <div
            :class="{
              active: state.tabsId === 2,
            }"
            @click="handleTbaChange(2)"
            >日程安排</div
          >
        </div> -->
        <a-divider></a-divider>
        <div :class="`${prefixCls}-bottom-right-list`" class="flex-1">
          <XundaEmpty :image="emptyImage" v-if="list.length === 0" />
          <div :class="`${prefixCls}-bottom-right-list-item`" class="flex-1" v-for="(item, index) in list" :key="index" >
            <div>
              <div :class="`${prefixCls}-bottom-right-list-item-name`" class="flex-1">{{ item.title }} </div>
              <div :class="`${prefixCls}-bottom-right-list-item-time`" class="flex-1">{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }} </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <CreateScheduleForm @register="registerForm" @reload="reload" />
    <ScheduleDetail @register="registerDetail" @reload="reload" />
  </div>
</template>

<script lang="ts">
  import { defineComponent, computed, unref, onMounted, ref, reactive, watch } from 'vue';
  import { useDesign } from '@/hooks/web/useDesign';
  import { RightOutlined } from '@ant-design/icons-vue';
  import { useUserStore } from '@/store/modules/user';
  import { getNoticeList } from '@/api/system/message';
  import { getCommonMenuList } from '@/api/system/commonMenu';
  import { useRouter } from 'vue-router';
  import dayjs from 'dayjs';
  import { useAppStore } from '@/store/modules/app';
  import FullCalendar from '@fullcalendar/vue3';
  import { CalendarOptions } from '@fullcalendar/core';
  import dayGridPlugin from '@fullcalendar/daygrid';
  import interactionPlugin from '@fullcalendar/interaction';
  import timeGridPlugin from '@fullcalendar/timegrid';
  import { calendar } from '@/views/workFlow/schedule/calendar';
  import { getScheduleList } from '@/api/workFlow/schedule';
  import emptyImage from '@/assets/images/dashboard-nodata.png';
  import CreateScheduleForm from '@/views/workFlow/schedule/Form.vue';
  import ScheduleDetail from '@/views/workFlow/schedule/Detail.vue';
  import { useModal } from '@/components/Modal';

  export default defineComponent({
    name: 'XynDefault',
    components: {
      RightOutlined,
      FullCalendar,
      CreateScheduleForm,
      ScheduleDetail,
    },

    setup(props) {
      interface State {
        startTime: string;
        endTime: string;
        tabsId: number;
      }

      const { prefixCls } = useDesign('xyn-default-wrap');
      const userStore = useUserStore();
      const userInfo = userStore.getUserInfo;
      const router = useRouter();
      const menuColorList = ['#448EF8', '#FF7B1A', '#6D6CFC', '#FF7B1A', '#00C8AD', '#6D6CFC'];
      const noticeList = ref<any[]>([]);
      const menuList = ref<any[]>([]);
      const AppList = ref<any[]>([]);
      const list = ref<any[]>([]);
      const yearMonth = ref<string | Date>('');

      const [registerForm, { openModal: openFormModal }] = useModal();
      const [registerDetail, { openModal: openDetailModal }] = useModal();

      const state = reactive<State>({
        startTime: '',
        endTime: '',
        tabsId: 1,
      });
      const appStore = useAppStore();
      const getSysConfig = computed(() => appStore.getSysConfigInfo);

      const calendarOptions = reactive<CalendarOptions>({
        plugins: [dayGridPlugin, interactionPlugin, timeGridPlugin],
        initialView: unref(getSysConfig).defaultView || 'dayGridMonth',
        firstDay: unref(getSysConfig).firstDay,
        headerToolbar: {
          left: 'prev,next today',
          center: 'title',
          right: 'timeGridDay,timeGridWeek,dayGridMonth',
        },
        events: [], //数据
        eventColor: '#3BB2E3', //事件背景颜色
        eventClick: handleEventClick,
        dateClick: handleDateClick,
        editable: false, // 是否可以进行（拖动、缩放）修改
        eventStartEditable: false, // Event日程开始时间可以改变，默认true，如果是false其实就是指日程块不能随意拖动，只能上下拉伸改变他的endTime
        eventDurationEditable: false, // Event日程的开始结束时间距离是否可以改变，默认true，如果是false则表示开始结束时间范围不能拉伸，只能拖拽
        selectable: false, // 是否可以选中日历格
        selectMirror: false,
        selectMinDistance: 0, // 选中日历格的最小距离
        dayMaxEvents: true,
        weekends: true,
        navLinks: false, // 天链接
        slotEventOverlap: false,
        datesSet: datesRender,
        dayMaxEventRows: 2,
        locale: 'zh',
        aspectRatio: 1.65,
        buttonText: { today: '今日', month: '月', week: '周', day: '日' },
        slotLabelFormat: { hour: '2-digit', minute: '2-digit', meridiem: false, hour12: false }, // 设置时间为24小时
        allDayText: '全天',
        views: {
          //对应月视图
          dayGridMonth: {
            displayEventTime: false, //是否显示时间
            dayMaxEventRows: 4,
            moreLinkClick: 'popover',
            dayCellContent(item) {
              let date = new Date(item.date); // 参数需要毫秒数，所以这里将秒数乘于 1000
              let Y = date.getFullYear();
              let M = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
              let D = date.getDate();
              let _dateF: any = calendar.solar2lunar(Y, M, D);
              let myClass = '';
              if (_dateF.isToday) myClass = 'today-month';
              // if (unref(getSysConfig).showLunarCalendar) {
              //   let IDayCn = _dateF.IDayCn;
              //   if (IDayCn == '初一') IDayCn = _dateF.IMonthCn;
              //   return { html: `<p class='calendar-right'><label class='${myClass}'>${_dateF.cDay}</label><span>${IDayCn}</span></p>` };
              // }
              return { html: `<p class='calendar-right'><label class='${myClass}'>${_dateF.cDay}</label></p>` };
            },
          },
          //对应周视图调整
          timeGridWeek: {
            displayEventTime: false, //是否显示时间
            dayHeaderContent(item) {
              let date = new Date(item.date); // 参数需要毫秒数，所以这里将秒数乘于 1000
              let Y = date.getFullYear();
              let M = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
              let D = date.getDate();
              let _dateF: any = calendar.solar2lunar(Y, M, D);
              let myClass = 'calender-week';
              if (_dateF.isToday) myClass = 'calender-week today-week';
              // if (unref(getSysConfig).showLunarCalendar) {
              //   const htmlVal = `<div class='${myClass}'>${_dateF.cDay}</div><div class="list-week"><div>周${_dateF.ncWeek.slice(
              //     2,
              //   )}</div><div class='list-calendar'>${_dateF.IDayCn}</div></div></div>`;
              //   return {
              //     html: htmlVal,
              //   };
              // }
              return { html: `<div class='${myClass}'>${_dateF.cDay}</div><div class='list-week'><div >周${_dateF.ncWeek.slice(2)}</div></div></div>` };
            },
          },
          timeGridDay: {
            displayEventTime: false, //是否显示时间
            dayHeaderContent(item) {
              let date = new Date(item.date); // 参数需要毫秒数，所以这里将秒数乘于 1000
              let Y = date.getFullYear() + '-';
              let M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
              let D = date.getDate();
              let date_ = Y + M + D;
              let _date = date_.split('-');
              let _dateF: any = calendar.solar2lunar(_date[0], _date[1], _date[2]);
              let myClass = 'calender-week';
              if (_dateF.isToday) myClass = 'calender-week today-week';
              if (unref(getSysConfig).showLunarCalendar) {
                return {
                  html: `<div class='${myClass}'>${_dateF.cDay}</div><div class='list-week'><div>周${_dateF.ncWeek.slice(2)}</div><div class='list-calendar'>${
                    _dateF.IDayCn
                  }</div></div></div>`,
                };
              }
              return { html: `<div class='${myClass}'>${_dateF.cDay}</div><div class='list-week'><div >周${_dateF.ncWeek.slice(2)}</div></div></div>` };
            },
          },
        },
      });

      const getUserInfo = computed(() => userStore.getUserInfo || {});

      watch(
        () => unref(getSysConfig).firstDay,
        val => {
          calendarOptions.firstDay = val;
        },
      );

      function handleEventClick(data) {
        if (unref(getUserInfo).userId == data.event.extendedProps.creatorUserId) return openFormModal(true, { id: data.event.id });
        openDetailModal(true, { id: data.event.id, type: 1 });
      }
      function handleDateClick(data: any) {
        let startTime = dayjs(data.date).format('YYYY-MM-DD HH:00');
        let clickTime = dayjs(data.date).format('YYYY-MM-DD');
        let currTime = dayjs().format('YYYY-MM-DD');
        if (clickTime == currTime) {
          let thisDate = new Date();
          thisDate.setHours(thisDate.getHours() + 1);
          startTime = dayjs(thisDate).format('YYYY-MM-DD HH:00');
        }
        openFormModal(true, { startTime: new Date(startTime).getTime(), id: '', duration: unref(getSysConfig).duration });
      }
      const handleDatePickerChange = date => {
        console.log(date);
      };
      const handleTbaChange = (tabsId: number) => {
        state.tabsId = tabsId;
        initData();
      };
      function datesRender(calendar) {
        let view = calendar.view;
        state.startTime = dayjs(view.activeStart).format('YYYY-MM-DD HH:mm');
        state.endTime = dayjs(view.activeEnd).format('YYYY-MM-DD HH:mm');
        initData();
      }
      function initData() {
        // const query = { startTime: state.startTime, endTime: state.endTime };
        getScheduleList({
          ...state,
        }).then(res => {
          list.value = res.data.list.slice(0, 3);
          calendarOptions.events = res?.data?.list.map(o => {
            let allDay = false;
            let startDay = dayjs(o.startDay).format('YYYY-MM-DD');
            let endDay = '';
            if (o.endDay) endDay = dayjs(o.endDay).format('YYYY-MM-DD');
            allDay = o.allDay && startDay != endDay ? false : o.allDay;
            return {
              id: o.id,
              title: o.title,
              start: o.startDay,
              end: o.endDay,
              color: o.color,
              editable: false,
              allDay: allDay,
              creatorUserId: o.creatorUserId,
            };
          });

        });
      }
      function reload() {
        initData();
      }

      onMounted(() => {
        getNoticeList({
          type: ['1'],
          currentPage: 1,
          pageSize: 20,
        }).then(res => {
          noticeList.value = res.data.list;
        });

        getCommonMenuList({}).then(res => {
          menuList.value = res.data.list;
        });
      });

      return {
        prefixCls,
        userInfo,
        emptyImage,
        router,
        AppList,
        noticeList,
        menuList,
        menuColorList,
        yearMonth,
        dayjs,
        calendarOptions,
        registerForm,
        registerDetail,
        reload,
        list,
        state,
        handleTbaChange,
        handleDateClick,
        handleEventClick,
        handleDatePickerChange,

        RightOutlined,
        FullCalendar,
        CreateScheduleForm,
      };
    },
  });
</script>

<style lang="less">
  @prefix-cls: ~'@{namespace}-xyn-default-wrap';
  @margin: 18px;

  .@{prefix-cls} {
    height: 100%;
    width: 100%;
    .ant-empty {
      padding-top: 80px;

      .ant-empty-image {
        height: auto;

        img {
          width: 200px;
          height: 150px;
          display: block;
          margin: 0 auto 0;
        }
        .ant-empty-description,
        :deep(.ant-empty-description) {
          color: #9ba2ab;
        }
      }
    }

    &-top {
      margin-bottom: @margin;
      // height: 210px;
      height: 19.5%;

      & > div {
        flex: 1;
        height: 100%;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        padding: 55px 0 55px 25px;

        div {
          &:first-child {
            font-weight: 500;
            font-size: 18px;
            color: #333333;
            line-height: 30px;
            text-align: left;
          }
          &:last-child {
            font-size: 14px;
            color: #9ba2ab;
            line-height: 20px;
            text-align: left;
            font-style: normal;
            margin-top: 15px;
          }
        }
      }

      &-left {
        background-image: url('@/assets/images/xynDefault/top-left.png');
        margin-right: @margin;
      }
      &-right {
        background-image: url('@/assets/images/xynDefault/top-right.png');
      }
    }
    &-bottom {
      &-left {
        flex: 1;
        &-top {
          flex: 1;

          & > div {
            background: #ffffff;
            border-radius: 15px 15px 15px 15px;
            padding: 24px;
            // flex: 1;
            width: calc(50% - 9px);
            flex-shrink: 0;
            & + div {
              margin-left: @margin;
            }
          }

          &-menu {
            width: calc(50% - 9px);
            height: 90px;
            background: #ffffff;
            border-radius: 10px 10px 10px 10px;
            border: 1px solid #eaeaea;
            padding: 20px;
            cursor: pointer;
            margin-bottom: 20px;
            &:nth-child(2n) {
              margin-left: 18px;
            }

            div {
              font-weight: 600;
              font-size: 15px;
              color: #333333;
              line-height: 20px;
              text-align: left;
              font-style: normal;
            }

            .icon-box {
              width: 51px;
              height: 51px;
              background: #448ef8;
              border-radius: 8px 8px 8px 8px;
              color: #fff;
              margin-right: 15px;

              span {
                font-size: 30px;
              }
            }
          }
        }
        &-bottom {
          margin-top: @margin;
          height: 35.7%;
          background: #ffffff;
          border-radius: 15px 15px 15px 15px;
          padding: 24px;
          &-app {
            padding-left: 33px;
            padding-top: 60px;
            & > div {
              width: 125px;
              cursor: pointer;
              & + div {
                margin-left: 60px;
              }
              img {
                width: 85px;
                height: 85px;
                display: block;
                margin: 0 auto;
              }
              div {
                margin-top: @margin;
                font-size: 15px;
                color: #333333;
                line-height: 20px;
                text-align: left;
                text-align: center;
              }
              &:hover {
                div {
                  color: #448ef8;
                  // font-weight: 600;
                }
              }
            }
          }
        }
      }

      &-right {
        width: 33.3%;
        flex-shrink: 0;
        margin-left: @margin;
        background: #ffffff;
        border-radius: 15px 15px 15px 15px;
        padding: 24px;

        &-tabs {
          border-top: 1px solid #d9d9d9;
          padding: 20px 0;
          div {
            font-weight: 500;
            font-size: 15px;
            color: #333333;
            line-height: 35px;
            text-align: center;
            font-style: normal;
            cursor: pointer;
            &.active {
              position: relative;
              color: #448ef8;
              &:after {
                content: '';
                position: absolute;
                left: 50%;
                bottom: -3px;
                transform: translate(-50%, 0);
                width: 31px;
                height: 3px;
                background: #1890ff;
                border-radius: 2px 2px 2px 2px;
              }
            }
          }
        }

        &-list {
          overflow: hidden auto;
          &-item {
            padding-left: 25px;
            margin-bottom: 10px;
            position: relative;
            &:before {
              content: '';
              position: absolute;
              top: 11px;
              left: 0;
              width: 8px;
              height: 8px;
              background: #1990ff;
              border-radius: 50%;
            }
            &-name {
              font-size: 15px;
              color: #448ef8;
              line-height: 30px;
              text-align: left;
            }
            &-time {
              font-size: 14px;
              color: #9ba2ab;
              line-height: 20px;
              margin-top: 10px;
            }
          }
        }

        .schedule-container {
          padding: 0;
          height: 54%;
          .fc .fc-scroller {
            overflow: hidden scroll !important;
          }
          .fc .fc-scrollgrid,
          .fc .fc-scrollgrid table,
          .fc-theme-standard td,
          .fc-theme-standard th {
            border-width: 0px;
          }
          .fc-media-screen {
            height: 100%;
            cursor: pointer;
          }
          .fc-toolbar.fc-header-toolbar {
            padding: 10px;
            margin-bottom: 0;
          }
          .fc-toolbar-chunk {
            display: flex;
          }
          .fc-button-primary {
            background-color: @primary-color !important;
            border-color: @primary-color !important;
            height: 32px;
            line-height: 32px;
            padding: 0 0.65em;
            font-size: 12px;
            display: flex;
            align-items: center;
          }
          .fc-button-primary:not(:disabled):active,
          .fc-button-primary:not(:disabled).fc-button-active {
            background-color: @primary-5 !important;
            border-color: @primary-5 !important;
          }
          .fc-button-primary:not(:disabled):focus {
            box-shadow: unset !important;
          }
          .fc-button .fc-icon {
            line-height: 16px;
          }
          .fc-view th {
            height: 40px;
            line-height: 40px;
            font-size: 16px;
            color: #74798c;
            // color: #909399;
            font-weight: normal;
            // background: @app-content-background;
          }
          .fc .fc-popover {
            z-index: 999 !important;
            max-height: 200px;
            display: flex;
            flex-direction: column;
            .fc-popover-body {
              overflow: auto;
              flex: 1;
            }
          }
          .fc-center {
            color: @text-color-base;
          }
          .fc-view th,
          .fc-view td,
          .fc-view thead,
          .fc-view tbody,
          .fc-view .fc-divider,
          .fc-view .fc-row,
          .fc-view .fc-content,
          .fc-view .fc-popover,
          .fc-view .fc-list-view,
          .fc-view .fc-list-header td {
            border-color: #ebeef5;
            color: @text-color-base;
            a {
              color: @text-color-base;
            }
          }
        }
      }

      &-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        &-left {
          font-weight: 500;
          font-size: 15px;
          color: #333333;
          line-height: 20px;
          text-align: left;
        }
        &-right {
          font-size: 14px;
          color: #448ef9;
          line-height: 20px;
          text-align: left;
          cursor: pointer;
        }
      }

      &-notice {
        margin-top: 28px;
        overflow: hidden;
        cursor: pointer;
        img {
          width: 25px;
          height: 25px;
          flex-shrink: 0;
          margin-right: 8px;
        }

        &-right {
          width: calc(100% - 8px - 25px);
        }

        &-top {
          div {
            flex-shrink: 0;
            & + div {
              margin-left: 40px;
            }

            &:first-child {
              font-size: 15px;
              color: #448ef8;
              line-height: 20px;
              text-align: left;
              font-style: normal;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            &:last-child {
              font-size: 14px;
              color: #9ba2ab;
              line-height: 20px;
              text-align: right;
            }
          }
        }
        &-desc {
          width: 100%;
          font-size: 16px;
          color: #74798c;
          line-height: 20px;
          text-align: left;
          font-style: normal;
          margin-top: 10px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  .fc .fc-timeGridDay-view .fc-col-header-cell-cushion,
  .fc .fc-timeGridWeek-view .fc-col-header-cell-cushion {
    padding: 2px 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 64px;
  }

  .fc-col-header,
  .fc-daygrid-body,
  .fc-scrollgrid-sync-table,
  .fc-daygrid-day-top,
  .fc-daygrid-day-number {
    width: 100% !important;
  }

  .fc-daygrid-day-events,
  .fc-daygrid-body-unbalanced {
    min-height: unset;
  }
  .today-month {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: @primary-color !important;
    line-height: 20px;
    color: #fff;
    text-align: center;
  }

  .calendar-right {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 5px;
  }

  .calender-week {
    width: 45px;
    height: 45px;
    font-size: 24px;
    line-height: 45px;
    background-color: #eaedf0;
    color: #303133;
    font-weight: bold;
    border-radius: 50%;

    .fc-view-harness th {
      height: 85px !important;
    }
  }

  .today-week {
    width: 45px;
    height: 45px;
    font-size: 24px;
    line-height: 45px;
    background-color: @primary-color !important;
    color: #fff;
    font-weight: bold;
    border-radius: 50%;

    .fc-view-harness th {
      height: 85px !important;
    }
  }

  .list-week {
    margin-left: 10px;
    font-size: 14px;
  }

  .list-calendar {
    margin-top: -20px;
    font-size: 14px;
  }
  @media (max-width: 1360px) {
    .fc .fc-timeGridWeek-view .fc-col-header-cell-cushion {
      display: inline-block !important;
      margin-top: 10px;
    }
    .fc .fc-timeGridWeek-view .list-week {
      text-align: center;
      width: 100%;
      margin-left: 0 !important;
    }
  }
  html[data-theme='dark'] {
    .schedule-container {
      .fc-theme-standard .fc-scrollgrid {
        border: 1px solid #303030 !important;
      }
      .fc-theme-standard td,
      .fc-theme-standard th {
        border: 1px solid #303030 !important;
      }
      .calender-week {
        background-color: #303030;
        color: #fff;
      }
    }
  }
</style>
