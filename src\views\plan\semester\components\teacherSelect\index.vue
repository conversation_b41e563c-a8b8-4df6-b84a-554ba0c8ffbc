<template>
  <div class="dual-table-layout">
    <div class="table-wrapper">
      <div class="left-table" v-if="!disabled">
        <div class="xunda-content-wrapper-search-box">
          <BasicForm
            @register="registerLeftSearchForm"
            :schemas="leftSearchSchemas"
            @submit="handleSearchSubmit"
            @reset="handleSearchReset"
            class="search-form">
          </BasicForm>
        </div>
        <div class="header-wrapper" v-if="leftTitle || $slots.leftHeader">
          <slot name="leftHeader">
            <span class="title">{{ leftTitle }}</span>
          </slot>
        </div>
        <slot name="leftContent" />
        <BasicTable @register="registerTable" ref="originalTableRef">
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'action'">
              <TableAction
                :actions="[
                  {
                    label: t('选择'),
                    onClick: doSelectHandler.bind(null, record),
                    disabled: selectedIds?.includes(record.id),
                  },
                  {
                    label: t('取消选择'),
                    onClick: removeSelectHandler.bind(null, record),
                    disabled: !selectedIds.includes(record.id),
                  },
                ]" />
            </template>
          </template>
        </BasicTable>
      </div>
      <div :class="'right-table' + (disabled ? 'disabled' : '')">
        <div class="header-wrapper">
          <slot name="rightHeader" v-if="!disabled">
            <span class="title">已选</span>
            <a-button type="link" class="clear-btn" @click="randomHandler">随机生成</a-button>
            <a-button type="link" class="clear-btn" @click="removeAll">清空列表</a-button>
          </slot>
        </div>
        <slot name="rightContent" />
        <BasicTable @register="registerTable2" ref="selectedTableRef">
          <template #tableTitle>
            <a-button type="link" preIcon="icon-ym icon-ym-btn-refresh" @click="reloadSelectedTable()" v-if="!disabled"> {{ t('重置') }}</a-button>
            <a-button type="link" preIcon="icon-ym icon-ym-btn-clearn" @click="handelBatchRemoveSelected()" v-if="!disabled">
              {{ t('common.batchDelText', '批量删除') }}</a-button
            >
          </template>
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'action'">
              <TableAction
                :actions="[
                  {
                    label: t('取消选择'),
                    onClick: removeSelectHandler.bind(null, record),
                  },
                ]" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { toRefs, ref, reactive, unref, watch, computed } from 'vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { BasicTable, useTable, TableAction, TableActionType } from '@/components/Table';
  import { getAllTeacher, getSemesterTeachers, saveTeacherJson, teacherColumns, randomGenTeacher, leftSearchSchemas } from '.';
  import { BasicForm, useForm } from '@/components/Form';

  interface State {
    treeData: any[];
    dataForm: any;
    keyword: string;
    selectedData: any[];
    selectedDataIds: any[];
  }
  defineOptions({ name: 'TeacherSelect', inheritAttrs: false });

  const { t } = useI18n();
  const { createMessage } = useMessage();
  const state = reactive<State>({
    treeData: [],
    dataForm: {
      interfaceIdentId: '',
      dataInterfaceIds: [],
    },
    keyword: '',
    selectedData: [],
    selectedDataIds: [],
  });
  const { treeData, selectedData, selectedDataIds, keyword } = toRefs(state);
  const props = defineProps({
    leftTitle: {
      type: String,
      default: '',
    },
    rightTitle: {
      type: String,
      default: '',
    },
    semesterId: {
      type: String,
      default: '',
    },
    disabled: { type: Boolean, default: false },
  });

  const allSearchInfo = {
    moduleId: '661952363199727173',
    superQueryJson: '',
    dataType: 1,
    keyword: '',
  };

  // 定义组件的 emits
  const emit = defineEmits(['changeLoading', 'handleStep']);
  const changeLoading = loading => {
    emit('changeLoading', loading);
  };

  const [registerLeftSearchForm] = useForm({
    baseColProps: { span: 6 },
    showActionButtonGroup: true,
    showAdvancedButton: true,
    compact: true,
  });

  function handleSearchReset() {
    getOriginalTable().clearSelectedRowKeys();
  }

  function handleSearchSubmit(data) {
    getOriginalTable().clearSelectedRowKeys();
    let obj = {
      ...allSearchInfo,
      superQueryJson: allSearchInfo.superQueryJson,
      ...data,
    };
    Object.keys(allSearchInfo).map(key => {
      delete allSearchInfo[key];
    });
    for (let [key, value] of Object.entries(obj)) {
      allSearchInfo[key.replaceAll('-', '_')] = value;
    }
    getOriginalTable().reload({ page: 1 });
  }

  const originalTableRef = ref<Nullable<TableActionType>>(null);
  function getOriginalTable() {
    const table = unref(originalTableRef);
    if (!table) throw new Error('table is null!');
    return table;
  }
  const [registerTable, { getForm }] = useTable({
    api: getAllTeacher,
    columns: teacherColumns,
    searchInfo: allSearchInfo,
    clickToRowSelect: false,
    afterFetch: data => {
      setOriginalTableSelectedRowKeys();
      return data;
    },
    immediate: false,
    canResize: true,
    bordered: true,
    showIndexColumn: true,
    // resizeHeightOffset: 100,
    indexColumnProps: {
      width: 50,
    },
    actionColumn: {
      width: 150,
      title: t('component.table.action'),
      dataIndex: 'action',
    },
    rowSelection: {
      type: 'checkbox',
      onSelect: handleSelect,
      onSelectAll: handleSelectAll,
      getCheckboxProps: record => ({
        disabled: record.top,
      }),
    },
  });

  const selectedTableRef = ref<Nullable<TableActionType>>(null);
  function getSelectedTable() {
    const table = unref(selectedTableRef);
    if (!table) throw new Error('table is null!');
    return table;
  }
  const [registerTable2, { getForm: getSelectedForm }] = useTable({
    columns: teacherColumns,
    immediate: false,
    canResize: true,
    bordered: true,
    showIndexColumn: true,
    // resizeHeightOffset: 100,
    indexColumnProps: {
      width: 50,
    },
    showTableSetting: false,
    actionColumn: {
      width: 100,
      title: t('component.table.action'),
      dataIndex: 'action',
      ifShow: !props.disabled,
    },
    pagination: false,
    rowSelection: props.disabled
      ? undefined
      : {
          type: 'checkbox',
        },
  });

  const selectedIds = computed(() => (state.selectedData ?? []).map(o => o.id));
  function initData() {
    state.selectedData = [];
    getSemesterTeachers(props.semesterId).then(res => {
      state.selectedData = res.data;
      getSelectedTable().clearSelectedRowKeys();
      if (!props.disabled) getOriginalTable().reload();
    });
  }

  watch(
    () => state.selectedData,
    () => {
      getSelectedTable().setTableData(state.selectedData);
      if (!props.disabled) setOriginalTableSelectedRowKeys();
    },
    { deep: true },
  );

  function handleSelect(record, selected) {
    if (selected) {
      selectedData.value.push(record);
    } else [(selectedData.value = selectedData.value.filter(o => o.id !== record.id))];
  }

  function handleSelectAll(selected, selectedRows: any[], changeRows) {
    selectedData.value = selectedData.value.filter(o => selectedRows.findIndex(s => s.id === o) !== -1);
    if (selected) {
      selectedData.value.push(...selectedRows);
    }
  }

  function handleSearch(value) {
    allSearchInfo.keyword = value;
    getOriginalTable().reload();
  }
  function removeAll() {
    state.selectedData = [];
    const arr = state.selectedData.map(o => o.id);
    getOriginalTable().setSelectedRowKeys(arr);
  }

  function doSelectHandler(record) {
    state.selectedData.push(record);
  }

  // 取消右边选择
  function removeSelectHandler(record) {
    state.selectedData = state.selectedData.filter(x => x.id !== record.id);
    getSelectedTable().clearSelectedRowKeys();
  }

  function handelBatchRemoveSelected() {
    const selectedIds = getSelectedTable().getSelectRowKeys();
    if (!selectedIds?.length) return createMessage.error(t('common.selectDataTip'));
    state.selectedData = state.selectedData.filter(o => !selectedIds?.includes(o.id));
    getSelectedTable().clearSelectedRowKeys();
  }

  function setOriginalTableSelectedRowKeys() {
    const selectedData = state.selectedData ?? [];
    getOriginalTable().setSelectedRowKeys(selectedData.map(o => o.id));
  }

  function randomHandler() {
    const data = { genNum: 5 };
    randomGenTeacher(data).then(res => {
      console.log(res);
      state.selectedData = res.data;
      getSelectedTable().clearSelectedRowKeys();
    });
  }

  function reloadSelectedTable() {
    state.selectedData = [];
    getSemesterTeachers(props.semesterId).then(res => {
      state.selectedData = res.data;
      getSelectedTable().clearSelectedRowKeys();
    });
  }

  function getSelectedDataIds() {
    return (state.selectedData ?? []).map(o => o.id);
  }

  function handleSubmit(type) {
    const teacherIds = getSelectedDataIds();
    changeLoading(true);
    saveTeacherJson({
      id: props.semesterId,
      teachers: teacherIds,
    })
      .then(res => {
        createMessage.success('考评员配置成功');
        changeLoading(false);
        emit('handleStep', type);
      })
      .catch(err => {
        changeLoading(false);
      });
  }

  defineExpose({
    initData,
    handleSubmit,
  });
</script>

<style lang="less" scoped>
  .dual-table-layout {
    padding: 16px;
    .table-wrapper {
      width: 100%;
      display: flex;
      justify-content: space-between;
    }
    .left-table,
    .right-table {
      width: 50%;
      background: #fff;
      border-radius: 4px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
      padding: 16px;
    }
    .header-wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      .title {
        font-size: 16px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
      }
    }
  }
</style>
