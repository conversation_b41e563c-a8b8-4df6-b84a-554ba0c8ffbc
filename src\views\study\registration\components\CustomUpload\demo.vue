<template>
  <div class="demo-container">
    <h3>自定义上传组件演示</h3>

    <a-card title="基本使用" :bordered="false">
      <CustomUpload
        v-model:value="fileList"
        buttonText="上传文件"
        tipText="支持上传任意类型文件，单个文件大小不超过10MB"
        :fileSize="10"
        sizeUnit="MB"
        @change="handleChange" />
    </a-card>

    <a-card title="自定义接口" :bordered="false" style="margin-top: 16px">
      <CustomUpload
        v-model:value="fileList2"
        buttonText="上传文件(自定义接口)"
        tipText="使用自定义接口上传文件"
        :fileSize="5"
        sizeUnit="MB"
        customUploadUrl="/api/custom/upload"
        customDownloadUrl="/api/custom/download"
        :uploadParams="{ type: 'custom', module: 'registration' }"
        @success="handleSuccess"
        @error="handleError" />
    </a-card>

    <a-card title="仅允许PDF文件" :bordered="false" style="margin-top: 16px">
      <CustomUpload
        v-model:value="pdfFiles"
        accept=".pdf"
        buttonText="上传PDF文件"
        tipText="只能上传PDF文件，单个文件大小不超过5MB"
        :fileSize="5"
        sizeUnit="MB"
        :limit="3" />
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import CustomUpload from './index.vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { customUploadFile, getCustomDownloadUrl } from './index';

  const { createMessage } = useMessage();

  // 文件列表
  const fileList = ref([]);
  const fileList2 = ref([]);
  const pdfFiles = ref([]);

  // 文件变更事件处理
  function handleChange(files) {
    console.log('文件变更:', files);
  }

  // 上传成功事件处理
  function handleSuccess(file) {
    createMessage.success(`文件 ${file.name} 上传成功`);
  }

  // 上传失败事件处理
  function handleError(error) {
    createMessage.error(`上传失败: ${error.message}`);
  }
</script>

<style lang="less" scoped>
  .demo-container {
    padding: 16px;

    h3 {
      margin-bottom: 24px;
    }
  }
</style>
