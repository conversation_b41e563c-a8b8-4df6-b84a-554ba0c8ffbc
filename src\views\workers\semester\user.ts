import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { defHttp } from '@/utils/http/axios';

const { t } = useI18n();

// 基础Api
export const registrationApi = '/api/study/registration';

/**
 * 获取列表
 * @param data
 * @returns
 */
export function getUserList(data) {
  return defHttp.post({ url: registrationApi + `/getList`, data });
}

export function setClassesId(data) {
  return defHttp.post({ url: registrationApi + `/setClassesId`, data });
}

export function generateExaminationAdmissionCard(data) {
  return defHttp.post({ url: registrationApi + `/generateExaminationAdmissionCard`, data });
}

/**
 * 搜索表单配置
 */
export const userSearchSchemas: FormSchema[] = [
  {
    field: 'name',
    label: t('姓名'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
  {
    field: 'area',
    label: t('地区'),
    component: 'AreaSelect',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
  {
    field: 'company',
    label: t('单位'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
  {
    field: 'workType',
    label: t('工种'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
  {
    field: 'level',
    label: t('等级'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
];

/**
 * 表格列配置
 */
export const userColumns: BasicColumn[] = [
  {
    title: t('姓名'),
    dataIndex: 'name',
    width: 120,
  },
];
