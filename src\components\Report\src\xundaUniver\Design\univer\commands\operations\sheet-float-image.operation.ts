import { type ICommand, type IAccessor, CommandType } from '@univerjs/core';
import { XundaSheetsFloatImageService } from '../../services/sheet-float-image.service';
import { XundaCommandIds } from '../../utils/define';

export const XundaSheetsInsertFloatImageOperation: ICommand = {
  id: XundaCommandIds.insertFloatImage,
  type: CommandType.OPERATION,
  handler: async (accessor: IAccessor) => {
    const xundaSheetsFloatImageService = accessor.get(XundaSheetsFloatImageService);
    xundaSheetsFloatImageService.insertFloatImage();

    return true;
  },
};

export const XundaSheetsInsertedFloatImageOperation: ICommand = {
  id: XundaCommandIds.insertedFloatImage,
  type: CommandType.OPERATION,
  handler: async (_: IAccessor) => {
    return true;
  },
};

export const XundaSheetsFocusFloatImageOperation: ICommand = {
  id: XundaCommandIds.focusFloatImage,
  type: CommandType.OPERATION,
  handler: async (_: IAccessor) => {
    return true;
  },
};
