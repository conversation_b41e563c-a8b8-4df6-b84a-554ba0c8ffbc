<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    width="600px"
    :minHeight="100"
    :cancelText="t('common.cancelText', '取消')"
    :okText="t('common.okText', '确定')"
    @ok="handleSubmit"
    :closeFunc="onClose">
    <template #title>
      <a-space :size="10">
        <div class="text-16px font-medium">{{ title }}</div>
        <a-space-compact size="small" block v-if="dataForm.id">
          <a-tooltip :title="t('common.prevRecord')">
            <a-button size="small" :disabled="getPrevDisabled" @click="handlePrev">
              <i class="icon-ym icon-ym-caret-left text-10px"></i>
            </a-button>
          </a-tooltip>
          <a-tooltip :title="t('common.nextRecord')">
            <a-button size="small" :disabled="getNextDisabled" @click="handleNext">
              <i class="icon-ym icon-ym-caret-right text-10px"></i>
            </a-button>
          </a-tooltip>
        </a-space-compact>
      </a-space>
    </template>
    <template #insertFooter>
      <div class="loat-left mt-5px">
        <XundaCheckboxSingle v-model:value="submitType" :label="continueText" />
      </div>
    </template>
    <a-row class="dynamic-form">
      <QuestionFormBase
        v-model="state.dataForm"
        :rules="state.dataRule"
        :showPaperId="true"
        :showScore="true"
        :showQuestionId="true"
        ref="questionFormBaseRef"
      />
    </a-row>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { create, update, getInfo } from '@/views/exam/paperQuestion';
  import { reactive, toRefs, nextTick, ref, unref, computed, inject, watch, defineProps } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useUserStore } from '@/store/modules/user';
  import type { FormInstance } from 'ant-design-vue';
  import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
  // 表单权限
  import { usePermission } from '@/hooks/web/usePermission';
  // 导入题型组件
  import SingleChoiceEditor from '../questionComponent/SingleChoiceEditor.vue'
  import MultipleChoiceEditor from '../questionComponent/MultipleChoiceEditor.vue'
  import TrueFalseEditor from '../questionComponent/TrueFalseEditor.vue'
  import FillBlankEditor from '../questionComponent/FillBlankEditor.vue'
  import ShortAnswerEditor from '../questionComponent/ShortAnswerEditor.vue'
  import QuestionFormBase from '../QuestionFormBase.vue';

  interface State {
    dataForm: any;
    tableRows: any;
    dataRule: any;
    optionsObj: any;
    childIndex: any;
    isEdit: any;
    interfaceRes: any;
    //可选范围默认值
    ableAll: any;
    //掩码配置
    maskConfig: any;
    //定位属性
    locationScope: any;
    title: string;
    continueText: string;
    allList: any[];
    currIndex: number;
    isContinue: boolean;
    submitType: number;
    showContinueBtn: boolean;
  }

  const emit = defineEmits(['reload', 'update:modelValue']);
  const getLeftTreeActiveInfo: (() => any) | null = inject('getLeftTreeActiveInfo', null);
  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const [registerModal, { openModal, setModalProps }] = useModal();
  const formRef = ref<FormInstance>();
  const questionFormBaseRef = ref();
  const state = reactive<State>({
    dataForm: {
      id: '',
      parentId: '',
      paperId: '',
      questionId: '',
      questionTypeId: '',
      no: undefined,
      displayOrder: undefined,
      content: '',
      option: '',
      answer: '',
      score: undefined,
      analysis: '',
      difficulty: undefined,
      knowledgeTag: '',
    },
    tableRows: {},
    dataRule: {
      id: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '主键不能为空'),
          trigger: 'blur',
        },
      ],
      paperId: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '试卷不能为空'),
          trigger: 'blur',
        },
      ],
      questionTypeId: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '题型不能为空'),
          trigger: 'blur',
        },
      ],
      no: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '序号不能为空'),
          trigger: 'blur',
        },
      ],
      content: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '题干不能为空'),
          trigger: 'blur',
        },
      ],
      answer: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '答案不能为空'),
          trigger: 'blur',
        },
      ],
      score: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '分值不能为空'),
          trigger: 'blur',
        },
      ],
    },
    optionsObj: {
      //选项配置
      defaultProps: { label: 'fullName', value: 'enCode' }, // 默认下拉选择键值
    },
    childIndex: -1,
    isEdit: false,
    interfaceRes: {},
    //可选范围默认值
    ableAll: {},

    //掩码配置
    maskConfig: {},
    //定位属性
    locationScope: {
      faddressDetail: [],
    },
    title: '',
    continueText: '',
    allList: [],
    currIndex: 0,
    isContinue: false,
    submitType: 0,
    showContinueBtn: true,
  });
  const { title, continueText, showContinueBtn, dataRule, dataForm, optionsObj, ableAll, maskConfig, submitType } = toRefs(state);

  const getPrevDisabled = computed(() => state.currIndex === 0);
  const getNextDisabled = computed(() => state.currIndex === state.allList.length - 1);
  // 表单权限
  const { hasFormP } = usePermission();

  // 支持外部传入paperId
  const props = defineProps<{ paperId?: string }>();

  // 控制paperId输入框显示
  const showPaperIdInput = computed(() => !props.paperId);

  defineExpose({ init });

  function init(data) {
    state.submitType = 0;
    state.isContinue = false;
    state.title = !data.id ? t('common.add2Text', '新增') : t('common.editText', '编辑');
    state.continueText = !data.id ? t('common.continueAndAddText', '确定并新增') : t('common.continueText', '确定并继续');
    setFormProps({ continueLoading: false });

    if (!data.id) {
      // 新增时先清空数据，不直接Object.assign
      state.dataForm = {
        id: '',
        parentId: '',
        paperId: props.paperId || data.paperId || '',
        questionId: '',
        questionTypeId: 'single', // 确保有默认值
        no: undefined,
        displayOrder: undefined,
        content: '',
        option: '',
        answer: '',
        score: undefined,
        analysis: '',
        difficulty: undefined,
        knowledgeTag: '',
      };
      // 强制触发activeType变化
      activeType.value = '';
      nextTick(() => {
        activeType.value = 'single';
      });
      console.log('paperQuestion init - new form, dataForm:', state.dataForm);
      console.log('paperQuestion init - activeType set to:', activeType.value);
    } else {
      // 编辑时设置id，确保能正确加载数据
      state.dataForm.id = data.id;
      // 优先props.paperId，其次data.paperId
      if (props.paperId) {
        state.dataForm.paperId = props.paperId;
      } else if (data.paperId) {
        state.dataForm.paperId = data.paperId;
      }
    }

    openModal();
    state.allList = data.allList;
    state.currIndex = state.allList.length && data.id ? state.allList.findIndex(item => item.id === data.id) : 0;
    getAllSelectOptions();
    nextTick(() => {
      getForm().resetFields();
      setTimeout(initData, 0);
    });
  }
  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      // 新增时，题型已在init函数中设置，这里只需要处理其他逻辑
      if (getLeftTreeActiveInfo) {
        const info = getLeftTreeActiveInfo() || {};
        // 只合并允许的字段，比如 parentId
        state.dataForm.parentId = info.parentId || '';
      }
      state.childIndex = -1;
      changeLoading(false);
    }
  }
  function getForm() {
    const form = unref(questionFormBaseRef);
    if (!form) {
      throw new Error('form is null!');
    }
    return form;
  }
  function getData(id) {
    getInfo(id).then(res => {
      // 使用Object.assign，不替换dataForm引用
      Object.assign(state.dataForm, res.data || {});
      state.childIndex = -1;
      nextTick(() => {
        if (state.dataForm.questionTypeId) {
          activeType.value = state.dataForm.questionTypeId;
        }
      });
      changeLoading(false);
    });
  }

  // 题型配置
  const questionTypes = [
    { type: 'single', label: '单选题', component: SingleChoiceEditor },
    { type: 'multi', label: '多选题', component: MultipleChoiceEditor },
    { type: 'truefalse', label: '判断题', component: TrueFalseEditor },
    { type: 'fillblank', label: '填空', component: FillBlankEditor },
    { type: 'shortanswer', label: '简答', component: ShortAnswerEditor },
  ]

  const activeType = ref('single')
  const currentEditor = ref()
  const activeComponent = computed(() => {
    const found = questionTypes.find(q => q.type === activeType.value)
    return found ? found.component : null
  })

  // 监听题型变化，自动设置questionTypeId
  watch(activeType, (newType) => {
    if (newType) {
      state.dataForm.questionTypeId = newType
    }
  })

  // 题型切换
  const handleTypeChange = (type) => {
    activeType.value = type
    state.dataForm.option = ''
    state.dataForm.answer = ''
    state.dataForm.questionTypeId = type
  }

  // 提交时合并题目组件数据
  async function handleSubmit(type) {
    try {
      const values = await getForm()?.validate();
      if (!values) return;
      // 从QuestionFormBase组件获取currentEditor
      const currentEditor = questionFormBaseRef.value?.currentEditor;
      if (currentEditor && currentEditor.validate) {
        const childValid = currentEditor.validate();
        if (!childValid) return;
      }
      if (currentEditor && currentEditor.getData) {
        const childData = currentEditor.getData();
        Object.assign(state.dataForm, childData);
      }
      if (!state.dataForm.questionTypeId) {
        state.dataForm.questionTypeId = activeType.value;
      }
      setFormProps({ confirmLoading: true });
      const formMethod = state.dataForm.id ? update : create;
      formMethod(state.dataForm)
        .then(res => {
          createMessage.success(res.msg);
          setFormProps({ confirmLoading: false });
          if (state.submitType == 1 && !state.dataForm.id) {
            initData();
            state.isContinue = true;
          } else {
            setFormProps({ open: false });
            emit('reload');
          }
        })
        .catch(() => {
          setFormProps({ confirmLoading: false });
        });
    } catch (_) {}
  }

  // 跳转上一个
  function handlePrev() {
    state.currIndex--;
    handleGetNewInfo();
  }

  // 跳转下一个
  function handleNext() {
    state.currIndex++;
    handleGetNewInfo();
  }

  // 重新获取编辑信息
  function handleGetNewInfo() {
    changeLoading(true);
    getForm().resetFields();
    const id = state.allList[state.currIndex].id;
    getData(id);
  }

  function setFormProps(data) {
    setModalProps(data);
  }

  //
  function changeLoading(loading) {
    setModalProps({ loading });
  }

  // 关闭弹窗
  async function onClose() {
    if (state.isContinue) emit('reload');
    return true;
  }

  function changeData(model, index) {
    state.isEdit = false;
    state.childIndex = index;
    for (let key in state.interfaceRes) {
      if (key != model) {
        let faceReList = state.interfaceRes[key];
        for (let i = 0; i < faceReList.length; i++) {
          let relationField = faceReList[i].relationField;
          if (relationField) {
            let modelAll = relationField.split('-');
            let faceMode = '';
            let faceMode2 = modelAll.length == 2 ? modelAll[0].substring(0, modelAll[0].length - 4) + modelAll[1] : '';
            for (let i = 0; i < modelAll.length; i++) {
              faceMode += modelAll[i];
            }
            if (faceMode == model || faceMode2 == model) {
              let options = 'get' + key + 'Options';
              eval(options)(true);
              changeData(key, index);
            }
          }
        }
      }
    }
  }
  function getAllSelectOptions() {}

  // 保证id和paperId始终同步到外部（如有v-model）
  watch(() => [state.dataForm.id, state.dataForm.paperId], () => emit('update:modelValue', { ...state.dataForm }));
</script>
