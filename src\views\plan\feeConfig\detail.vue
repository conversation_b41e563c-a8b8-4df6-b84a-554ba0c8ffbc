<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" width="600px" :minHeight="100" :showOkBtn="false">
    <template #insertFooter> </template>
    <!-- 表单 -->
    <a-row class="dynamic-form">
      <a-form :colon="false" size="middle" layout="horizontal" labelAlign="right" :labelCol="{ style: { width: '100px' } }" :model="dataForm" ref="formRef">
        <a-row :gutter="15">
          <!-- 具体表单 -->
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="semesterId">
              <template #label>计划 </template>
              <p>{{ dataForm.semesterId }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="feeType">
              <template #label>费用类型 </template>
              <p>{{ dataForm.feeType }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="feeId">
              <template #label>费用标识 </template>
              <p>{{ dataForm.feeId }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="courseNum">
              <template #label>课程数 </template>
              <p>{{ dataForm.courseNum }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="currency">
              <template #label>币种 </template>
              <p>{{ xundaUtils.optionText(dataForm.currency, optionsObj.Options, optionsObj.defaultProps) }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="price">
              <template #label>原价 </template>
              <p>{{ dataForm.price }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="discount">
              <template #label>优惠金额 </template>
              <p>{{ dataForm.discount }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="discountRate">
              <template #label>折扣率 </template>
              <p>{{ dataForm.discountRate }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="realPrice">
              <template #label>实际价格 </template>
              <p>{{ dataForm.realPrice }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="expireTime">
              <template #label>有效期 </template>
              <p>{{ dataForm.expireTime }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="description">
              <template #label>说明 </template>
              <p>{{ dataForm.description }} </p>
            </a-form-item>
          </a-col>
          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { getDetailInfo } from '@/views/plan/feeConfig';
  import { reactive, toRefs, nextTick, ref } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { usePermission } from '@/hooks/web/usePermission';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
  import { xundaUtils } from '@/utils/xunda';
  interface State {
    dataForm: any;
    title: string;
    optionsObj: any;
  }
  defineOptions({ name: 'Detail' });
  const emit = defineEmits(['reload']);
  const { createMessage, createConfirm } = useMessage();
  const [registerModal, { openModal, setModalProps, closeModal }] = useModal();
  const { t } = useI18n();
  const detailRef = ref<any>(null);
  const visitRecordGridRef = ref<any>(null);
  const state = reactive<State>({
    dataForm: {},
    title: t('common.detailText', '详情'),
    optionsObj: {
      //选项配置
      defaultProps: { label: 'fullName', value: 'enCode' }, // 默认下拉选择键值
    },
  });

  const { title, dataForm, optionsObj } = toRefs(state);
  // 表单权限
  const { hasFormP } = usePermission();

  defineExpose({ init });

  function init(data) {
    getAllSelectOptions();
    state.dataForm.id = data.id;
    openModal();
    nextTick(() => {
      setTimeout(initData, 0);
    });
  }

  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      closeModal();
    }
  }

  function getData(id) {
    getDetailInfo(id).then(res => {
      state.dataForm = res.data || {};
      nextTick(() => {
        changeLoading(false);
      });
    });
  }

  function setFormProps(data) {
    setModalProps(data);
  }

  function changeLoading(loading) {
    setFormProps({ loading });
  }

  function getAllSelectOptions() {}
</script>
