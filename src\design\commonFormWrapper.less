.xunda-common-form-wrapper {
  height: 100%;
  overflow: hidden;
  display: flex;
  .xunda-common-form-wrapper__main {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    position: relative;
  }
}
.form-extra-panel {
  width: 361px;
  height: 100%;
  flex-shrink: 1;
  border-left: 1px solid @border-color-base;
  position: relative;
  display: flex;
  flex-direction: column;
  &.form-extra-panel-unfold {
    width: 0;
    border-left: unset;
    .trigger-btn {
      border-right: unset;
    }
  }
  & > .ant-tabs {
    flex-shrink: 0;
    &.average-tabs-single {
      .ant-tabs-tab {
        &:hover,
        &.ant-tabs-tab-active .ant-tabs-tab-btn {
          color: inherit !important;
        }
      }
      .ant-tabs-ink-bar {
        display: none;
      }
    }
  }
  .trigger-btn {
    width: 20px;
    height: 42px;
    border-radius: 4px 0px 0px 4px;
    box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.1);
    background: @component-background;
    border: 1px solid @border-color-base;
    z-index: 10;
    position: absolute;
    left: -20px;
    top: calc(50% - 21px);
    text-align: center;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .form-extra-panel-main {
    flex: 1;
    overflow: hidden;
  }
  .form-extra-comment {
    height: 100%;
    display: flex;
    flex-direction: column;
    .form-extra-comment-main {
      flex: 1;
      overflow: hidden;
    }
    .form-extra-comment-list {
      padding: 0 20px;
      .form-extra-comment-item {
        padding: 20px 0;
        border-bottom: 1px solid @border-color-base;

        &:last-child {
          border-bottom: unset;
        }
        .form-extra-comment-item-main {
          display: flex;
          .comment-avatar {
            flex-shrink: 0;
            margin-right: 12px;
          }
          .comment-content {
            flex: 1;
            min-width: 0;
            .comment-head {
              display: flex;
              align-items: center;
              justify-content: space-between;
              font-size: 12px;
              line-height: 16px;
              margin-bottom: 10px;
              .username {
                flex: 1;
                min-width: 0;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                .replay-separate {
                  margin: 0 5px;
                  color: @text-color-label;
                }
                i {
                  margin-left: 5px;
                  line-height: 16px;
                  vertical-align: 0;
                  font-size: 14px;
                  color: @text-color-label;
                  cursor: pointer;
                }
              }
              .time {
                flex-shrink: 0;
                color: @text-color-label;
                margin-left: 10px;
              }
            }
            .comment-text {
              font-size: 12px;
              line-height: 24px;
              color: @text-color-label;
              vertical-align: middle;
              word-break: break-all;
              .comment-text-emoji {
                display: inline-block;
                vertical-align: middle;
              }
            }
            .comment-other {
              padding: 8px 4px 2px 4px;
              background-color: #f4f4f4;
              margin-top: 12px;
              border-radius: 4px;
            }
            .comment-img-list {
              .comment-img-item {
                display: inline-block;
                width: 40px;
                height: 40px;
                cursor: pointer;
                overflow: hidden;
                margin: 0 6px 6px 0;
                object-fit: cover;
              }
            }
            .comment-file-List {
              margin-bottom: 8px;
              .anticon-download {
                right: 5px;
              }
              .anticon-eye {
                right: 25px;
              }
            }
            .comment-actions {
              margin-top: 4px;
              display: flex;
              align-items: center;
              justify-content: space-between;

              .ant-btn {
                padding: 0;
                font-size: 12px;
              }
            }
          }
        }
      }
    }
    .form-extra-comment-list-footer {
      flex-shrink: 0;
      border-top: 1px solid @border-color-base;
    }
  }
  .form-extra-log {
    height: 100%;
    .form-extra-log-list {
      padding: 24px 12px 0;
      overflow: auto;
      height: 100%;
      .tag {
        display: block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
      }
      .time-item-container {
        margin-top: 8px;
        padding: 0 10px;
        border-radius: 4px;
        background-color: @app-content-background;
        .time-item-head {
          height: 40px;
          display: flex;
          align-items: center;

          .head-avatar {
            margin-right: 6px;
          }
          .head-name {
            line-height: 24px;
            flex: 1;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .head-status {
            border-radius: 10px;
            padding-inline: 10px;
            margin-inline-end: 0;
          }
        }
        .time-item-log {
          border-top: 1px solid @border-color-base;
          padding: 10px 0 10px 30px;
          .time-item-log-total {
            display: flex;
            margin-bottom: 4px;
            span {
              color: @error-color;
            }
          }
          .time-item-log-item {
            margin-bottom: 8px;
            &:last-child {
              margin-bottom: 0;
            }
            &-label {
              font-weight: 600;
            }
            &-value {
              word-break: break-all;
              color: @text-color-label;
              &-old {
                text-decoration: line-through;
              }
              &-new {
                color: @text-color-base;
              }
              &-more {
                color: @primary-color;
                cursor: pointer;
              }
              &-modify {
                color: @warning-color;
              }
            }
          }
        }
      }
    }
  }
}
.comment-input-wrapper {
  &.comment-input-wrapper-inner {
    border: 1px solid @border-color-base;
    border-radius: 4px;
    &.comment-input-wrapper-active {
      border: 1px solid @primary-color;
    }
    .comment-input-placeholder {
      color: #bfbfbf;
      line-height: 30px;
      height: 96px;
      padding: 0 11px;
    }
  }
  .comment-input-placeholder {
    color: #bfbfbf;
    line-height: 50px;
    padding: 0 10px;
  }
  .comment-input-wrapper-content {
    margin-bottom: 22px;
    textarea {
      border: unset !important;
      box-shadow: unset !important;
    }
  }
  .ant-input-textarea-show-count::after {
    padding-right: 10px;
  }
  .comment-input-wrapper-actions {
    padding: 0 10px;
    height: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .actions-left {
      display: flex;
      align-items: center;
      .ym-custom,
      .icon-ym {
        font-size: 18px;
        margin-right: 15px;
        color: #bfbfbf;
        cursor: pointer;
      }
    }
  }
  .comment-img-list {
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
    padding-left: 10px;
    .img-item {
      width: 40px;
      height: 40px;
      position: relative;
      margin-right: 10px;
      cursor: pointer;
      object-fit: cover;
      flex-shrink: 0;
      margin-bottom: 10px;

      .badge {
        background-color: @error-color;
        border-radius: 10px;
        color: #fff;
        font-size: 12px;
        height: 18px;
        width: 18px;
        line-height: 18px;
        border: 1px solid #fff;
        position: absolute;
        right: -9px;
        top: -9px;
        cursor: pointer;
        z-index: 100;
        display: flex;
        justify-content: center;
        align-items: center;
        .icon-ym {
          font-size: 12px;
          transform: scale(0.7);
          display: inline-block;
        }
      }
    }
  }
  .comment-upload-file {
    padding: 0 10px;
    .anticon-close {
      display: block !important;
    }
    .upload-file-list__item {
      margin-top: 5px !important;
    }
  }
}

.emojiBox {
  height: 150px;
  width: 300px;
  overflow: auto;
  text-align: left;
}
.emojiBox .emoji {
  padding: 0;
}
.emojiBox li {
  display: inline-block;
  width: 28px;
  height: 28px;
  line-height: 28px;
  text-align: center;
  cursor: pointer;
}
.emoji-popover {
  z-index: 30000 !important;
  .ant-popover-inner-content {
    padding: 10px;
  }
}

[data-theme='dark'] {
  .form-extra-panel .form-extra-comment-list .form-extra-comment-item .form-extra-comment-item-main .comment-content .comment-other {
    background-color: #333 !important;
  }
  .comment-input-wrapper {
    .comment-input-placeholder {
      color: rgba(255, 255, 255, 0.3);
    }
    .comment-input-wrapper-actions {
      .actions-left {
        .ym-custom,
        .icon-ym {
          color: rgba(255, 255, 255, 0.3);
        }
      }
    }
  }
}
