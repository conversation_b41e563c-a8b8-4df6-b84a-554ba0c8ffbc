<template>
  <a-row class="dynamic-form">
    <a-form
      :colon="false"
      size="middle"
      layout="horizontal"
      labelAlign="left"
      :labelCol="{ style: { width: '100px' } }"
      :model="dataForm"
      :rules="dataRule"
      ref="formRef">
      <a-row :gutter="15">
        <!-- 具体表单 -->
        <a-col :span="24" class="ant-col-item" :hidden="false">
          <a-form-item name="registrationEntrance">
            <template #label>正式报名入口 </template>
            <XundaInput
              v-if="!disabled"
              v-model:value="dataForm.registrationEntrance"
              :disabled="false"
              placeholder="请输入"
              :allowClear="true"
              :style="{ width: '100%' }"
              :readOnly="disabled">
            </XundaInput>
            <div v-else>{{ dataForm.registrationEntrance }}</div>
          </a-form-item>
        </a-col>
        <a-col :span="24" class="ant-col-item" :hidden="false">
          <a-form-item name="attachment">
            <template #label>资料 </template>
            <XundaUploadFile
              v-if="!disabled"
              v-model:value="dataForm.attachment"
              :fileSize="10"
              sizeUnit="MB"
              :limit="9"
              pathType="selfPath"
              :sortRule="[3]"
              timeFormat="YYYY"
              folder="fileFiled"
              buttonText="点击上传"
              :showUploadList="false"
              :showDownload="false"
              :detailed="disabled"
              :disabled="disabled">
            </XundaUploadFile>
          </a-form-item>
        </a-col>
        <!-- 表单结束 -->
      </a-row>
    </a-form>
  </a-row>

  <div class="file-list-container">
    <div v-if="fileList && fileList.length > 0" class="file-list">
      <div v-for="(file, index) in fileList" :key="index" class="file-item">
        <Bit_KKFileView :file="file" operationMode="icon" :previewMode="false" :allowDelete="!disabled" @delete="handleDeleteFile" />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { getAdultHighEducationInfoAsync, setAdultHighEducationInfoAsync } from './index';
  import { reactive, toRefs, nextTick, ref, unref, computed, inject } from 'vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useUserStore } from '@/store/modules/user';
  import type { FormInstance } from 'ant-design-vue';
  import Bit_KKFileView from '@/components/Bit/kkFileView';

  interface State {
    dataForm: any;
    tableRows: any;
    dataRule: any;
    optionsObj: any;
    childIndex: any;
    isEdit: any;
    interfaceRes: any;
    //可选范围默认值
    ableAll: any;
    //掩码配置
    maskConfig: any;
    //定位属性
    locationScope: any;
    title: string;
    continueText: string;
    allList: any[];
    currIndex: number;
    isContinue: boolean;
    submitType: number;
    showContinueBtn: boolean;
  }

  const props = defineProps({
    semesterId: { type: String, default: '' },
    disabled: { type: Boolean, default: false },
    componentConfig: { type: Object, default: null },
  });
  // 定义组件的 emits
  const emit = defineEmits(['changeLoading', 'handleStep']);
  const changeLoading = loading => {
    emit('changeLoading', loading);
  };

  const userStore = useUserStore();
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const formRef = ref<FormInstance>();
  function getForm() {
    const form = unref(formRef);
    if (!form) {
      throw new Error('form is null!');
    }
    return form;
  }
  const state = reactive<State>({
    dataForm: {
      id: undefined,
      semesterId: undefined,
      title: '',
      content: '',
      attachment: '',
    },
    tableRows: {},
    dataRule: {
      registrationEntrance: [
        {
          required: 'true',
        },
      ],
    },
    optionsObj: {
      //选项配置
      semesterIdOptions: [],
      semesterIdProps: {
        label: 'name',
        value: 'id',
      },
      defaultProps: { label: 'fullName', value: 'enCode' }, // 默认下拉选择键值
    },
    childIndex: -1,
    isEdit: false,
    interfaceRes: {
      id: [],
      semesterId: [],
      title: [],
      content: [],
      attachment: [],
    },
    //可选范围默认值
    ableAll: {},

    //掩码配置
    maskConfig: {},
    //定位属性
    locationScope: {
      faddressDetail: [],
    },
    title: '',
    continueText: '',
    allList: [],
    currIndex: 0,
    isContinue: false,
    submitType: 0,
    showContinueBtn: true,
  });
  const { dataRule, dataForm } = toRefs(state);
  const fileList = computed(() => {
    return dataForm.value.attachment;
  });

  function init() {
    state.submitType = 0;
    nextTick(() => {
      getForm().resetFields();
      setTimeout(initData, 0);
    });
  }
  function initData() {
    changeLoading(true);
    getAdultHighEducationInfoAsync(props.semesterId, props.componentConfig?.otherModel.type).then(res => {
      state.dataForm = res.data || {};
      if (res.data.attachment) {
        state.dataForm.attachment = JSON.parse(res.data.attachment);
      }
      console.log(state.dataForm);
      changeLoading(false);
    });
  }

  async function handleSubmit(type) {
    try {
      const values = await getForm()?.validate();
      if (!values) return;
      changeLoading(true);
      var query = { ...state.dataForm };
      if (query.attachment) {
        query.attachment = JSON.stringify(query.attachment);
      }
      query.type = props.componentConfig?.otherModel.type;
      console.log(state.dataForm);
      const formMethod = setAdultHighEducationInfoAsync;
      query.semesterId = props.semesterId;
      formMethod(query)
        .then(res => {
          createMessage.success(props.componentConfig?.name + '配置成功');
          changeLoading(false);
          emit('handleStep', type);
        })
        .catch(() => {
          changeLoading(false);
        });
    } catch (_) {
      if (type == 'prev') emit('handleStep', type);
      changeLoading(false);
    }
  }

  function handleDeleteFile(file) {
    state.dataForm.attachment = state.dataForm.attachment.filter(item => item.fileId !== file.fileId);
  }

  defineExpose({
    init,
    handleSubmit,
  });
</script>

<style lang="less" scoped>
  .ant-col-item {
    margin-bottom: 8px;
  }

  .generate-notice-btn {
    margin-bottom: 10px;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
  }

  .flex {
    display: flex;
  }

  .justify-end {
    justify-content: flex-end;
  }

  .justify-start {
    justify-content: flex-start;
  }

  .mb-2 {
    margin-bottom: 0.5rem;
  }

  /* 附件列表样式 */
  .attachment-list {
    margin-top: 20px;
    margin-bottom: 20px;
  }

  .attachment-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 16px;
    padding-left: 8px;
    border-left: 3px solid #1890ff;
  }

  .attachment-item {
    margin-bottom: 16px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    overflow: hidden;
  }
  .file-list-container {
    margin-top: 20px;

    .file-list {
      display: flex;
      flex-direction: column;
      gap: 16px;
      padding: 10px 0;
    }

    .file-item {
      background: white;
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;
      width: 100%;
      display: flex;
      flex-direction: column;

      /* 移除鼠标悬停效果 */

      .file-info {
        padding: 12px 15px;
        border-top: 1px solid #f0f0f0;

        .file-name {
          font-weight: 500;
          margin-bottom: 5px;
          color: #333;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 100%;
        }

        .file-size {
          font-size: 12px;
          color: #888;
        }
      }
    }
  }
</style>
