import { type Dependency, UniverInstanceType, Plugin, Inject, Injector, DependentOn, LocaleService } from '@univerjs/core';
import { UniverSheetsUIPlugin } from '@univerjs/sheets-ui';
import { XundaSheetsFloatEchartController } from '../controllers/sheet-float-echart.controller';
import { XundaSheetsFloatEchartService } from '../services/sheet-float-echart.service';
import zhCN from '../locales/zh-CN';

@DependentOn(UniverSheetsUIPlugin)
export class XundaSheetsFloatEchartPlugin extends Plugin {
  static override pluginName = 'XUNDA_SHEET_FLOAT_ECHART_PLUGIN';
  static override type = UniverInstanceType.UNIVER_SHEET;

  constructor(
    @Inject(Injector) protected readonly _injector: Injector,
    @Inject(LocaleService) private readonly _localeService: LocaleService,
  ) {
    super();

    this._localeService.load({
      zhCN,
    });
  }

  override onStarting(): void {
    ([[XundaSheetsFloatEchartController], [XundaSheetsFloatEchartService]] as Dependency[]).forEach(d => this._injector.add(d));

    this._injector.get(XundaSheetsFloatEchartController);
  }
}
