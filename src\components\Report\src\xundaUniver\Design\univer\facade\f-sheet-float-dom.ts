import { XundaSheetsFloatDomService } from '../services/sheet-float-dom.service';

export class XundaFacadeSheetsFloatDom {
  constructor(private readonly _xundaSheetsFloatDomService: XundaSheetsFloatDomService) {}

  savePiniaStoreId(value: any) {
    this._xundaSheetsFloatDomService.savePiniaStoreId(value);
  }

  saveFloatEchartItems(data: any) {
    this._xundaSheetsFloatDomService.saveFloatEchartItems(data);
  }

  saveFloatImageItems(data: any) {
    this._xundaSheetsFloatDomService.saveFloatImageItems(data);
  }
}
