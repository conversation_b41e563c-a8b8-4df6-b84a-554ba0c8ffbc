import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';

// 基础Api
export const eaCourseApi = '/api/ea/course';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: eaCourseApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: eaCourseApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: eaCourseApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: eaCourseApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: eaCourseApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: eaCourseApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: eaCourseApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: eaCourseApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: eaCourseApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: eaCourseApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: eaCourseApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: eaCourseApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: eaCourseApi + `/getForSelect`, data });
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'courseType',
    label: t('课程类型'),
    component: 'TreeSelect',
    colProps: { span: 24 },
    componentProps: {
      submitOnPressEnter: true,
    },
  },
  {
    field: 'no',
    label: t('课程号'),
    component: 'Input',
    colProps: { span: 24 },
    componentProps: {
      submitOnPressEnter: true,
    },
  },
  {
    field: 'name',
    label: t('课程名(中)'),
    component: 'Input',
    colProps: { span: 24 },
    componentProps: {
      submitOnPressEnter: true,
    },
  },
];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('课程名称'),
    dataIndex: 'name',
    width: 120,
  },
  {
    title: t('课程号'),
    dataIndex: 'no',
    width: 120,
  },
];
