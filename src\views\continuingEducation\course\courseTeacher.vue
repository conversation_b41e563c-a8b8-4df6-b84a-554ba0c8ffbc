<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" destroyOnClose class="course-teacher-popup">
    <template #title>
      <span class="popup-title">授课老师安排</span>
    </template>

    <template #centerToolbar>
      <a-space :size="10"> </a-space>
    </template>

    <div class="xunda-content-wrapper course-teacher-wrapper">
      <div class="xunda-content-wrapper-center">
        <div class="course-teacher-header">
          <div class="course-info-card">
            <span class="label">课程：</span>
            <span class="course-name">{{ courseName }}</span>
          </div>
        </div>
        
        <div class="xunda-content-wrapper-search-box">
          <BasicForm 
            @register="registerSearchForm" 
            :schemas="userSearchSchemas" 
            @submit="handleSearchSubmit" 
            @reset="handleSearchReset" 
            class="search-form">
          </BasicForm>
        </div>
        
        <div class="xunda-content-wrapper-content bg-white section-card course-teacher-content">
          <BasicTable @register="registerTable" ref="tableRef">
            <template #tableTitle>
              <div class="table-title-section">
                <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="handleAdd()">
                  {{ t('添加') }}
                </a-button>
                <a-button type="primary" danger preIcon="icon-ym icon-ym-btn-delete" @click="handelBatchRemove()">
                  {{ t('批量删除') }}
                </a-button>
              </div>
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'role'">
                <p class="role-text">{{ xundaUtils.optionText(record.role, optionsObj.roleOptions, optionsObj.roleFieldNames) }}</p>
              </template>
              <template v-if="column.key === 'action'">
                <TableAction
                  :actions="[
                    {
                      label: t('删除'),
                      color: 'error',
                      modelConfirm: {
                        onOk: handleDelete.bind(null, record.id),
                      },
                    },
                  ]" />
              </template>
            </template>
          </BasicTable>
        </div>
      </div>
      <Form ref="formRef" @reload="reload" />
    </div>
  </BasicPopup>
</template>

<script lang="ts" setup>
  // 基础组件
  import { BasicPopup, usePopup } from '@/components/Popup';
  import { BasicForm, useForm } from '@/components/Form';
  import { BasicTable, useTable, TableAction, TableActionType } from '@/components/Table';

  // 工具函数
  import { ref, reactive, onMounted, nextTick, shallowRef } from 'vue';
  import { debounce } from 'lodash-es';
  import { useRoute } from 'vue-router';
  import { cloneDeep } from 'lodash-es';

  // 业务逻辑
  import { getUserList, batchDelete, userColumns, userSearchSchemas } from './courseTeacher';
  import Form from '@/views/ea/courseTeacher/form.vue';

  // 钩子函数
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { usePermission } from '@/hooks/web/usePermission';

  // API
  import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
  import { xundaUtils } from '@/utils/xunda';

  // 类型定义
  interface CourseTeacherOptions {
    roleFieldNames: { label: string; value: string };
    roleOptions: any[];
  }

  interface CourseTeacherData {
    id: string;
    courseId: string;
    menuId: string;
    allList: Array<Record<string, any>>;
  }

  const [registerPopup, { openPopup }] = usePopup();
  const emit = defineEmits(['reload']);
  defineExpose({ init });
  const formRef = ref<any>(null);

  // 工具钩子
  const { hasFormP } = usePermission();
  const route = useRoute();
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();

  // 表格引用
  const tableRef = ref<TableActionType | null>(null);
  const tableRightRef = ref<TableActionType | null>(null);

  // 业务数据
  const cacheList = shallowRef<Array<Record<string, any>>>([]);
  const courseId = ref<string>('');
  const courseName = ref<string>('');
  const optionsObj = ref<CourseTeacherOptions>({
    roleFieldNames: { label: 'fullName', value: 'enCode' },
    roleOptions: [],
  });
  const getDefaultSearchInfo = () => ({
    menuId: route.meta.modelId as string,
    moduleId: '***********', //模块ID
    superQueryJson: '',
    dataType: 0,
    courseId: courseId.value,
  });

  const searchInfo = reactive(getDefaultSearchInfo());

  const [registerSearchForm] = useForm({
    baseColProps: { span: 8 },
    showActionButtonGroup: true,
    showAdvancedButton: true,
    autoAdvancedLine: 2,
    compact: true,
  });
  const tableGetList = async (params: any): Promise<any> => {
    if (!courseId.value) return [];
    return await getUserList({
      ...params,
      courseId: courseId.value,
    });
  };
  const [registerTable, { reload }] = useTable({
    api: tableGetList,
    columns: userColumns,
    immediate: false,
    searchInfo: searchInfo,
    clickToRowSelect: false,
    afterFetch: data => {
      const list = data.map(o => ({
        ...o,
      }));
      cacheList.value = cloneDeep(list);
      return list;
    },
    actionColumn: {
      width: 80,
      title: t('common.actionText', '操作'),
      dataIndex: 'action',
      fixed: 'right',
    },
    rowSelection: {
      type: 'checkbox',
      getCheckboxProps: record => ({
        disabled: record.top,
      }),
    },
  });

  function handleSearchReset() {
    tableRef.value?.clearSelectedRowKeys();
  }

  const handleSearchSubmit = debounce((data: Record<string, any>) => {
    const newSearchInfo = {
      ...getDefaultSearchInfo(),
      superQueryJson: searchInfo.superQueryJson,
      ...data,
    };

    // 清除现有搜索信息
    Object.keys(searchInfo).forEach(key => {
      delete searchInfo[key];
    });

    // 设置新的搜索信息
    Object.entries(newSearchInfo).forEach(([key, value]) => {
      searchInfo[key.replaceAll('-', '_')] = value;
    });

    tableRef.value?.reload({ page: 1 });
  }, 300);

  function initAll() {
    tableRef.value?.clearSelectedRowKeys();
    tableRightRef.value?.clearSelectedRowKeys();
    tableRef.value?.reload({ page: 1 });
    tableRightRef.value?.reload({ page: 1 });
  }

  function handleAdd() {
    const data = {
      id: '',
      courseId: courseId.value,
      menuId: searchInfo.menuId,
      allList: cacheList.value,
    };
    formRef.value?.init(data);
  }

  // 编辑
  function handleEdit(record) {
    // 不带工作流
    const data = {
      id: record.id,
      menuId: searchInfo.menuId,
      allList: cacheList.value,
    };
    formRef.value?.init(data);
  }

  // 删除
  async function handleDelete(id: string) {
    try {
      const query = { ids: [id] };
      const res = await batchDelete(query);
      createMessage.success(res.msg);
      initAll();
    } catch (error) {
      createMessage.error('删除失败');
      console.error('Delete error:', error);
    }
  }
  async function handelBatchRemove() {
    const ids = tableRef.value?.getSelectRowKeys() ?? [];
    if (!ids.length) return createMessage.error('请选择一条数据');

    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要删除选中的记录吗?',
      onOk: async () => {
        try {
          tableRef.value?.setLoading(true);
          const query = { ids };
          const res = await batchDelete(query);
          createMessage.success(res.msg);
          initAll();
        } catch (error) {
          createMessage.error('批量删除失败');
          console.error('Batch delete error:', error);
        } finally {
          tableRef.value?.setLoading(false);
        }
      },
    });
  }

  function init(data) {
    courseId.value = data.id;
    courseName.value = data.name;
    openPopup();
    nextTick(() => {
      initAll();
    });
  }

  // 设置查询表单
  async function setSearchSchema() {
    try {
      optionsObj.value.roleFieldNames = { label: 'fullName', value: 'enCode' };
      optionsObj.value.roleOptions = [];
      const res = await getDictionaryDataSelector('CourseTeacherRole');
      optionsObj.value.roleOptions = res.data.list;
    } catch (error) {
      createMessage.error('获取角色选项失败');
      console.error('Get role options error:', error);
    }
  }

  onMounted(() => {
    setSearchSchema();
  });
</script>

<style lang="less" scoped>
.course-teacher-popup {
  :deep(.ant-modal-content) {
    border-radius: 8px;
    overflow: hidden;
  }
  
  :deep(.ant-modal-header) {
    background: linear-gradient(90deg, #4e73df 0%, #224abe 100%);
    border: none;
    padding: 16px 24px;
  }
  
  :deep(.ant-modal-title) {
    color: #fff;
    font-size: 18px;
    font-weight: 600;
  }
  
  :deep(.ant-modal-close) {
    color: #fff;
    top: 10px;
  }
}

.course-teacher-wrapper {
  padding: 0;
  
  .course-teacher-header {
    padding: 16px 24px;
    background: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    
    .course-info-card {
      display: inline-flex;
      align-items: center;
      padding: 10px 20px;
      background: #fff;
      border-radius: 6px;
      box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
      
      .label {
        font-weight: 600;
        color: #4e73df;
        font-size: 14px;
      }
      
      .course-name {
        font-weight: 600;
        color: #5a5c69;
        font-size: 16px;
      }
    }
  }
  
  :deep(.xunda-content-wrapper-search-box) {
    padding: 20px 24px;
    background: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
  }
  
  .course-teacher-content {
    margin: 24px;
    border-radius: 8px;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    overflow: hidden;
    
    :deep(.ant-table-wrapper) {
      padding: 0;
    }
  }
  
  :deep(.table-title-section) {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    
    .ant-btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }
  }
  
  :deep(.role-text) {
    margin: 0;
    padding: 4px 8px;
    background-color: #eaecf4;
    border-radius: 4px;
    color: #5a5c69;
    font-weight: 500;
    display: inline-block;
  }
}

.popup-title {
  font-weight: 600;
}
</style>