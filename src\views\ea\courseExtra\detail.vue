<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" width="600px" :minHeight="100" :showOkBtn="false">
    <template #insertFooter> </template>
    <!-- 表单 -->
    <a-row class="dynamic-form">
      <a-form :colon="false" size="middle" layout="horizontal" labelAlign="right" :labelCol="{ style: { width: '100px' } }" :model="dataForm" ref="formRef">
        <a-row :gutter="15">
          <!-- 具体表单 -->
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="id">
              <template #label>主键 </template>
              <XundaInput v-model:value="dataForm.id" placeholder="请输入" disabled detailed allowClear :style="{ width: '100%' }"> </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="courseIdSelf">
              <template #label>课程主键 </template>
              <p>{{ xundaUtils.optionText(dataForm.courseIdSelf, optionsObj.courseIdSelfOptions, optionsObj.courseIdSelfFieldNames) }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="extraType">
              <template #label>类型 </template>
              <p>{{ xundaUtils.optionText(dataForm.extraType, optionsObj.extraTypeOptions, optionsObj.extraTypeFieldNames) }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="courseIdExtra">
              <template #label>前置/绑定课程主键 </template>
              <p>{{ xundaUtils.optionText(dataForm.courseIdExtra, optionsObj.courseIdExtraOptions, optionsObj.courseIdExtraFieldNames) }}</p>
            </a-form-item>
          </a-col>
          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { getDetailInfo } from '@/views/ea/courseExtra';
  import { reactive, toRefs, nextTick, ref } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { usePermission } from '@/hooks/web/usePermission';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
  import { xundaUtils } from '@/utils/xunda';
  interface State {
    dataForm: any;
    title: string;
    maskConfig: any;
    optionsObj: any;
  }
  defineOptions({ name: 'Detail' });
  const emit = defineEmits(['reload']);
  const { createMessage, createConfirm } = useMessage();
  const [registerModal, { openModal, setModalProps, closeModal }] = useModal();
  const { t } = useI18n();
  const detailRef = ref<any>(null);
  const visitRecordGridRef = ref<any>(null);
  const state = reactive<State>({
    dataForm: {},
    title: t('common.detailText', '详情'),
    optionsObj: {
      //选项配置
      defaultProps: { label: 'fullName', value: 'enCode' }, // 默认下拉选择键值
    },
  });

  const { title, dataForm, optionsObj, maskConfig } = toRefs(state);
  // 表单权限
  const { hasFormP } = usePermission();

  defineExpose({ init });

  function init(data) {
    getAllSelectOptions();
    state.dataForm.id = data.id;
    openModal();
    nextTick(() => {
      setTimeout(initData, 0);
    });
  }

  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      closeModal();
    }
  }

  function getData(id) {
    getDetailInfo(id).then(res => {
      state.dataForm = res.data || {};
      nextTick(() => {
        changeLoading(false);
      });
    });
  }

  function setFormProps(data) {
    setModalProps(data);
  }

  function changeLoading(loading) {
    setFormProps({ loading });
  }

  function getAllSelectOptions() {
    state.optionsObj.courseIdSelfFieldNames = { label: 'name', value: 'id' };
    state.optionsObj.courseIdSelfOptions = [];
    state.optionsObj.extraTypeFieldNames = { label: 'fullName', value: 'enCode' };
    state.optionsObj.extraTypeOptions = [];
    optionsObj.value.extraTypeOptions = [
      { fullName: '前置', enCode: 'pre' },
      { fullName: '绑定', enCode: 'bind' },
    ];
    state.optionsObj.courseIdExtraFieldNames = { label: 'name', value: 'id' };
    state.optionsObj.courseIdExtraOptions = [];
  }
</script>
