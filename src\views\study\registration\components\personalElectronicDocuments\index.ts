/**
 * 个人电子文档模块API
 * 包含学生注册电子文件的查询、上传和删除等相关功能
 */

import { useI18n } from '@/hooks/web/useI18n';
import { defHttp } from '@/utils/http/axios';

// 国际化
const { t } = useI18n();

/**
 * 注册模块基础API路径
 */
export const registrationApi = '/api/study/registration';

/**
 * 获取注册信息(原始数据，未经转换)
 * @param {string } id - 注册记录ID
 * @returns {Promise<any>} 返回注册信息
 */
export function getInfo(id: string) {
  return defHttp.get({ url: `${registrationApi}/${id}` });
}

/**
 * 获取注册详细信息(经过转换的数据)
 * @param {string } id - 注册记录ID
 * @returns {Promise<any>} 返回转换后的详细信息
 */
export function getDetailInfo(id: string) {
  return defHttp.get({ url: `${registrationApi}/detail/${id}` });
}

/**
 * 获取指定类型的图片文件
 * @param {object} params - 请求参数
 * @param {string } params.id - 注册记录ID
 * @param {string} params.type - 图片类型
 * @returns {Promise<any>} 返回图片文件信息
 */
export function getImageFileAsync({ id, type }: { id: string; type: string | number }) {
  return defHttp.get({ url: `${registrationApi}/getImageFile/${id}/${type}` });
}

/**
 * 获取个人文件
 * @param {object} params - 请求参数
 * @param {string } params.id - 注册记录ID
 * @returns {Promise<any>} 返回个人文件信息
 */
export function getFileAsync({ id }: { id: string }) {
  return defHttp.post({ url: `${registrationApi}/getPersonalFile/${id}` });
}

/**
 * 上传个人文件
 * @param {any} data - 上传的文件数据
 * @returns {Promise<any>} 返回上传结果
 */
export function uploadFileAsync(data: any) {
  return defHttp.post({ url: `${registrationApi}/uploadPersonalFile`, data });
}

/**
 * 删除附件
 * @param {object} data - 删除参数
 * @returns {Promise<any>} 返回删除结果
 */
export function deleteAttachmentAsync(data: { id: string; fileId: string }) {
  return defHttp.post({ url: `${registrationApi}/deleteAttachment`, data });
}
