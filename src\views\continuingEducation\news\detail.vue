<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="title"
    defaultFullscreen
    width="100%"
    :height="1000"
    wrapClassName="news-detail-modal"
    :showOkBtn="false">
    <template #insertFooter> </template>

    <div class="news-detail-container">
      <div class="news-content-wrapper">
        <!-- 标题区域 -->
        <div class="news-header">
          <h1 class="news-title">{{ dataForm.title }}</h1>
          <div class="news-meta" v-if="dataForm.semesterName">
            <span class="news-semester">
              <CalendarOutlined />
              {{ dataForm.semesterName }}
            </span>
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="news-content" v-html="dataForm.content"></div>

        <!-- 附件区域 -->
        <div class="news-attachment" v-if="dataForm.attachment">
          <div class="attachment-label">
            <PaperClipOutlined />
            附件:
          </div>
          <div class="attachment-content">
            <FileOutlined />
            <span>{{ dataForm.attachment }}</span>
          </div>
        </div>
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { getDetailInfo } from './index';
  import { reactive, toRefs, nextTick, ref } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { usePermission } from '@/hooks/web/usePermission';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { CalendarOutlined, PaperClipOutlined, FileOutlined } from '@ant-design/icons-vue';
  interface State {
    dataForm: any;
    title: string;
    optionsObj: any;
  }

  defineOptions({ name: 'Detail' });
  const planNameList = ref<any>([]);
  const emit = defineEmits(['reload']);
  useMessage();
  const [registerModal, { openModal, setModalProps, closeModal }] = useModal();

  const { t } = useI18n();
  const detailRef = ref<any>(null);
  const visitRecordGridRef = ref<any>(null);
  const state = reactive<State>({
    dataForm: {
      id: '',
      type: '',
      semesterId: '',
      title: '',
      state: null,
      content: '',
      attachment: '',
      semesterName: '',
    },
    title: t('common.detailText', '新闻详情'),
    optionsObj: {
      //选项配置
      defaultProps: { label: 'fullName', value: 'enCode', children: 'children' }, // 默认下拉选择键值
    },
  });

  const { title, dataForm, optionsObj } = toRefs(state);
  // 表单权限
  const { hasFormP } = usePermission();

  defineExpose({ init });

  function init(data) {
    getAllSelectOptions();
    state.dataForm.id = data.id;
    openModal();
    if (data.isPreview) {
      state.dataForm = data;
      return;
    }
    nextTick(() => {
      setTimeout(initData, 0);
    });
  }

  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      closeModal();
    }
  }

  function getData(id) {
    getDetailInfo(id).then(res => {
      if (res.data) {
        state.dataForm = { ...state.dataForm, ...res.data };
      }
      nextTick(() => {
        changeLoading(false);
      });
    });
  }

  function setFormProps(data) {
    setModalProps(data);
  }

  function changeLoading(loading) {
    setFormProps({ loading });
  }

  function getAllSelectOptions() {}
</script>

<style lang="less" scoped>
  .news-detail-modal {
    :deep(.ant-modal) {
      max-width: 100%;
      padding: 0;
      margin: 0;
    }

    :deep(.ant-modal-content) {
      height: 100vh;
      border-radius: 0;
      display: flex;
      flex-direction: column;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    :deep(.ant-modal-body) {
      flex: 1;
      padding: 0;
      overflow: auto;
    }

    :deep(.ant-modal-header) {
      background: #fff;
      padding: 16px 24px;
      border-bottom: 1px solid #e8e8e8;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      z-index: 10;
    }

    :deep(.ant-modal-title) {
      font-size: 20px;
      font-weight: 600;
      color: #262626;
    }

    :deep(.ant-modal-close) {
      top: 10px;
      right: 16px;
    }
  }

  .news-detail-container {
    min-height: 100%;
    background: #f5f5f5;
    padding: 0;

    .news-content-wrapper {
      width: 100%;
      max-width: 800px;
      margin: 32px auto;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      overflow: hidden;

      .news-header {
        padding: 32px;
        background: linear-gradient(120deg, #f0f8ff, #e6f7ff);
        border-bottom: 1px solid #e8e8e8;

        .news-title {
          font-size: 28px;
          font-weight: 700;
          color: #262626;
          margin-bottom: 16px;
          line-height: 1.4;
          text-align: center;
        }

        .news-meta {
          display: flex;
          justify-content: center;
          color: #595959;

          .news-semester {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            padding: 4px 12px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 16px;
          }
        }
      }

      .news-content {
        padding: 32px;
        font-size: 16px;
        line-height: 1.8;
        color: #262626;
        background: #fff;

        // 基础样式优化
        :deep(p) {
          margin-bottom: 16px;
          font-size: 16px;
          line-height: 1.8;
        }

        :deep(h1) {
          font-size: 28px;
          margin: 32px 0 24px 0;
          font-weight: 700;
          color: #1890ff;
          padding-bottom: 12px;
          border-bottom: 2px solid #e8e8e8;
        }

        :deep(h2) {
          font-size: 24px;
          margin: 28px 0 20px 0;
          font-weight: 600;
          color: #1890ff;
          padding-bottom: 8px;
          border-bottom: 1px solid #f0f0f0;
        }

        :deep(h3) {
          font-size: 20px;
          margin: 24px 0 16px 0;
          font-weight: 600;
          color: #262626;
        }

        :deep(h4),
        :deep(h5),
        :deep(h6) {
          font-size: 18px;
          margin: 20px 0 12px 0;
          font-weight: 500;
          color: #595959;
        }

        :deep(img) {
          max-width: 100%;
          height: auto;
          margin: 24px auto;
          display: block;
          border-radius: 4px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        :deep(ul),
        :deep(ol) {
          padding-left: 24px;
          margin-bottom: 16px;
        }

        :deep(li) {
          margin-bottom: 8px;
        }

        :deep(a) {
          color: #1890ff;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }

        :deep(blockquote) {
          margin: 24px 0;
          padding: 16px 24px;
          border-left: 4px solid #1890ff;
          background-color: #f8f8f8;
          border-radius: 0 4px 4px 0;
          font-style: italic;
        }

        :deep(table) {
          width: 100%;
          border-collapse: collapse;
          margin: 24px 0;
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
          border-radius: 4px;
          overflow: hidden;

          th,
          td {
            border: 1px solid #d9d9d9;
            padding: 12px 16px;
            text-align: left;
          }

          th {
            background-color: #fafafa;
            font-weight: 600;
            color: #595959;
          }

          tr:nth-child(even) {
            background-color: #fbfbfb;
          }

          tr:hover {
            background-color: #f0f7ff;
          }
        }

        :deep(code) {
          background-color: #f5f5f5;
          padding: 2px 6px;
          border-radius: 3px;
          font-family: monospace;
          font-size: 14px;
        }

        :deep(pre) {
          background-color: #2d2d2d;
          color: #f8f8f2;
          padding: 16px;
          border-radius: 4px;
          overflow-x: auto;
          margin: 24px 0;
          font-size: 14px;
        }
      }

      .news-attachment {
        padding: 32px;
        margin-top: 0;
        background: #fafafa;
        border-top: 1px solid #e8e8e8;

        .attachment-label {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 18px;
          font-weight: 600;
          color: #262626;
          margin-bottom: 16px;
        }

        .attachment-content {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 16px 20px;
          background-color: #fff;
          border-radius: 6px;
          color: #595959;
          font-size: 15px;
          border: 1px dashed #d9d9d9;
          transition: all 0.3s;

          &:hover {
            border-color: #1890ff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .news-detail-container {
      padding: 0;
      background: #fff;

      .news-content-wrapper {
        margin: 0;
        border-radius: 0;
        box-shadow: none;

        .news-header {
          padding: 24px 16px;

          .news-title {
            font-size: 24px;
          }
        }

        .news-content {
          padding: 24px 16px;
          font-size: 15px;

          :deep(h1) {
            font-size: 24px;
          }

          :deep(h2) {
            font-size: 20px;
          }

          :deep(h3) {
            font-size: 18px;
          }
        }

        .news-attachment {
          padding: 24px 16px;
        }
      }
    }
  }
</style>
