import { FormSchema } from "@/components/Form";
import { useI18n } from "@/hooks/web/useI18n";
import { BasicColumn } from "@/components/Table/src/types/table";
import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from "@/utils/xunda";

// 基础Api
export const planTemplateConfigApi = '/api/plan/templateConfig';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: planTemplateConfigApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: planTemplateConfigApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: planTemplateConfigApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: planTemplateConfigApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: planTemplateConfigApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: planTemplateConfigApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: planTemplateConfigApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: planTemplateConfigApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: planTemplateConfigApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: planTemplateConfigApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: planTemplateConfigApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: planTemplateConfigApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: planTemplateConfigApi + `/getForSelect`, data });
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'name',
    label: t('计划名称'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
];


/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('键'),
    dataIndex: 'code',
    width: 120,
  },
  {
    title: t('模板名称'),
    dataIndex: 'name',
    sorter: true,
    width: 120,
  },
  {
    title: t('版本号'),
    dataIndex: 'version',
    sorter: true,
    width: 120,
  },
  {
    title: t('是否启用'),
    dataIndex: 'enableFlag',
    width: 120,
    customRender: xundaUtils.customRenderBoolean,
  },
  {
    title: t('计划配置'),
    dataIndex: 'planConfig',
    width: 120,
  },
  {
    title: t('报名表配置'),
    dataIndex: 'applyFormConfig',
    width: 120,
  },
  {
    title: t('报名条件配置'),
    dataIndex: 'applyConditionConfig',
    width: 120,
  },
  {
    title: t('报名审核工作流'),
    dataIndex: 'applyFormFlowId',
    width: 120,
  },
  {
    title: t('证书模板'),
    dataIndex: 'certTemplate',
    width: 120,
  },
];