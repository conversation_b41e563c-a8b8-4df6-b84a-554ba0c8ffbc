import {
  bpmnStart,
  bpmnTask,
  bpmnEnd,
  bpmnSubFlow,
  bpmnInclusive,
  bpmnParallel,
  bpmnExclusive,
  typeStart,
  typeTask,
  typeEnd,
  typeLabel,
  typeSubFlow,
  bpmnGroup,
  typeGroup,
  typeTrigger,
  typeGetData,
  typeAddData,
  typeUpdateData,
  typeDelData,
  typeInterface,
  typeLaunchFlow,
  typeMessage,
  typeSchedule,
  bpmnExecute,
  bpmnWebhook,
  bpmnNotice,
  bpmnTime,
  bpmnEvent,
  typeEventTrigger,
  typeTimeTrigger,
  typeNoticeTrigger,
  typeWebhookTrigger,
  bpmnCopy,
  typeCopy,
  bpmnPaste,
  typePaste,
  bpmnProcessing,
  typeProcessing,
  bpmnChoose,
  typeChoose,
  typeInclusion,
  typeParallel,
  typeExclusive,
} from '../variableName';

const START = {
  name: 'append.xunda-start',
  group: 'model',
  className: 'context-pad-start icon-ym icon-ym-flow-node-start',
  icon: 'icon-ym icon-ym-flow-node-start',
  title: '流程发起',
  ymName: 'xunda-startEvent',
  type: bpmnStart,
  elementName: typeStart,
  wnType: typeStart,
};

const APPROVER = {
  name: 'append.xunda-task',
  group: 'model',
  className: 'context-pad-approver icon-ym icon-ym-flow-node-approve',
  title: '审批节点',
  type: bpmnTask,
  elementName: typeTask,
  wnType: typeTask,
};
const PROCESSING = {
  name: 'append.xunda-processing',
  group: 'model',
  className: 'context-pad-processing icon-ym icon-ym-generator-todo',
  title: '办理节点',
  type: bpmnProcessing,
  elementName: typeProcessing,
  wnType: typeProcessing,
};
const SUBFLOW = {
  name: 'append.xunda-subFlow',
  group: 'model',
  className: 'context-pad-sub-flow icon-ym icon-ym-flow-node-subFlow',
  title: '子流程',
  type: bpmnSubFlow,
  elementName: typeSubFlow,
  wnType: typeSubFlow,
};
const TRIGGER = {
  name: 'append.xunda-trigger',
  group: 'model',
  className: 'context-pad-trigger icon-ym icon-ym-flow-trigger-event',
  title: '触发节点',
  type: bpmnTask,
  elementName: typeTrigger,
  wnType: typeTrigger,
};
const GROUP = {
  name: 'append.xunda-group',
  group: 'model',
  className: 'context-pad-sub-flow icon-ym icon-ym-flow-node-subFlow',
  title: '分组',
  type: bpmnGroup,
  elementName: typeGroup,
  wnType: typeGroup,
};

const END = {
  name: 'append.xunda-end',
  group: 'model',
  className: 'context-pad-end icon-ym icon-ym-flow-node-end',
  title: '结束',
  type: bpmnEnd,
  elementName: typeEnd,
  wnType: typeEnd,
};

const CONNECT = {
  name: 'append.xunda-connect',
  group: 'connect',
  className: 'context-pad-connect icon-ym icon-ym-flow-line',
  title: '连线',
  type: 'connect',
  wnType: typeLabel,
};

const DELETE = {
  name: 'delete',
  group: 'edit',
  className: 'context-pad-delete icon-ym icon-ym-app-delete',
  title: '删除',
  ymName: 'xunda-delete',
  type: 'delete',
};
const INCLUSIVE = {
  name: 'append.xunda-inclusive',
  group: 'model',
  className: 'context-pad-condition icon-ym icon-ym-flow-node-condition-multiple',
  title: '包容分支 ',
  type: bpmnInclusive,
  elementName: typeInclusion,
  wnType: typeInclusion,
};
const PARALLEL = {
  name: 'append.xunda-parallel',
  group: 'model',
  className: 'context-pad-interflow icon-ym icon-ym-flow-node-parallel',
  title: '并行分支',
  type: bpmnParallel,
  elementName: typeParallel,
  wnType: typeParallel,
};
const EXCLUSIVE = {
  name: 'append.xunda-exclusive',
  group: 'model',
  className: 'context-pad-branch icon-ym icon-ym-flow-node-condition-single',
  title: '排它分支',
  type: bpmnExclusive,
  elementName: typeExclusive,
  wnType: typeExclusive,
};
const GETDATA = {
  name: 'append.xunda-getData',
  group: 'model',
  className: 'context-pad-getData icon-ym icon-ym-header-search',
  title: '获取数据',
  type: bpmnExecute,
  elementName: typeGetData,
  wnType: typeGetData,
};
const ADDDATA = {
  name: 'append.xunda-addData',
  group: 'model',
  className: 'context-pad-addData icon-ym icon-ym-btn-add',
  title: '新增数据',
  type: bpmnExecute,
  elementName: typeAddData,
  wnType: typeAddData,
};
const UPDATEDATA = {
  name: 'append.xunda-updateData',
  group: 'model',
  className: 'context-pad-updateData icon-ym icon-ym-generator-annular',
  title: '更新数据',
  type: bpmnExecute,
  elementName: typeUpdateData,
  wnType: typeUpdateData,
};
const DELDATA = {
  name: 'append.xunda-delData',
  group: 'model',
  className: 'context-pad-delData icon-ym icon-ym-btn-clearn',
  title: '删除数据',
  type: bpmnExecute,
  elementName: typeDelData,
  wnType: typeDelData,
};
const INTERFACE = {
  name: 'append.xunda-interface',
  group: 'model',
  className: 'context-pad-interface icon-ym icon-ym-options',
  title: '数据接口',
  type: bpmnExecute,
  elementName: typeInterface,
  wnType: typeInterface,
};
const LAUNCH = {
  name: 'append.xunda-launch',
  group: 'model',
  className: 'context-pad-launch icon-ym icon-ym-flow-node-branch',
  title: '发起审批',
  type: bpmnExecute,
  elementName: typeLaunchFlow,
  wnType: typeLaunchFlow,
};
const MESSAGE = {
  name: 'append.xunda-message',
  group: 'model',
  className: 'context-pad-message icon-ym icon-ym-header-message',
  title: '消息通知',
  type: bpmnExecute,
  elementName: typeMessage,
  wnType: typeMessage,
};
const SCHEDULE = {
  name: 'append.xunda-schedule',
  group: 'model',
  className: 'context-pad-schedule icon-ym icon-ym-xingcheng',
  title: '创建日程',
  type: bpmnExecute,
  elementName: typeSchedule,
  wnType: typeSchedule,
};
const EVENTTRIGGER = {
  name: 'append.xunda-event-trigger',
  group: 'model',
  className: 'context-pad-event-trigger icon-ym icon-ym-flow-trigger-event',
  title: '事件触发',
  type: bpmnEvent,
  elementName: typeEventTrigger,
  wnType: typeEventTrigger,
};

const TIMETRIGGER = {
  name: 'append.xunda-timeout-trigger',
  group: 'model',
  className: 'context-pad-timeout-trigger icon-ym icon-ym-flow-trigger-timer',
  title: '定时触发',
  type: bpmnTime,
  elementName: typeTimeTrigger,
  wnType: typeTimeTrigger,
};
const NOTICETRIGGER = {
  name: 'append.xunda-notice-trigger',
  group: 'model',
  className: 'context-pad-notice-trigger icon-ym icon-ym-flow-trigger-notice',
  title: '通知触发',
  type: bpmnNotice,
  elementName: typeNoticeTrigger,
  wnType: typeNoticeTrigger,
};
const WEBHOOKTRIGGER = {
  name: 'append.xunda-webhook-trigger',
  group: 'model',
  className: 'context-pad-webhook-trigger icon-ym icon-ym-flow-trigger-webhook',
  title: 'webhook',
  type: bpmnWebhook,
  elementName: typeWebhookTrigger,
  wnType: typeWebhookTrigger,
};
const COPY = {
  name: 'append.xunda-copy',
  group: 'model',
  className: 'context-pad-copy ym-custom ym-custom-content-copy',
  title: '复制',
  type: bpmnCopy,
  elementName: null,
  wnType: typeCopy,
};
const PASTE = {
  name: 'append.xunda-paste',
  group: 'model',
  className: 'context-pad-paste ym-custom ym-custom-content-paste',
  title: '粘贴',
  type: bpmnPaste,
  elementName: null,
  wnType: typePaste,
};
const CHOOSE = {
  name: 'append.xunda-choose',
  group: 'model',
  className: 'context-pad-condition icon-ym icon-ym-flow-node-branch',
  title: '选择分支',
  type: bpmnChoose,
  elementName: typeChoose,
  wnType: typeChoose,
};

interface xundaConfigBpmnContextPadProp {
  start: any;
  approver: any;
  subFlow: any;
  end: any;
  connect: any;
  del: any;
  inclusive: any;
  parallel: any;
  exclusive: any;
  group: any;
  trigger: any;
  getData: any;
  addData: any;
  updateData: any;
  delData: any;
  interfaceData: any;
  launch: any;
  message: any;
  schedule: any;
  event: any;
  timeout: any;
  notice: any;
  webhook: any;
  copy: any;
  paste: any;
  processing: any;
  choose: any;
}

const xundaConfigBpmnContextPad: xundaConfigBpmnContextPadProp = {
  start: START,
  approver: APPROVER,
  subFlow: SUBFLOW,
  end: END,
  connect: CONNECT,
  del: DELETE,
  inclusive: INCLUSIVE,
  parallel: PARALLEL,
  exclusive: EXCLUSIVE,
  group: GROUP,
  trigger: TRIGGER,
  getData: GETDATA,
  addData: ADDDATA,
  updateData: UPDATEDATA,
  delData: DELDATA,
  interfaceData: INTERFACE,
  launch: LAUNCH,
  message: MESSAGE,
  schedule: SCHEDULE,
  event: EVENTTRIGGER,
  timeout: TIMETRIGGER,
  notice: NOTICETRIGGER,
  webhook: WEBHOOKTRIGGER,
  copy: COPY,
  paste: PASTE,
  processing: PROCESSING,
  choose: CHOOSE,
};

export { xundaConfigBpmnContextPad, xundaConfigBpmnContextPadProp };
