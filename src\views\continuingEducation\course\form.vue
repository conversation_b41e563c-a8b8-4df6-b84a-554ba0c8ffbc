<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    width="100%"
    :minHeight="250"
    :cancelText="t('common.cancelText', '取消')"
    :okText="t('common.okText', '确定')"
    @ok="handleSubmit"
    :destroy-on-close="true"
    :closeFunc="onClose">
    <template #title>
      <a-space :size="10">
        <div class="text-16px font-medium">{{ title }}</div>
        <a-space-compact size="small" block v-if="dataForm.id">
          <a-tooltip :title="t('common.prevRecord')">
            <a-button size="small" :disabled="getPrevDisabled" @click="handlePrev">
              <i class="icon-ym icon-ym-caret-left text-10px"></i>
            </a-button>
          </a-tooltip>
          <a-tooltip :title="t('common.nextRecord')">
            <a-button size="small" :disabled="getNextDisabled" @click="handleNext">
              <i class="icon-ym icon-ym-caret-right text-10px"></i>
            </a-button>
          </a-tooltip>
        </a-space-compact>
      </a-space>
    </template>
    <a-row class="dynamic-form">
      <a-form
        :colon="false"
        size="middle"
        layout="horizontal"
        labelAlign="left"
        :labelCol="{ style: { width: '100px' } }"
        :model="dataForm"
        :rules="dataRule"
        ref="formRef">
        <a-row :gutter="15">
          <!-- 具体表单 -->
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="courseType">
              <template #label>课程类型 </template>
              <XundaTreeSelect
                v-model:value="dataForm.courseType"
                :options="courseTypeOptions"
                placeholder="请选择"
                allowClear
                :fieldNames="{ label: 'fullName', value: 'enCode', children: 'children' }"
                showCheckedStrategy />
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="name">
              <template #label>课程名(中) </template>
              <XundaInput
                v-model:value="dataForm.name"
                :disabled="false"
                @change="changeData('name', -1)"
                placeholder="请输入课程名(中)"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="no">
              <template #label>课程号 </template>
              <XundaInput
                v-model:value="dataForm.no"
                :disabled="false"
                @change="changeData('no', -1)"
                placeholder="课程号"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="englishName">
              <template #label>课程名(英) </template>
              <XundaInput
                v-model:value="dataForm.englishName"
                :disabled="false"
                @change="changeData('englishName', -1)"
                placeholder="课程名(英)"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="12" class="ant-col-item" :hidden="false">
            <a-form-item name="freeFlag">
              <template #label>是否免费 </template>
              <XundaSwitch v-model:value="dataForm.freeFlag" @change="changeData('freeFlag', -1)"> </XundaSwitch>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="description">
              <template #label>说明 </template>
              <XundaTextarea
                v-model:value="dataForm.description"
                :disabled="false"
                @change="changeData('description', -1)"
                placeholder="说明"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaTextarea>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="photo">
              <template #label>图片上传 </template>
              <XundaUploadImg
                v-model:value="dataForm.photo"
                :fileSize="10"
                sizeUnit="MB"
                :limit="9"
                pathType="selfPath"
                :sortRule="[3]"
                timeFormat="YYYY"
                folder="picture"
                tipText="请上传图片">
              </XundaUploadImg>
            </a-form-item>
          </a-col>
          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { create, update, getInfo } from '@/views/ea/course';
  import { XundaTreeSelect } from '@/components/Xunda/TreeSelect';
  import { reactive, toRefs, nextTick, ref, unref, computed, inject } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useUserStore } from '@/store/modules/user';
  import type { FormInstance } from 'ant-design-vue';
  // 表单权限
  import { usePermission } from '@/hooks/web/usePermission';
  import { getCourseTypeForSelect } from '.';
  interface State {
    dataForm: any;
    tableRows: any;
    dataRule: any;
    optionsObj: any;
    childIndex: any;
    isEdit: any;
    interfaceRes: any;
    //可选范围默认值
    ableAll: any;
    //掩码配置
    maskConfig: any;
    //定位属性
    locationScope: any;
    title: string;
    continueText: string;
    allList: any[];
    currIndex: number;
    isContinue: boolean;
    submitType: number;
    showContinueBtn: boolean;
    activeKey: 'basic'; // 新增：默认激活第一个tab
    prerequisiteCourses: any[];
    corequisiteCourses: any[];
  }

  const emit = defineEmits(['reload']);
  const getLeftTreeActiveInfo: (() => any) | null = inject('getLeftTreeActiveInfo', null);
  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const [registerModal, { openModal, setModalProps }] = useModal();
  const [registerCourseModal, { openCourseModal }] = useModal();
  const modalTitle = ref('');
  const selectedModalData = ref([]);
  const modalType = ref(''); // 'prerequisite' 或 'corequisite'
  const formRef = ref<FormInstance>();
  const prerequisiteTableRef = ref();
  const corequisiteTableRef = ref();
  const state = reactive<State>({
    prerequisiteCourses: [],
    corequisiteCourses: [],
    dataForm: {
      id: '',
      schoolId: '',
      campusId: undefined,
      semesterId: '',
      templateFlag: undefined,
      courseType: '',
      no: '',
      name: '',
      englishName: '',
      credit: undefined,
      useCount: undefined,
      notInCount: undefined,
      description: '',
      total: undefined,
      selected: undefined,
      remain: undefined,
      scoreConfig: '',
      photo: '',
    },
    tableRows: {},
    dataRule: {
      name: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '课程名(中)不能为空'),
          trigger: 'blur',
        },
      ],
      courseType: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '课程类型不能为空'),
          trigger: 'blur',
        },
      ],
    },
    optionsObj: {
      //选项配置
      defaultProps: { label: 'fullName', value: 'enCode' }, // 默认下拉选择键值
    },
    childIndex: -1,
    isEdit: false,
    interfaceRes: {},
    //可选范围默认值
    ableAll: {},

    //掩码配置
    maskConfig: {},
    //定位属性
    locationScope: {
      faddressDetail: [],
    },
    title: '',
    continueText: '',
    allList: [],
    currIndex: 0,
    isContinue: false,
    submitType: 0,
    showContinueBtn: true,
    activeKey: 'basic', // 新增：默认激活第一个tab
  });
  const { title, continueText, showContinueBtn, dataRule, dataForm, optionsObj, ableAll, maskConfig, submitType, activeKey } = toRefs(state);

  const getPrevDisabled = computed(() => state.currIndex === 0);
  const getNextDisabled = computed(() => state.currIndex === state.allList.length - 1);
  // 表单权限
  const { hasFormP } = usePermission();
  const courseTypeOptions = ref([]);

  defineExpose({ init });

  function init(data) {
    state.submitType = 0;
    state.isContinue = false;
    state.title = !data.id ? t('common.add2Text', '新增') : t('common.editText', '编辑');
    state.continueText = !data.id ? t('common.continueAndAddText', '确定并新增') : t('common.continueText', '确定并继续');
    setFormProps({ continueLoading: false });
    state.dataForm.id = data.id;
    openModal();
    state.allList = data.allList;
    state.currIndex = state.allList.length && data.id ? state.allList.findIndex(item => item.id === data.id) : 0;
    getAllSelectOptions();
    nextTick(() => {
      getForm().resetFields();
      setTimeout(initData, 0);
    });
  }
  function initData() {
    corequisiteTableRef.value?.init();
    prerequisiteTableRef.value?.init();
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      // 设置默认值
      state.dataForm = {
        id: '',
        schoolId: '1922122900279865345',
        campusId: undefined,
        semesterId: '1969226028783226882',
        templateFlag: undefined,
        courseType: '',
        no: '',
        name: '',
        englishName: '',
        credit: undefined,
        useCount: undefined,
        notInCount: undefined,
        description: '',
        total: undefined,
        selected: undefined,
        remain: undefined,
        scoreConfig: '',
      };

      if (getLeftTreeActiveInfo) state.dataForm = { ...state.dataForm, ...(getLeftTreeActiveInfo() || {}) };
      state.childIndex = -1;
      changeLoading(false);
    }
  }
  function getForm() {
    const form = unref(formRef);
    if (!form) {
      throw new Error('form is null!');
    }
    return form;
  }
  function getData(id) {
    getInfo(id).then(res => {
      state.dataForm = res.data || {};
      state.prerequisiteCourses = res.data?.prerequisiteCourses || [];
      state.corequisiteCourses = res.data?.corequisiteCourses || [];
      console.log('state.dataForm.photo', state.dataForm.photo);
      if (state.dataForm.photo) {
        state.dataForm.photo = JSON.parse(state.dataForm.photo);
      }
      console.log('state.dataForm.photo=====', state.dataForm.photo);
      state.childIndex = -1;
      changeLoading(false);
    });
  }
  async function handleSubmit(type) {
    try {
      const values = await getForm()?.validate();
      if (!values) return;
      setFormProps({ confirmLoading: true });

      state.dataForm.prerequisiteCourses = prerequisiteTableRef.value?.getCourses();
      state.dataForm.corequisiteCourses = corequisiteTableRef.value?.getCourses();
      state.dataForm.photo = JSON.stringify(state.dataForm.photo);
      const formMethod = state.dataForm.id ? update : create;
      console.log('values', state.dataForm);
      formMethod(state.dataForm)
        .then(res => {
          createMessage.success(res.msg);
          setFormProps({ confirmLoading: false });
          if (state.submitType == 1) {
            initData();
            state.isContinue = true;
          } else {
            setFormProps({ open: false });
            emit('reload');
          }
        })
        .catch(() => {
          setFormProps({ confirmLoading: false });
        });
    } catch (_) {}
  }

  // 跳转上一个
  function handlePrev() {
    state.currIndex--;
    handleGetNewInfo();
  }

  // 跳转下一个
  function handleNext() {
    state.currIndex++;
    handleGetNewInfo();
  }

  // 重新获取编辑信息
  function handleGetNewInfo() {
    changeLoading(true);
    getForm().resetFields();
    const id = state.allList[state.currIndex].id;
    getData(id);
  }

  function setFormProps(data) {
    setModalProps(data);
  }

  //
  function changeLoading(loading) {
    setModalProps({ loading });
  }

  // 关闭弹窗
  async function onClose() {
    if (state.isContinue) emit('reload');
    return true;
  }

  function changeData(model, index) {
    state.isEdit = false;
    state.childIndex = index;
    if (model == 'schoolId') {
      state.dataForm.campusId = undefined;
    }
    for (let key in state.interfaceRes) {
      if (key != model) {
        let faceReList = state.interfaceRes[key];
        for (let i = 0; i < faceReList.length; i++) {
          let relationField = faceReList[i].relationField;
          if (relationField) {
            let modelAll = relationField.split('-');
            let faceMode = '';
            let faceMode2 = modelAll.length == 2 ? modelAll[0].substring(0, modelAll[0].length - 4) + modelAll[1] : '';
            for (let i = 0; i < modelAll.length; i++) {
              faceMode += modelAll[i];
            }
            if (faceMode == model || faceMode2 == model) {
              let options = 'get' + key + 'Options';
              eval(options)(true);
              changeData(key, index);
            }
          }
        }
      }
    }
  }

  function getAllSelectOptions() {
    state.optionsObj.schoolIdFieldNames = { label: 'name', value: 'id' };
    getCourseTypeForSelect().then(res => {
      courseTypeOptions.value = res.data.list;
    });
  }
</script>
