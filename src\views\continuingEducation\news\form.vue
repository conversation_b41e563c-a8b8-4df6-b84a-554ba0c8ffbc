<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    width="900px"
    defaultFullscreen
    :minHeight="100"
    :cancelText="t('common.cancelText', '取消')"
    :okText="t('common.okText', '确定')"
    @ok="handleSubmit"
    :closeFunc="onClose">
    <template #title>
      <a-space :size="10">
        <div class="text-16px font-medium">{{ title }}</div>
        <a-space-compact size="small" block v-if="dataForm.id">
          <a-tooltip :title="t('common.prevRecord')">
            <a-button size="small" :disabled="getPrevDisabled" @click="handlePrev">
              <i class="icon-ym icon-ym-caret-left text-10px"></i>
            </a-button>
          </a-tooltip>
          <a-tooltip :title="t('common.nextRecord')">
            <a-button size="small" :disabled="getNextDisabled" @click="handleNext">
              <i class="icon-ym icon-ym-caret-right text-10px"></i>
            </a-button>
          </a-tooltip>
        </a-space-compact>
      </a-space>
    </template>
    <template #insertFooter>
      <div class="loat-left mt-5px">
        <XundaCheckboxSingle v-model:value="submitType" :label="continueText" />
      </div>
      <a-button @click="handlePreview" color="success">预览</a-button>
    </template>
    <a-row class="dynamic-form">
      <a-form
        :colon="false"
        size="middle"
        layout="horizontal"
        labelAlign="left"
        :labelCol="{ style: { width: '80px' } }"
        :model="dataForm"
        :rules="dataRule"
        ref="formRef">
        <a-row :gutter="15">
          <!-- 具体表单 -->
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="type">
              <template #label>信息类别 </template>
              <XundaSelect
                v-model:value="dataForm.type"
                :disabled="false"
                placeholder="请选择"
                :allowClear="true"
                :style="{ width: '100%' }"
                :showSearch="true"
                :options="[
                  { id: '1', fullName: '新闻资讯' },
                  { id: '2', fullName: '通知公告' },
                ]"
                :fieldNames="{ label: 'fullName', value: 'id' }">
              </XundaSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="title">
              <template #label>标题 </template>
              <XundaInput
                v-model:value="dataForm.title"
                :disabled="false"
                @change="changeData('title', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="content">
              <template #label>内容 </template>
              <XundaEditor v-model:value="dataForm.content" />
              <!-- <XundaInput
                v-model:value="dataForm.content"
                :disabled="false"
                @change="changeData('content', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput> -->
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="attachment">
              <template #label>附件 </template>
              <XundaUploadFile
                v-model:value="dataForm.attachment"
                :fileSize="10"
                sizeUnit="MB"
                :limit="9"
                pathType="selfPath"
                :sortRule="[3]"
                timeFormat="YYYY"
                folder="fileFiled"
                buttonText="点击上传">
              </XundaUploadFile>
            </a-form-item>
          </a-col>
          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>

  <!-- 预览组件 -->
  <Detail ref="detailRef" @reload="() => {}" />
</template>
<script lang="ts" setup>
  import { create, update, getInfo } from './index';
  import { reactive, toRefs, nextTick, ref, unref, computed, inject } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useUserStore } from '@/store/modules/user';
  import type { FormInstance } from 'ant-design-vue';
  // 表单权限
  import { usePermission } from '@/hooks/web/usePermission';
  import Detail from './detail.vue';
  interface State {
    dataForm: any;
    tableRows: any;
    dataRule: any;
    optionsObj: any;
    childIndex: any;
    isEdit: any;
    interfaceRes: any;
    //可选范围默认值
    ableAll: any;
    //掩码配置
    maskConfig: any;
    //定位属性
    locationScope: any;
    title: string;
    continueText: string;
    allList: any[];
    currIndex: number;
    isContinue: boolean;
    submitType: number;
    showContinueBtn: boolean;
  }

  const emit = defineEmits(['reload']);
  const getLeftTreeActiveInfo: (() => any) | null = inject('getLeftTreeActiveInfo', null);
  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const [registerModal, { openModal, setModalProps }] = useModal();
  const formRef = ref<FormInstance>();
  const detailRef = ref<InstanceType<typeof Detail> | null>(null);

  const state = reactive<State>({
    dataForm: {
      id: undefined,
      semesterId: undefined,
      title: '',
      content: '',
      attachment: '',
    },
    tableRows: {},
    dataRule: {
      type: [{ required: true, message: '请选择信息类别' }],
      title: [
        {
          required: 'true',
          message: t('请输入资讯标题'),
          trigger: 'blur',
        },
      ],
      content: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '不能为空'),
          trigger: 'blur',
        },
      ],
    },
    optionsObj: {
      //选项配置
      semesterIdOptions: [],
      semesterIdProps: {
        label: 'name',
        value: 'id',
      },
      defaultProps: { label: 'fullName', value: 'enCode', children: 'children' }, // 默认下拉选择键值
    },
    childIndex: -1,
    isEdit: false,
    interfaceRes: {
      id: [],
      semesterId: [],
      title: [],
      content: [],
      attachment: [],
    },
    //可选范围默认值
    ableAll: {},

    //掩码配置
    maskConfig: {
      id: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      semesterId: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      title: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      content: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      attachment: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
    },
    //定位属性
    locationScope: {
      faddressDetail: [],
    },
    title: '',
    continueText: '',
    allList: [],
    currIndex: 0,
    isContinue: false,
    submitType: 0,
    showContinueBtn: true,
  });
  const { title, continueText, showContinueBtn, dataRule, dataForm, optionsObj, ableAll, maskConfig, submitType } = toRefs(state);

  const getPrevDisabled = computed(() => state.currIndex === 0);
  const getNextDisabled = computed(() => state.currIndex === state.allList.length - 1);
  // 表单权限
  const { hasFormP } = usePermission();

  defineExpose({ init });

  function init(data) {
    state.submitType = 0;
    state.isContinue = false;
    state.title = !data.id ? t('common.add2Text', '新增') : t('common.editText', '编辑');
    state.continueText = !data.id ? t('common.continueAndAddText', '确定并新增') : t('common.continueText', '确定并继续');
    setFormProps({ continueLoading: false });
    state.dataForm.id = data.id;
    openModal();
    state.allList = data.allList;
    state.currIndex = state.allList.length && data.id ? state.allList.findIndex(item => item.id === data.id) : 0;
    getAllSelectOptions();
    nextTick(() => {
      getForm().resetFields();
      setTimeout(initData, 0);
    });
  }
  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      // 设置默认值
      state.dataForm = {
        id: undefined,
        semesterId: '1969226028783226882',
        title: '',
        content: '',
        attachment: '',
      };
      if (getLeftTreeActiveInfo) state.dataForm = { ...state.dataForm, ...(getLeftTreeActiveInfo() || {}) };
      state.childIndex = -1;
      changeLoading(false);
    }
  }
  function getForm() {
    const form = unref(formRef);
    if (!form) {
      throw new Error('form is null!');
    }
    return form;
  }
  function getData(id) {
    getInfo(id).then(res => {
      state.dataForm = res.data || {};
      state.childIndex = -1;
      if (res.data.attachment) {
        state.dataForm.attachment = JSON.parse(res.data.attachment);
      }
      changeLoading(false);
    });
  }

  // 预览功能
  async function handlePreview() {
    try {
      const values = await getForm()?.validate();
      if (!values) return;

      // 准备预览数据
      const previewData = {
        id: undefined,
        isPreview: true,
        title: state.dataForm.title,
        content: state.dataForm.content,
        attachment: Array.isArray(state.dataForm.attachment)
          ? state.dataForm.attachment.map(file => file.name || file.fileName || '').join(', ')
          : typeof state.dataForm.attachment === 'object'
          ? state.dataForm.attachment.name || state.dataForm.attachment.fileName || JSON.stringify(state.dataForm.attachment)
          : state.dataForm.attachment,
        semesterName: '', // 这里可以根据需要添加学期名称
      };

      // 调用detail.vue组件进行预览
      detailRef.value?.init(previewData);
    } catch (_) {
      createMessage.warning(t('common.validateError', '请正确填写表单'));
    }
  }

  async function handleSubmit(type) {
    try {
      const values = await getForm()?.validate();
      if (!values) return;
      setFormProps({ confirmLoading: true });
      const formMethod = state.dataForm.id ? update : create;
      console.log('values', state.dataForm);
      var query = { ...state.dataForm };
      if (query.attachment) {
        query.attachment = JSON.stringify(query.attachment);
      }
      formMethod(query)
        .then(res => {
          createMessage.success(res.msg);
          setFormProps({ confirmLoading: false });
          if (state.submitType == 1) {
            initData();
            state.isContinue = true;
          } else {
            setFormProps({ open: false });
            emit('reload');
          }
        })
        .catch(() => {
          setFormProps({ confirmLoading: false });
        });
    } catch (_) {}
  }

  // 跳转上一个
  function handlePrev() {
    state.currIndex--;
    handleGetNewInfo();
  }

  // 跳转下一个
  function handleNext() {
    state.currIndex++;
    handleGetNewInfo();
  }

  // 重新获取编辑信息
  function handleGetNewInfo() {
    changeLoading(true);
    getForm().resetFields();
    const id = state.allList[state.currIndex].id;
    getData(id);
  }

  function setFormProps(data) {
    setModalProps(data);
  }

  //
  function changeLoading(loading) {
    setModalProps({ loading });
  }

  // 关闭弹窗
  async function onClose() {
    if (state.isContinue) emit('reload');
    return true;
  }

  function changeData(model, index) {
    state.isEdit = false;
    state.childIndex = index;
    for (let key in state.interfaceRes) {
      if (key != model) {
        let faceReList = state.interfaceRes[key];
        for (let i = 0; i < faceReList.length; i++) {
          let relationField = faceReList[i].relationField;
          if (relationField) {
            let modelAll = relationField.split('-');
            let faceMode = '';
            let faceMode2 = modelAll.length == 2 ? modelAll[0].substring(0, modelAll[0].length - 4) + modelAll[1] : '';
            for (let i = 0; i < modelAll.length; i++) {
              faceMode += modelAll[i];
            }
            if (faceMode == model || faceMode2 == model) {
              let options = 'get' + key + 'Options';
              eval(options)(true);
              changeData(key, index);
            }
          }
        }
      }
    }
  }

  function getAllSelectOptions() {}
</script>

<style lang="less" scoped>
  .news-detail-modal {
    :deep(.ant-modal) {
      max-width: 100%;
      padding: 0;
      margin: 0;
    }

    :deep(.ant-modal-content) {
      height: 100vh;
      border-radius: 0;
      display: flex;
      flex-direction: column;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    :deep(.ant-modal-body) {
      flex: 1;
      padding: 0;
      overflow: auto;
    }

    :deep(.ant-modal-header) {
      background: #fff;
      padding: 16px 24px;
      border-bottom: 1px solid #e8e8e8;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      z-index: 10;
    }

    :deep(.ant-modal-title) {
      font-size: 20px;
      font-weight: 600;
      color: #262626;
    }

    :deep(.ant-modal-close) {
      top: 10px;
      right: 16px;
    }
  }

  .news-detail-container {
    min-height: 100%;
    background: #f5f5f5;
    padding: 0;

    .news-content-wrapper {
      width: 100%;
      max-width: 800px;
      margin: 32px auto;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      overflow: hidden;

      .news-header {
        padding: 32px;
        background: linear-gradient(120deg, #f0f8ff, #e6f7ff);
        border-bottom: 1px solid #e8e8e8;

        .news-title {
          font-size: 28px;
          font-weight: 700;
          color: #262626;
          margin-bottom: 16px;
          line-height: 1.4;
          text-align: center;
        }

        .news-meta {
          display: flex;
          justify-content: center;
          color: #595959;

          .news-semester {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            padding: 4px 12px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 16px;
          }
        }
      }

      .news-content {
        padding: 32px;
        font-size: 16px;
        line-height: 1.8;
        color: #262626;
        background: #fff;

        // 基础样式优化
        :deep(p) {
          margin-bottom: 16px;
          font-size: 16px;
          line-height: 1.8;
        }

        :deep(h1) {
          font-size: 28px;
          margin: 32px 0 24px 0;
          font-weight: 700;
          color: #1890ff;
          padding-bottom: 12px;
          border-bottom: 2px solid #e8e8e8;
        }

        :deep(h2) {
          font-size: 24px;
          margin: 28px 0 20px 0;
          font-weight: 600;
          color: #1890ff;
          padding-bottom: 8px;
          border-bottom: 1px solid #f0f0f0;
        }

        :deep(h3) {
          font-size: 20px;
          margin: 24px 0 16px 0;
          font-weight: 600;
          color: #262626;
        }

        :deep(h4),
        :deep(h5),
        :deep(h6) {
          font-size: 18px;
          margin: 20px 0 12px 0;
          font-weight: 500;
          color: #595959;
        }

        :deep(img) {
          max-width: 100%;
          height: auto;
          margin: 24px auto;
          display: block;
          border-radius: 4px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        :deep(ul),
        :deep(ol) {
          padding-left: 24px;
          margin-bottom: 16px;
        }

        :deep(li) {
          margin-bottom: 8px;
        }

        :deep(a) {
          color: #1890ff;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }

        :deep(blockquote) {
          margin: 24px 0;
          padding: 16px 24px;
          border-left: 4px solid #1890ff;
          background-color: #f8f8f8;
          border-radius: 0 4px 4px 0;
          font-style: italic;
        }

        :deep(table) {
          width: 100%;
          border-collapse: collapse;
          margin: 24px 0;
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
          border-radius: 4px;
          overflow: hidden;

          th,
          td {
            border: 1px solid #d9d9d9;
            padding: 12px 16px;
            text-align: left;
          }

          th {
            background-color: #fafafa;
            font-weight: 600;
            color: #595959;
          }

          tr:nth-child(even) {
            background-color: #fbfbfb;
          }

          tr:hover {
            background-color: #f0f7ff;
          }
        }

        :deep(code) {
          background-color: #f5f5f5;
          padding: 2px 6px;
          border-radius: 3px;
          font-family: monospace;
          font-size: 14px;
        }

        :deep(pre) {
          background-color: #2d2d2d;
          color: #f8f8f2;
          padding: 16px;
          border-radius: 4px;
          overflow-x: auto;
          margin: 24px 0;
          font-size: 14px;
        }
      }

      .news-attachment {
        padding: 32px;
        margin-top: 0;
        background: #fafafa;
        border-top: 1px solid #e8e8e8;

        .attachment-label {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 18px;
          font-weight: 600;
          color: #262626;
          margin-bottom: 16px;
        }

        .attachment-content {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 16px 20px;
          background-color: #fff;
          border-radius: 6px;
          color: #595959;
          font-size: 15px;
          border: 1px dashed #d9d9d9;
          transition: all 0.3s;

          &:hover {
            border-color: #1890ff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .news-detail-container {
      padding: 0;
      background: #fff;

      .news-content-wrapper {
        margin: 0;
        border-radius: 0;
        box-shadow: none;

        .news-header {
          padding: 24px 16px;

          .news-title {
            font-size: 24px;
          }
        }

        .news-content {
          padding: 24px 16px;
          font-size: 15px;

          :deep(h1) {
            font-size: 24px;
          }

          :deep(h2) {
            font-size: 20px;
          }

          :deep(h3) {
            font-size: 18px;
          }
        }

        .news-attachment {
          padding: 24px 16px;
        }
      }
    }
  }
</style>
