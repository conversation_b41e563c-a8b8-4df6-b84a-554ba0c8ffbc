import { useI18n } from '@/hooks/web/useI18n';
import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();

// 基础Api
export const semesterApi = '/api/plan/semester';

// 导出一个异步函数，用于获取附件配置
export function getAttachmentConfigAsync(id) {
  // 使用defHttp.get方法，发送GET请求，获取附件配置
  return defHttp.get({ url: semesterApi + `/getAttachmentConfig/` + id });
}

export function setAttachmentConfigAsync(data) {
  // 使用defHttp.get方法，发送GET请求，获取附件配置
  return defHttp.post({ url: semesterApi + `/setAttachmentConfig`, data });
}
