export default [
  {
    options: {
      left: 163.5,
      top: 39,
      height: 28.5,
      width: 264,
      title: '办公用品请购审批单',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 21.75,
      fontWeight: 'bold',
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      right: 426.74219512939453,
      bottom: 89.99479293823242,
      vCenter: 294.74219512939453,
      hCenter: 75.74479293823242,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 295.5,
      top: 79.5,
      height: 32,
      width: 99,
      title: '申请部门',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 395.99219512939453,
      bottom: 163.99219512939453,
      vCenter: 346.49219512939453,
      hCenter: 147.99219512939453,
      fontSize: 11.25,
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 127.5,
      top: 79.5,
      height: 32,
      width: 168,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 297.7474136352539,
      bottom: 163.24478149414062,
      vCenter: 213.7474136352539,
      hCenter: 147.24478149414062,
      hideTitle: true,
      borderTop: 'solid',
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 394.5,
      top: 79.5,
      height: 32,
      width: 168,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 563.239631652832,
      bottom: 162.49219512939453,
      vCenter: 479.23963165283203,
      hCenter: 146.49219512939453,
      hideTitle: true,
      borderTop: 'solid',
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 79.5,
      height: 32,
      width: 99,
      title: '申请时间',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 130.5,
      bottom: 161.74219512939453,
      vCenter: 81,
      hCenter: 145.74219512939453,
      fontSize: 11.25,
      borderTop: 'solid',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 295.5,
      top: 111,
      height: 32,
      width: 99,
      title: '期望供货日期',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 395.99219512939453,
      bottom: 193.99219512939453,
      vCenter: 346.49219512939453,
      hCenter: 177.99219512939453,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 127.5,
      top: 111,
      height: 32,
      width: 168,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 296.9974136352539,
      bottom: 195.48958587646484,
      vCenter: 212.9974136352539,
      hCenter: 179.48958587646484,
      hideTitle: true,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 394.5,
      top: 111,
      height: 32,
      width: 168,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 566.49609375,
      bottom: 173.99609375,
      vCenter: 482.49609375,
      hCenter: 157.99609375,
      hideTitle: true,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 111,
      height: 32,
      width: 99,
      title: '申请人',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 129,
      bottom: 195.48958587646484,
      vCenter: 79.5,
      hCenter: 179.48958587646484,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 129,
      top: 142.5,
      height: 64,
      width: 433.5,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 294.2395935058594,
      bottom: 243.73959350585938,
      vCenter: 210.98959350585938,
      hCenter: 227.73959350585938,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 142.5,
      height: 64,
      width: 99,
      title: '请购原因',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 128.25,
      bottom: 273.49219512939453,
      vCenter: 78.75,
      hCenter: 257.49219512939453,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 181.5,
      top: 205.5,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 276.7500228881836,
      bottom: 259.99219512939453,
      vCenter: 228.7500228881836,
      hCenter: 243.99219512939453,
      hideTitle: true,
      title: '规格型号',
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 276,
      top: 205.5,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 566.49609375,
      bottom: 173.99609375,
      vCenter: 482.49609375,
      hCenter: 157.99609375,
      hideTitle: true,
      title: '请购数量',
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 370.5,
      top: 205.5,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 461.24219512939453,
      bottom: 258.49219512939453,
      vCenter: 413.24219512939453,
      hCenter: 242.49219512939453,
      hideTitle: true,
      title: '预算金额（元）',
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 85.5,
      top: 205.5,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 181.48828125,
      bottom: 259.98828125,
      vCenter: 133.48828125,
      hCenter: 243.98828125,
      hideTitle: true,
      title: '物品名称',
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 466.5,
      top: 205.5,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 556.4974136352539,
      bottom: 259.9948043823242,
      vCenter: 508.4974136352539,
      hCenter: 243.99480438232422,
      hideTitle: true,
      title: '备注',
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 205.5,
      height: 32,
      width: 55.5,
      title: '序号',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 129,
      bottom: 195.48958587646484,
      vCenter: 79.5,
      hCenter: 179.48958587646484,
      fontSize: 11.25,
      fontFamily: 'SimSun',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 181.5,
      top: 237,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 277.4974136352539,
      bottom: 291.49219512939453,
      vCenter: 229.4974136352539,
      hCenter: 275.49219512939453,
      hideTitle: true,
      title: '',
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 276,
      top: 237,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 372.73958587646484,
      bottom: 290.74219512939453,
      vCenter: 324.73958587646484,
      hCenter: 274.74219512939453,
      hideTitle: true,
      title: '',
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 370.5,
      top: 237,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 466.4948043823242,
      bottom: 291.49219512939453,
      vCenter: 418.4948043823242,
      hCenter: 275.49219512939453,
      hideTitle: true,
      title: '',
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 85.5,
      top: 237,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 181.49480438232422,
      bottom: 291.49219512939453,
      vCenter: 133.49480438232422,
      hCenter: 275.49219512939453,
      hideTitle: true,
      title: '',
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 466.5,
      top: 237,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 556.4974136352539,
      bottom: 259.9948043823242,
      vCenter: 508.4974136352539,
      hCenter: 243.99480438232422,
      hideTitle: true,
      title: '',
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 237,
      height: 32,
      width: 55.5,
      title: '1',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 85.5,
      bottom: 291.49219512939453,
      vCenter: 57.75,
      hCenter: 275.49219512939453,
      fontSize: 11.25,
      fontFamily: 'SimSun',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 276,
      top: 268.5,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 372.73958587646484,
      bottom: 290.74219512939453,
      vCenter: 324.73958587646484,
      hCenter: 274.74219512939453,
      hideTitle: true,
      title: '',
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 181.5,
      top: 268.5,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 277.4974136352539,
      bottom: 291.49219512939453,
      vCenter: 229.4974136352539,
      hCenter: 275.49219512939453,
      hideTitle: true,
      title: '',
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 370.5,
      top: 268.5,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 467.9948043823242,
      bottom: 322.98960876464844,
      vCenter: 419.9948043823242,
      hCenter: 306.98960876464844,
      hideTitle: true,
      title: '',
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 85.5,
      top: 268.5,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 181.49480438232422,
      bottom: 325.23960876464844,
      vCenter: 133.49480438232422,
      hCenter: 309.23960876464844,
      hideTitle: true,
      title: '',
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 466.5,
      top: 268.5,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 563.2474136352539,
      bottom: 322.98960876464844,
      vCenter: 515.2474136352539,
      hCenter: 306.98960876464844,
      hideTitle: true,
      title: '',
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 268.5,
      height: 32,
      width: 55.5,
      title: '2',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 85.5,
      bottom: 322.98960876464844,
      vCenter: 57.75,
      hCenter: 306.98960876464844,
      fontSize: 11.25,
      fontFamily: 'SimSun',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 181.5,
      top: 300,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 277.4974136352539,
      bottom: 291.49219512939453,
      vCenter: 229.4974136352539,
      hCenter: 275.49219512939453,
      hideTitle: true,
      title: '',
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 276,
      top: 300,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 372.73958587646484,
      bottom: 290.74219512939453,
      vCenter: 324.73958587646484,
      hCenter: 274.74219512939453,
      hideTitle: true,
      title: '',
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 370.5,
      top: 300,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 467.9948043823242,
      bottom: 322.98960876464844,
      vCenter: 419.9948043823242,
      hCenter: 306.98960876464844,
      hideTitle: true,
      title: '',
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 85.5,
      top: 300,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 181.49480438232422,
      bottom: 325.23960876464844,
      vCenter: 133.49480438232422,
      hCenter: 309.23960876464844,
      hideTitle: true,
      title: '',
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 466.5,
      top: 300,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 563.2474136352539,
      bottom: 322.98960876464844,
      vCenter: 515.2474136352539,
      hCenter: 306.98960876464844,
      hideTitle: true,
      title: '',
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 300,
      height: 32,
      width: 55.5,
      title: '3',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 85.5,
      bottom: 354.5,
      vCenter: 57.75,
      hCenter: 338.5,
      fontSize: 11.25,
      fontFamily: 'SimSun',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 181.5,
      top: 331.5,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 280.2421875,
      bottom: 363.5,
      vCenter: 232.2421875,
      hCenter: 347.5,
      hideTitle: true,
      title: '',
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 276,
      top: 332,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 372.73958587646484,
      bottom: 290.74219512939453,
      vCenter: 324.73958587646484,
      hCenter: 274.74219512939453,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 370.5,
      top: 332,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 467.9948043823242,
      bottom: 322.98960876464844,
      vCenter: 419.9948043823242,
      hCenter: 306.98960876464844,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 85.5,
      top: 332,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 182.24480438232422,
      bottom: 388.2473907470703,
      vCenter: 134.24480438232422,
      hCenter: 372.2473907470703,
      hideTitle: true,
      title: '',
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 466.5,
      top: 332,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 563.2474136352539,
      bottom: 322.98960876464844,
      vCenter: 515.2474136352539,
      hCenter: 306.98960876464844,
      hideTitle: true,
      title: '',
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 332,
      height: 32,
      width: 55.5,
      title: '4',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 84,
      bottom: 386.7473907470703,
      vCenter: 56.25,
      hCenter: 370.7473907470703,
      fontSize: 11.25,
      fontFamily: 'SimSun',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 181.5,
      top: 363,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 277.4974136352539,
      bottom: 291.49219512939453,
      vCenter: 229.4974136352539,
      hCenter: 275.49219512939453,
      hideTitle: true,
      title: '',
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 276,
      top: 363,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 372.73958587646484,
      bottom: 290.74219512939453,
      vCenter: 324.73958587646484,
      hCenter: 274.74219512939453,
      hideTitle: true,
      title: '',
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 370.5,
      top: 363,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 467.2448043823242,
      bottom: 416.7422180175781,
      vCenter: 419.2448043823242,
      hCenter: 400.7422180175781,
      hideTitle: true,
      title: '',
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 85.5,
      top: 363,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 182.24480438232422,
      bottom: 388.2473907470703,
      vCenter: 134.24480438232422,
      hCenter: 372.2473907470703,
      hideTitle: true,
      title: '',
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 466.5,
      top: 363,
      height: 32,
      width: 96,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 563.2474136352539,
      bottom: 322.98960876464844,
      vCenter: 515.2474136352539,
      hCenter: 306.98960876464844,
      hideTitle: true,
      title: '',
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 363,
      height: 32,
      width: 55.5,
      title: '5',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 84,
      bottom: 386.7473907470703,
      vCenter: 56.25,
      hCenter: 370.7473907470703,
      fontSize: 11.25,
      fontFamily: 'SimSun',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 370.5,
      top: 394.5,
      height: 32,
      width: 192,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 552.7448043823242,
      bottom: 448.9922180175781,
      vCenter: 460.4948043823242,
      hCenter: 432.9922180175781,
      fontSize: 11.25,
      fontFamily: 'SimSun',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 394.5,
      height: 32,
      width: 342,
      title: '合计',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 85.5,
      bottom: 448.9922180175781,
      vCenter: 57.75,
      hCenter: 432.9922180175781,
      fontSize: 11.25,
      fontFamily: 'SimSun',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 129,
      top: 426,
      height: 64,
      width: 433.5,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 562.4974136352539,
      bottom: 464.4948272705078,
      vCenter: 345.7474136352539,
      hCenter: 432.4948272705078,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 426,
      height: 64,
      width: 99,
      title: '部门主管意见',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 129,
      bottom: 512.4896087646484,
      vCenter: 79.5,
      hCenter: 480.48960876464844,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 129,
      top: 489,
      height: 64,
      width: 433.5,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 562.4974136352539,
      bottom: 464.4948272705078,
      vCenter: 345.7474136352539,
      hCenter: 432.4948272705078,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 489,
      height: 64,
      width: 99,
      title: '分管领导意见',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 127.75000762939453,
      bottom: 465.49742126464844,
      vCenter: 78.25000762939453,
      hCenter: 433.49742126464844,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 129,
      top: 552,
      height: 64,
      width: 433.5,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 561.2395935058594,
      bottom: 494.0000305175781,
      vCenter: 344.4895935058594,
      hCenter: 462.0000305175781,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 552,
      height: 64,
      width: 99,
      title: '行政部意见',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 129,
      bottom: 510.9922180175781,
      vCenter: 79.5,
      hCenter: 478.9922180175781,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 616,
      height: 64,
      width: 99,
      title: '采购部意见',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 129,
      bottom: 576.9948272705078,
      vCenter: 79.5,
      hCenter: 544.9948272705078,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 129,
      top: 616,
      height: 64,
      width: 433.5,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 560.2474136352539,
      bottom: 702.9974213242531,
      vCenter: 343.4974136352539,
      hCenter: 670.9974213242531,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 687,
      height: 76.5,
      width: 531,
      title: '注：\n1、申请单必须经过完整流程审批方有效\n2、各项物料规则型号需标明清楚\n3、重要物资的申购需另附相关技术资料',
      right: 561,
      bottom: 764.2448272705078,
      vCenter: 295.5,
      hCenter: 725.9948272705078,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      lineHeight: 18,
    },
    printElementType: { title: '长文本', type: 'longText' },
  },
];
