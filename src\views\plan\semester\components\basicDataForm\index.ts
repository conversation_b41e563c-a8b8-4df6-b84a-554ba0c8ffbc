import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
import { defHttp } from '@/utils/http/axios';
import { ref } from 'vue';
import { xundaUtils } from '@/utils/xunda';
const { t } = useI18n();

// 基础Api
export const semesterApi = '/api/plan/semester';
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: semesterApi + `/` + id });
}
// 新建
export function create(data) {
  return defHttp.post({ url: semesterApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: semesterApi + `/` + data.id, data });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: semesterApi + `/detail/` + id });
}
const planTypeSelect = ref([]);

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'name',
    label: t('计划名称'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
  {
    field: 'year',
    label: t('学年'),
    component: 'DatePicker',
    componentProps: ({ formModel }) => {
      return {
        submitOnPressEnter: true,
        format: 'yyyy',
        valueFormat: 'yyyy', // 确保值以年份格式存储
      };
    },
    // componentProps: {
    //   submitOnPressEnter: true,
    //   format: 'yyyy',
    //   valueFormat: 'yyyy', // 确保值以年份格式存储
    //   onChange: (date: any, dateString: string) => {
    //     const yearNumber = parseInt(dateString); // 直接获取年份数字
    //     console.log('年份数字:', date, dateString);
    //   },
    // },
  },
  {
    field: 'category',
    label: t('计划类别'),
    component: 'Select',
    componentProps: {
      submitOnPressEnter: true,
      options: planTypeSelect.value,
      fieldNames: {
        label: 'fullName',
        value: 'enCode',
      },
    },
  },
  {
    field: 'startTime',
    label: t('开始日期'),
    component: 'DateRange',
    componentProps: {
      submitOnPressEnter: true,
      format: 'YYYY-MM-DD',
    },
  },
  {
    field: 'endTime',
    label: t('结束日期'),
    component: 'DateRange',
    componentProps: {
      submitOnPressEnter: true,
      format: 'YYYY-MM-DD',
    },
  },
];

export const formSchemas: FormSchema[] = [
  {
    field: 'category',
    label: '分类',
    component: 'Select',
    colProps: {
      span: 12,
    },
    componentProps: {
      disabled: true,
      placeholder: '请选择',
      fieldNames: { label: 'fullName', value: 'enCode' },
    },
    rules: [{ required: true, trigger: 'blur', message: '必填' }],
  },
  {
    field: 'name',
    label: '计划名称',
    component: 'Input',
    colProps: {
      span: 12,
    },
    componentProps: { placeholder: '请输入', maxlength: 50 },
    rules: [{ required: true, trigger: 'blur', message: '必填' }],
  },
  {
    field: 'year',
    label: '学年',
    component: 'InputNumber',
    colProps: {
      span: 12,
    },
    componentProps: { min: 0, max: 999999 },
    rules: [{ required: true, trigger: 'blur', message: '必填' }],
  },
  {
    field: 'term',
    label: '期次',
    component: 'InputNumber',
    colProps: {
      span: 12,
    },
    componentProps: { min: 0, max: 999999 },
    rules: [{ required: true, trigger: 'blur', message: '必填' }],
  },
  {
    field: 'planLevel',
    label: '级别',
    component: 'Input',
    colProps: {
      span: 12,
    },
    componentProps: { placeholder: '请输入', maxlength: 50 },
    rules: [{ required: true, trigger: 'blur', message: '必填' }],
  },
  {
    field: 'weeks',
    component: 'InputNumber',
    label: t('周数'),
    required: true,
    colProps: {
      span: 12,
    },
    componentProps: {
      autocomplete: 'off',
      min: 0,
      addonAfter: '周',
      step: 1,
      controls: true,
    },
  },
  {
    field: 'description',
    label: '说明',
    component: 'Textarea',
    componentProps: { placeholder: '请输入', row: 3 },
  },
  {
    field: 'startTime',
    component: 'DatePicker',
    label: t('开始日期'),
    required: true,
    colProps: {
      span: 12,
    },
    componentProps: {
      autocomplete: 'off',
      style: 'width: 100%;',
      format: 'YYYY-MM-DD',
    },
  },
  {
    field: 'endTime',
    component: 'DatePicker',
    label: t('结束日期'),
    required: false,
    colProps: {
      span: 12,
    },
    componentProps: {
      autocomplete: 'off',
      style: 'width: 100%;',
      format: 'YYYY-MM-DD',
    },
  },
  {
    field: 'minNumber',
    component: 'InputNumber',
    label: t('最小开班人数'),
    required: true,
    colProps: {
      span: 12,
    },
    componentProps: {
      autocomplete: 'off',
      min: 0,
      addonAfter: '人',
      step: 1,
      controls: true,
    },
  },
  {
    field: 'maxNumber',
    component: 'InputNumber',
    label: t('最大班级人数'),
    required: true,
    colProps: {
      span: 12,
    },
    componentProps: {
      autocomplete: 'off',
      min: 0,
      addonAfter: '人',
      step: 1,
      controls: true,
    },
  },
  {
    field: 'freeFlag',
    label: '是否免费',
    component: 'Switch',
    colProps: {
      span: 12,
    },
    defaultValue: 0,
    ifShow: false,
  },
  {
    field: 'allowSelectCourse',
    label: '允许选课',
    component: 'Switch',
    colProps: {
      span: 12,
    },
    defaultValue: 0,
  },
  {
    field: 'examFlag',
    label: '需要考试',
    component: 'Switch',
    colProps: {
      span: 12,
    },
    defaultValue: 0,
  },
  {
    field: 'accommodationFlag',
    label: '提供住宿',
    component: 'Switch',
    colProps: {
      span: 12,
    },
    defaultValue: 0,
  },
  {
    field: 'photo',
    label: '图片',
    component: 'UploadImg',
    colProps: {
      span: 24,
    },
    defaultValue: 0,
  },
];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('计划名称'),
    dataIndex: 'name',
    width: 120,
    fixed: 'left',
  },
  {
    title: t('学年'),
    dataIndex: 'year',
    width: 120,
  },
  {
    title: t('期次'),
    dataIndex: 'term',
    width: 120,
  },
  {
    title: t('计划类别'),
    dataIndex: 'category',
    width: 200,
  },
  {
    title: t('级别'),
    dataIndex: 'planLevel',
    width: 120,
  },
  {
    title: t('描述'),
    dataIndex: 'description',
    width: 120,
  },
  {
    title: t('开始日期'),
    dataIndex: 'startTime',
    width: 120,
    customRender: ({ record }) => {
      return xundaUtils.toDateString(record.startTime);
    },
  },
  {
    title: t('结束日期'),
    dataIndex: 'endTime',
    width: 120,
    customRender: ({ record }) => {
      return xundaUtils.toDateString(record.endTime);
    },
  },
  {
    title: t('最小开班人数'),
    dataIndex: 'minNumber',
    width: 120,
  },
  {
    title: t('最大班级人数'),
    dataIndex: 'maxNumber',
    width: 120,
  },
  {
    title: t('已选人数'),
    dataIndex: 'currentNumber',
    width: 120,
  },
  {
    title: t('剩余空位'),
    dataIndex: 'remainNumber',
    width: 120,
  },
  {
    title: t('周数'),
    dataIndex: 'weeks',
    width: 120,
  },
  {
    title: t('是否免费'),
    dataIndex: 'freeFlag',
    width: 120,
    customRender: xundaUtils.customRenderBoolean,
  },
  {
    title: t('需要考试'),
    dataIndex: 'examFlag',
    width: 120,
    customRender: xundaUtils.customRenderBoolean,
  },
  {
    title: t('提供住宿'),
    dataIndex: 'accommodationFlag',
    width: 120,
    customRender: xundaUtils.customRenderBoolean,
  },
  {
    title: t('版本号'),
    dataIndex: 'version',
    width: 120,
  },
  {
    title: t('允许选课'),
    dataIndex: 'allowSelectCourse',
    width: 120,
    customRender: xundaUtils.customRenderBoolean,
  },
  {
    title: t('状态'),
    dataIndex: 'publishFlag',
    width: 120,
    fixed: 'right',
  },
];

export async function getPlanType(updateSchema?) {
  const res = await getDictionaryDataSelector('planType');
  planTypeSelect.value = res.data.list;
  updateSchema({
    field: 'category',
    label: t('计划类别'),
    component: 'Select',
    componentProps: {
      submitOnPressEnter: true,
      showSearch: true,
      options: planTypeSelect.value,
      fieldNames: {
        label: 'fullName',
        value: 'enCode',
      },
    },
  });
  return res.data.list;
}
