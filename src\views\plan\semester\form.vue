<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    width="800px"
    :minHeight="100"
    :cancelText="t('common.cancelText', '取消')"
    :okText="t('common.okText', '确定')"
    @ok="handleSubmit"
    :closeFunc="onClose">
    <template #title>
      <a-space :size="10">
        <div class="text-16px font-medium">{{ title }}</div>
        <a-space-compact size="small" block v-if="dataForm.id">
          <a-tooltip :title="t('common.prevRecord')">
            <a-button size="small" :disabled="getPrevDisabled" @click="handlePrev">
              <i class="icon-ym icon-ym-caret-left text-10px"></i>
            </a-button>
          </a-tooltip>
          <a-tooltip :title="t('common.nextRecord')">
            <a-button size="small" :disabled="getNextDisabled" @click="handleNext">
              <i class="icon-ym icon-ym-caret-right text-10px"></i>
            </a-button>
          </a-tooltip>
        </a-space-compact>
      </a-space>
    </template>
    <template #insertFooter>
      <div class="loat-left mt-5px">
        <XundaCheckboxSingle v-model:value="submitType" :label="continueText" />
      </div>
    </template>
    <a-tabs v-model:activeKey="activeKey" type="card" class="campus-form-tabs">
      <a-tab-pane key="planBase" tab="基础信息">
        <a-row class="dynamic-form">
          <a-form
            :colon="false"
            size="middle"
            layout="horizontal"
            labelAlign="left"
            :labelCol="{ style: { width: '100px' } }"
            :model="dataForm"
            :rules="dataRule"
            ref="formRef">
            <a-row :gutter="15">
              <!-- 具体表单 -->
              <a-col :span="12" class="ant-col-item" :hidden="false">
                <a-form-item name="name">
                  <template #label>计划名称 </template>
                  <XundaInput
                    v-model:value="dataForm.name"
                    :disabled="false"
                    @change="changeData('name', -1)"
                    placeholder="请输入"
                    :allowClear="true"
                    :style="{ width: '100%' }">
                  </XundaInput>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item" :hidden="false">
                <a-form-item name="category" label="计划类别">
                  <XundaSelect
                    v-model:value="dataForm.category"
                    :disabled="false"
                    @change="changeData('category', -1)"
                    placeholder="请计划类别"
                    :allowClear="true"
                    :style="{ width: '100%' }"
                    :showSearch="true"
                    :options="optionsObj.categoryOptions"
                    :fieldNames="optionsObj.categoryProps">
                  </XundaSelect>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item" :hidden="false">
                <a-form-item name="year">
                  <template #label>学年 </template>
                  <XundaInput
                    v-model:value="dataForm.year"
                    :disabled="false"
                    @change="changeData('year', -1)"
                    placeholder="请输入"
                    :allowClear="true"
                    :style="{ width: '100%' }">
                  </XundaInput>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item" :hidden="false">
                <a-form-item name="term">
                  <template #label>期次 </template>
                  <XundaInput
                    v-model:value="dataForm.term"
                    :disabled="false"
                    @change="changeData('term', -1)"
                    placeholder="请输入"
                    :allowClear="true"
                    :style="{ width: '100%' }">
                  </XundaInput>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item" :hidden="false">
                <a-form-item name="planLevel">
                  <template #label>级别 </template>
                  <XundaInput
                    v-model:value="dataForm.planLevel"
                    :disabled="false"
                    @change="changeData('planLevel', -1)"
                    placeholder="请输入"
                    :allowClear="true"
                    :style="{ width: '100%' }">
                  </XundaInput>
                </a-form-item>
              </a-col>
              <a-col :span="24" class="ant-col-item" :hidden="false">
                <a-form-item name="description">
                  <template #label>描述 </template>
                  <XundaTextarea
                    v-model:value="dataForm.description"
                    @change="changeData('description', -1)"
                    placeholder="请输入"
                    :allowClear="true"
                    :style="{ width: '100%' }"
                    :autoSize="{ minRows: 4, maxRows: 4 }"
                    :showCount="true">
                  </XundaTextarea>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item" :hidden="false">
                <a-form-item name="startTime">
                  <template #label>开始日期 </template>
                  <XundaDatePicker
                    v-model:value="dataForm.startTime"
                    @change="changeData('startTime', -1)"
                    placeholder="请选择日期"
                    :allowClear="true"
                    :style="{ width: '100%' }"
                    format="yyyy-MM-dd">
                  </XundaDatePicker>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item" :hidden="false">
                <a-form-item name="endTime">
                  <template #label>结束日期 </template>
                  <XundaDatePicker
                    v-model:value="dataForm.endTime"
                    @change="changeData('endTime', -1)"
                    placeholder="请选择日期"
                    :allowClear="true"
                    :style="{ width: '100%' }"
                    format="yyyy-MM-dd">
                  </XundaDatePicker>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item" :hidden="false">
                <a-form-item name="minNumber">
                  <template #label>最小开班人数 </template>
                  <XundaInputNumber
                    v-model:value="dataForm.minNumber"
                    @change="changeData('minNumber', -1)"
                    placeholder="请输入"
                    :style="{ width: '100%' }"
                    :max="9999"
                    :step="1"
                    :controls="true">
                  </XundaInputNumber>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item" :hidden="false">
                <a-form-item name="maxNumber">
                  <template #label>最大班级人数 </template>
                  <XundaInputNumber
                    v-model:value="dataForm.maxNumber"
                    @change="changeData('maxNumber', -1)"
                    placeholder="请输入"
                    :style="{ width: '100%' }"
                    :max="9999"
                    :step="1"
                    :controls="true">
                  </XundaInputNumber>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item" :hidden="false">
                <a-form-item name="weeks">
                  <template #label>周数 </template>
                  <XundaInputNumber
                    v-model:value="dataForm.weeks"
                    @change="changeData('weeks', -1)"
                    placeholder="请输入"
                    :style="{ width: '100%' }"
                    :max="9999"
                    :step="1"
                    :controls="true">
                  </XundaInputNumber>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item" :hidden="false">
                <a-form-item name="freeFlag">
                  <template #label>是否免费 </template>
                  <XundaSwitch v-model:value="dataForm.freeFlag" @change="changeData('freeFlag', -1)"> </XundaSwitch>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item" :hidden="true">
                <a-form-item name="planConfigValue">
                  <template #label>报名配置 </template>
                  <XundaInput
                    v-model:value="dataForm.planConfigValue"
                    :disabled="false"
                    @change="changeData('planConfigValue', -1)"
                    placeholder="请输入"
                    :allowClear="true"
                    :style="{ width: '100%' }">
                  </XundaInput>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item" :hidden="true">
                <a-form-item name="formConfigValue">
                  <template #label>报名表单配置 </template>
                  <XundaInput
                    v-model:value="dataForm.formConfigValue"
                    :disabled="false"
                    @change="changeData('formConfigValue', -1)"
                    placeholder="请输入"
                    :allowClear="true"
                    :style="{ width: '100%' }">
                  </XundaInput>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item" :hidden="false">
                <a-form-item name="planTemplateId">
                  <template #label>附加信息模板 </template>
                  <XundaInput
                    v-model:value="dataForm.planTemplateId"
                    :disabled="false"
                    @change="changeData('planTemplateId', -1)"
                    placeholder="请输入"
                    :allowClear="true"
                    :style="{ width: '100%' }">
                  </XundaInput>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item" :hidden="false">
                <a-form-item name="examFlag">
                  <template #label>需要考试 </template>
                  <XundaSwitch v-model:value="dataForm.examFlag" @change="changeData('examFlag', -1)"> </XundaSwitch>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item" :hidden="false">
                <a-form-item name="accommodationFlag">
                  <template #label>提供住宿 </template>
                  <XundaSwitch v-model:value="dataForm.accommodationFlag" @change="changeData('accommodationFlag', -1)"> </XundaSwitch>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item" :hidden="false">
                <a-form-item name="version">
                  <template #label>版本号 </template>
                  <XundaInput
                    v-model:value="dataForm.version"
                    :disabled="false"
                    @change="changeData('version', -1)"
                    placeholder="请输入"
                    :allowClear="true"
                    :style="{ width: '100%' }">
                  </XundaInput>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item" :hidden="false">
                <a-form-item name="allowSelectCourse">
                  <template #label>允许选课 </template>
                  <XundaSwitch v-model:value="dataForm.allowSelectCourse" @change="changeData('allowSelectCourse', -1)"> </XundaSwitch>
                </a-form-item>
              </a-col>
              <!-- 表单结束 -->
            </a-row>
          </a-form>
        </a-row>
      </a-tab-pane>
      <!-- <a-tab-pane key="timeConfig" tab="日期配置">
        <TimeTable></TimeTable>
      </a-tab-pane>
      <a-tab-pane key="feeConfig" tab="费用配置">
        <FeeTable></FeeTable>
      </a-tab-pane>
      <a-tab-pane key="policyConfig" tab="优惠政策">
        <PolicyTable></PolicyTable>
      </a-tab-pane> -->
    </a-tabs>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { create, update, getInfo, getPlanType } from '@/views/plan/semester';
  import { reactive, toRefs, nextTick, ref, unref, computed, inject } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useUserStore } from '@/store/modules/user';
  import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
  // 表单权限
  import { usePermission } from '@/hooks/web/usePermission';
  import { FormInstance } from 'ant-design-vue';
  interface State {
    dataForm: any;
    tableRows: any;
    dataRule: any;
    optionsObj: any;
    childIndex: any;
    isEdit: any;
    interfaceRes: any;
    //可选范围默认值
    ableAll: any;
    //掩码配置
    maskConfig: any;
    //定位属性
    locationScope: any;
    title: string;
    continueText: string;
    allList: any[];
    currIndex: number;
    isContinue: boolean;
    submitType: number;
    showContinueBtn: boolean;
    activeKey: 'planBase'; //新增当前激活的tab
  }

  const emit = defineEmits(['reload']);
  const getLeftTreeActiveInfo: (() => any) | null = inject('getLeftTreeActiveInfo', null);
  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const [registerModal, { openModal, setModalProps }] = useModal();
  const formRef = ref<FormInstance>();
  const state = reactive<State>({
    dataForm: {
      fId: '',
      name: '',
      year: '',
      term: '',
      category: '',
      planLevel: '',
      description: '',
      startTime: undefined,
      endTime: undefined,
      minNumber: undefined,
      maxNumber: undefined,
      currentNumber: undefined,
      remainNumber: undefined,
      weeks: undefined,
      freeFlag: undefined,
      flowId: '',
      flowTaskId: '',
      planConfigValue: '',
      formConfigValue: '',
      planTemplateId: '',
      examFlag: undefined,
      accommodationFlag: undefined,
      version: '',
      allowSelectCourse: undefined,
      fCreatorTime: undefined,
      fCreatorUserId: '',
      fLastModifyTime: undefined,
      fLastModifyUserId: '',
      fDeleteTime: undefined,
      fDeleteUserId: '',
      fDeleteMark: undefined,
      fTenantId: '',
    },
    tableRows: {},
    dataRule: {
      name: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '不能为空'),
          trigger: 'blur',
        },
      ],
      year: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '不能为空'),
          trigger: 'blur',
        },
      ],
      term: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '不能为空'),
          trigger: 'blur',
        },
      ],
      currentNumber: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '不能为空'),
          trigger: 'blur',
        },
      ],
      flowId: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '不能为空'),
          trigger: 'blur',
        },
      ],
      examFlag: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '不能为空'),
          trigger: 'blur',
        },
      ],
      accommodationFlag: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '不能为空'),
          trigger: 'blur',
        },
      ],
      version: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '不能为空'),
          trigger: 'blur',
        },
      ],
      allowSelectCourse: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '不能为空'),
          trigger: 'blur',
        },
      ],
    },
    optionsObj: {
      //选项配置
      categoryOptions: [],
      categoryProps: {
        label: 'fullName',
        value: 'enCode',
      },
      defaultProps: { label: 'fullName', value: 'enCode' }, // 默认下拉选择键值
    },
    childIndex: -1,
    isEdit: false,
    interfaceRes: {
      fId: [],
      name: [],
      year: [],
      term: [],
      category: [],
      planLevel: [],
      description: [],
      startTime: [],
      endTime: [],
      minNumber: [],
      maxNumber: [],
      currentNumber: [],
      remainNumber: [],
      weeks: [],
      freeFlag: [],
      flowId: [],
      flowTaskId: [],
      planConfigValue: [],
      formConfigValue: [],
      planTemplateId: [],
      examFlag: [],
      accommodationFlag: [],
      version: [],
      allowSelectCourse: [],
      fCreatorTime: [],
      fCreatorUserId: [],
      fLastModifyTime: [],
      fLastModifyUserId: [],
      fDeleteTime: [],
      fDeleteUserId: [],
      fDeleteMark: [],
      fTenantId: [],
    },
    //可选范围默认值
    ableAll: {},

    //掩码配置
    maskConfig: {
      fId: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      name: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      year: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      term: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      category: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      planLevel: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      description: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      startTime: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      endTime: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      minNumber: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      maxNumber: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      currentNumber: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      remainNumber: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      weeks: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      freeFlag: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      flowId: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      flowTaskId: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      planConfigValue: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      formConfigValue: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      planTemplateId: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      examFlag: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      accommodationFlag: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      version: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      allowSelectCourse: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      fCreatorTime: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      fCreatorUserId: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      fLastModifyTime: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      fLastModifyUserId: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      fDeleteTime: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      fDeleteUserId: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      fDeleteMark: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      fTenantId: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
    },
    //定位属性
    locationScope: {
      faddressDetail: [],
    },
    title: '',
    continueText: '',
    allList: [],
    currIndex: 0,
    isContinue: false,
    submitType: 0,
    showContinueBtn: true,
    activeKey: 'planBase',
  });
  const { title, continueText, showContinueBtn, dataRule, dataForm, optionsObj, ableAll, maskConfig, submitType, activeKey } = toRefs(state);

  const getPrevDisabled = computed(() => state.currIndex === 0);
  const getNextDisabled = computed(() => state.currIndex === state.allList.length - 1);
  // 表单权限
  const { hasFormP } = usePermission();

  defineExpose({ init });

  const filingFormData = ref({});
  const noticeData = ref({});
  function init(data, updateSchema?) {
    activeKey.value = 'planBase';
    state.submitType = 0;
    state.isContinue = false;
    state.title = !data.id ? t('common.add2Text', '新增') : t('common.editText', '编辑');
    state.continueText = !data.id ? t('common.continueAndAddText', '确定并新增') : t('common.continueText', '确定并继续');
    setFormProps({ continueLoading: false });
    state.dataForm.id = data.id;
    openModal();
    state.allList = data.allList;
    state.currIndex = state.allList.length && data.id ? state.allList.findIndex(item => item.id === data.id) : 0;
    nextTick(() => {
      getAllSelectOptions(updateSchema);
      getForm().resetFields();
      setTimeout(initData, 0);
    });
  }
  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      // 设置默认值
      state.dataForm = {
        fId: '',
        name: '',
        year: '',
        term: '',
        category: '',
        planLevel: '',
        description: '',
        startTime: undefined,
        endTime: undefined,
        minNumber: undefined,
        maxNumber: undefined,
        currentNumber: undefined,
        remainNumber: undefined,
        weeks: undefined,
        freeFlag: undefined,
        flowId: '',
        flowTaskId: '',
        planConfigValue: '',
        formConfigValue: '',
        planTemplateId: '',
        examFlag: undefined,
        accommodationFlag: undefined,
        version: '',
        allowSelectCourse: undefined,
        fCreatorTime: undefined,
        fCreatorUserId: '',
        fLastModifyTime: undefined,
        fLastModifyUserId: '',
        fDeleteTime: undefined,
        fDeleteUserId: '',
        fDeleteMark: undefined,
        fTenantId: '',
      };
      if (getLeftTreeActiveInfo) state.dataForm = { ...state.dataForm, ...(getLeftTreeActiveInfo() || {}) };
      state.childIndex = -1;
      changeLoading(false);
    }
  }
  function getForm() {
    const form = unref(formRef);
    if (!form) {
      throw new Error('form is null!');
    }
    return form;
  }
  function getData(id) {
    getInfo(id).then(res => {
      state.dataForm = res.data || {};
      planConfigValue.value = JSON.parse(res.data.planConfigValue) || {};
      noticeData.value = planConfigValue.value.noticeData || {};
      state.childIndex = -1;
      changeLoading(false);
    });
  }
  async function handleSubmit(type) {
    try {
      const values = await getForm()?.validate();
      if (!values) return;
      console.log(planConfigValue.value);
      planConfigValue.value = {
        noticeData: noticeData.value,
      };
      state.dataForm.planConfigValue = JSON.stringify(planConfigValue.value);
      setFormProps({ confirmLoading: true });
      const formMethod = state.dataForm.id ? update : create;
      console.log('values', state.dataForm);
      formMethod(state.dataForm)
        .then(res => {
          createMessage.success(res.msg);
          setFormProps({ confirmLoading: false });
          if (state.submitType == 1) {
            initData();
            state.isContinue = true;
          } else {
            setFormProps({ open: false });
            emit('reload');
          }
        })
        .catch(() => {
          setFormProps({ confirmLoading: false });
        });
    } catch (_) {}
  }

  // 跳转上一个
  function handlePrev() {
    state.currIndex--;
    handleGetNewInfo();
  }

  // 跳转下一个
  function handleNext() {
    state.currIndex++;
    handleGetNewInfo();
  }

  // 重新获取编辑信息
  function handleGetNewInfo() {
    changeLoading(true);
    getForm().resetFields();
    const id = state.allList[state.currIndex].id;
    getData(id);
  }

  function setFormProps(data) {
    setModalProps(data);
  }

  //
  function changeLoading(loading) {
    setModalProps({ loading });
  }

  // 关闭弹窗
  async function onClose() {
    if (state.isContinue) emit('reload');
    return true;
  }

  function changeData(model, index) {
    state.isEdit = false;
    state.childIndex = index;
    for (let key in state.interfaceRes) {
      if (key != model) {
        let faceReList = state.interfaceRes[key];
        for (let i = 0; i < faceReList.length; i++) {
          let relationField = faceReList[i].relationField;
          if (relationField) {
            let modelAll = relationField.split('-');
            let faceMode = '';
            let faceMode2 = modelAll.length == 2 ? modelAll[0].substring(0, modelAll[0].length - 4) + modelAll[1] : '';
            for (let i = 0; i < modelAll.length; i++) {
              faceMode += modelAll[i];
            }
            if (faceMode == model || faceMode2 == model) {
              let options = 'get' + key + 'Options';
              eval(options)(true);
              changeData(key, index);
            }
          }
        }
      }
    }
  }

  function getAllSelectOptions(updateSchema) {
    getDictionaryDataSelector('planType').then(res => {
      optionsObj.value.categoryOptions = res.data.list;
    });
  }

  const planConfigRef = ref();
  const planConfigValue = ref();
</script>
