import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';

// 基础Api
export const policyConfigApi = '/api/promotion/policyConfig';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: policyConfigApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: policyConfigApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: policyConfigApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: policyConfigApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: policyConfigApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: policyConfigApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: policyConfigApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: policyConfigApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: policyConfigApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: policyConfigApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: policyConfigApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: policyConfigApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: policyConfigApi + `/getForSelect`, data });
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('标识'),
    dataIndex: 'id',
    width: 120,
  },
  {
    title: t('计划'),
    dataIndex: 'semesterId',
    width: 120,
  },
  {
    title: t('优惠组'),
    dataIndex: 'groupname',
    width: 120,
  },
  {
    title: t('字段'),
    dataIndex: 'field',
    width: 120,
  },
  {
    title: t('条件'),
    dataIndex: 'condition',
    width: 120,
  },
  {
    title: t('值'),
    dataIndex: 'value',
    width: 120,
  },
  {
    title: t('值数据类型'),
    dataIndex: 'valueType',
    width: 120,
  },
  {
    title: t('优先级(大的优先)'),
    dataIndex: 'priority',
    width: 120,
  },
  {
    title: t('互斥项'),
    dataIndex: 'mutex',
    width: 120,
  },
  {
    title: t('优惠金额'),
    dataIndex: 'money',
    width: 120,
  },
  {
    title: t('币种'),
    dataIndex: 'currency',
    width: 120,
  },
];
