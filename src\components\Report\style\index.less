@prefix-cls: ~'@{namespace}-report-designer';

html[data-theme='dark'] {
  .@{prefix-cls} {
    .flex-row {
      .center-side {
        #hiprintPrintTemplate {
          background-color: #fff;
          color: #333;
        }
        // 滚动条右下角的三角形颜色
        ::-webkit-scrollbar-corner {
          background-color: #fff;
        }
      }
      .right-side {
        #PrintElementOptionSetting {
          .prop-tabs .prop-tab-items .prop-tab-item span {
            color: rgba(255, 255, 255, 0.85);
          }
          .hiprint-option-items .hiprint-option-item .hiprint-option-item-label {
            color: #fff;
          }
        }
      }
    }
  }
}

.@{prefix-cls} {
  height: 100%;
  display: flex;

  &__body {
    flex: 1;
    position: relative;
    display: flex;
    overflow: hidden;
  }
  .design-wrap {
    width: 100%;
    height: calc(100%);
  }

  .flex-row {
    height: calc(100%);
    display: flex;
    overflow: hidden;

    .left-side {
      max-width: 250px;
      min-width: 250px;
      height: calc(100%);
      background: @component-background;
      border-radius: 8px;

      .tabs-content {
        height: calc(100% - 42px);
        user-select: none;
        .components-list {
          .components-list-title {
            font-weight: 600;
            .title-tip {
              font-size: 12px;
              color: #999;
              margin-left: 4px;
              font-weight: normal;
            }
          }
          .ant-collapse-content-box {
            padding: 0 10px;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
          }
          .components-item {
            width: 110px;
            margin-bottom: 10px;
            transition: transform 0ms !important;
            &.disabled {
              .components-body {
                cursor: not-allowed;
                color: @text-color-secondary;
                &:hover {
                  color: @text-color-secondary;
                  border-color: @border-color-base;
                }
              }
            }
            &.ep-click-item .components-body {
              cursor: pointer;
            }
            .components-body {
              padding-left: 8px;
              font-size: 12px;
              height: 36px;
              cursor: move;
              border: 1px solid @border-color-base;
              border-radius: var(--border-radius);
              line-height: 34px;
              display: flex;
              align-items: center;
              color: @text-color;
              i {
                line-height: 16px;
                height: 16px;
                margin-right: 4px;
              }
              &:hover {
                border: 1px solid @primary-color;
                color: @primary-color;
              }
            }
          }
        }
        .dataSet-content {
          height: 100%;
          .dataSet-content-header {
            height: 50px;
            padding: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid @border-color-base1;
            font-size: 16px;
          }
          .dataSet-content-main {
            height: calc(100% - 50px);
          }
        }
      }
    }

    .center-side {
      background: @component-background;
      flex: 1;
      min-width: 800px;
      overflow: hidden;
      margin: 0 0 0 10px;
      border-radius: 8px;
      box-sizing: border-box;
    }
  }
}
