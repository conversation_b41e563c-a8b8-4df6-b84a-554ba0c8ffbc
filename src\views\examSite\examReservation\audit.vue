<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="title"
    width="1000px"
    :minHeight="600"
    :showOkBtn="false"
    :closeFunc="onClose"
    class="audit-modal-custom">
    <div class="audit-container">
      <!-- 左侧详情区域 -->
      <div class="detail-section">
        <div class="section-header">
          <h3 class="section-title">预约详情</h3>
        </div>

        <div class="detail-content">
          <!-- 基本信息卡片 -->
          <div class="info-card">
            <div class="detail-row">
              <div class="detail-item">
                <label class="detail-label">单位名称</label>
                <span class="detail-value">{{ dataForm.unitName || '-' }}</span>
              </div>
              <div class="detail-item">
                <label class="detail-label">联系人</label>
                <span class="detail-value">{{ dataForm.contactPerson || '-' }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <label class="detail-label">联系电话</label>
                <span class="detail-value">{{ dataForm.contactPhone || '-' }}</span>
              </div>
              <div class="detail-item">
                <label class="detail-label">考试方式</label>
                <span class="detail-value">{{ getExamModeText(dataForm.examMode) }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <label class="detail-label">考点名称</label>
                <span class="detail-value">{{ dataForm.placeName || '-' }}</span>
              </div>
              <div class="detail-item">
                <label class="detail-label">考点ID</label>
                <span class="detail-value">{{ dataForm.placeId || '-' }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <label class="detail-label">考试日期</label>
                <span class="detail-value">{{ formatDate(dataForm.examDate) }}</span>
              </div>
              <div class="detail-item">
                <label class="detail-label">开始时间</label>
                <span class="detail-value">{{ dataForm.startTime || '-' }}</span>
              </div>
            </div>
          </div>

          <!-- 考试信息卡片 -->
          <div class="info-card">
            <div class="detail-row">
              <div class="detail-item">
                <label class="detail-label">持续时长</label>
                <span class="detail-value">{{ dataForm.durationHours ? `${dataForm.durationHours}小时` : '-' }}</span>
              </div>
              <div class="detail-item">
                <label class="detail-label">预计人数</label>
                <span class="detail-value">{{ dataForm.estimatedCount || '-' }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <label class="detail-label">当前状态</label>
                <span 
                  class="detail-value status-badge"
                  :class="getStatusClass(dataForm.status)"
                >
                  {{ getStatusText(dataForm.status) }}
                </span>
              </div>
              <div class="detail-item">
                <label class="detail-label">申请单位</label>
                <span class="detail-value">{{ dataForm.unitId || '-' }}</span>
              </div>
            </div>
          </div>

          <!-- 备注信息卡片 -->
          <div class="info-card">
            <div class="detail-row">
              <div class="detail-item full-width">
                <label class="detail-label">备注</label>
                <div class="detail-value">
                  <div class="note-content">{{ dataForm.note || '-' }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 历史审核记录 -->
          <div v-if="dataForm.auditRemark" class="info-card audit-history-card">
            <div class="detail-row">
              <div class="detail-item full-width">
                <label class="detail-label">历史审核意见</label>
                <div class="detail-value">
                  <div class="audit-remark-content">
                    {{ dataForm.auditRemark }}
                    <div v-if="dataForm.auditTime" class="audit-time">{{ formatDateTime(dataForm.auditTime) }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧审核区域 -->
      <div class="audit-section">
        <div class="section-header">
          <h3 class="section-title">审核操作</h3>
        </div>

        <div class="audit-form">
          <a-form 
            ref="formRef" 
            :model="auditForm" 
            :rules="auditRules" 
            layout="vertical" 
            class="custom-form"
          >
            <!-- 审核结果 -->
            <a-form-item label="审核结果" name="auditResult" class="audit-result-item">
              <a-radio-group 
                v-model:value="auditForm.auditResult" 
                class="audit-radio-group"
                @change="handleAuditResultChange"
              >
                <a-radio :value="1" class="custom-radio success">
                  <CheckCircleOutlined />
                  <span>通过</span>
                </a-radio>
                <a-radio :value="2" class="custom-radio danger">
                  <CloseCircleOutlined />
                  <span>拒绝</span>
                </a-radio>
              </a-radio-group>
            </a-form-item>

            <!-- 审核意见 -->
            <a-form-item label="审核意见" name="auditRemark" class="audit-remark-item">
              <a-textarea
                v-model:value="auditForm.auditRemark"
                :rows="4"
                placeholder="请输入审核意见"
                class="custom-textarea"
                :class="{ 'error-input': auditForm.auditResult === 2 }"
              />
            </a-form-item>

            <!-- 操作按钮 -->
            <div class="audit-actions">
              <a-button 
                type="primary" 
                size="large" 
                :loading="submitLoading"
                @click="handleSubmitAudit"
                class="submit-btn"
                :disabled="!auditForm.auditResult"
              >
                <template #icon>
                  <CheckOutlined v-if="auditForm.auditResult === 1" />
                  <CloseOutlined v-else-if="auditForm.auditResult === 2" />
                </template>
                {{ auditForm.auditResult === 1 ? '通过审核' : auditForm.auditResult === 2 ? '拒绝申请' : '请选择审核结果' }}
              </a-button>

              <a-button 
                size="large" 
                @click="handleCancel"
                class="cancel-btn"
              >
                取消
              </a-button>
            </div>
          </a-form>
        </div>
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { getDetailInfo, auditExamReservation } from './index';
  import { reactive, toRefs, nextTick, ref, computed, unref } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useMessage } from '@/hooks/web/useMessage';
  import { toDateString, toDateTimeString } from '@/utils/xunda';
  import { 
    CheckCircleOutlined, 
    CloseCircleOutlined, 
    CheckOutlined, 
    CloseOutlined,
    LoadingOutlined
  } from '@ant-design/icons-vue';
  import type { FormInstance } from 'ant-design-vue';

  // 定义表单数据接口
  interface ExamReservationData {
    id: string;
    unitId: string;
    unitName: string;
    contactPerson: string;
    contactPhone: string;
    examMode: number;
    placeId: string;
    placeName: string;
    examDate: string;
    startTime: string;
    durationHours: string;
    estimatedCount: string;
    note: string;
    status: number;
    auditRemark: string;
    auditUserId: string;
    auditTime: string;
  }

  // 定义状态接口
  interface State {
    dataForm: Partial<ExamReservationData>;
    title: string;
  }

  // 定义审核表单接口
  interface AuditForm {
    auditResult: number | null;
    auditRemark: string;
  }

  defineOptions({ name: 'AuditModal' });
  const emit = defineEmits(['reload']);
  const [registerModal, { openModal, setModalProps, closeModal }] = useModal();
  const { t } = useI18n();
  const { createMessage, createConfirm } = useMessage();

  // 响应式状态
  const state = reactive<State>({
    dataForm: {},
    title: t('审核考点预约', '审核考点预约'),
  });

  // 审核表单
  const auditForm = reactive<AuditForm>({
    auditResult: null,
    auditRemark: '',
  });

  // 解构状态
  const { title, dataForm } = toRefs(state);
  const formRef = ref<FormInstance>();
  const submitLoading = ref(false);
  const loading = ref(false);

  // 审核表单验证规则
  const auditRules = computed(() => ({
    auditResult: [
      { 
        required: true, 
        message: t('sys.validate.textRequiredSuffix', '请选择审核结果'),
        trigger: 'change'
      },
    ],
    auditRemark: [
      {
        required: auditForm.auditResult === 2,
        message: t('sys.validate.textRequiredSuffix', '拒绝时必须填写审核意见'),
        trigger: 'blur'
      },
    ],
  }));

  // 暴露初始化方法
  defineExpose({ init });

  // 初始化审核界面
  function init(data: { id: string }) {
    // 重置数据
    state.dataForm.id = data.id;
    state.dataForm = {};
    
    // 重置审核表单
    auditForm.auditResult = null;
    auditForm.auditRemark = '';

    // 打开模态框并加载数据
    openModal();
    nextTick(() => {
      initData();
    });
  }

  // 加载数据
  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    }
  }

  // 获取详情数据
  async function getData(id: string) {
    try {
      const res = await getDetailInfo(id);
      state.dataForm = res.data || {};
    } catch (error) {
      console.error('获取详情数据失败:', error);
      createMessage.error('获取数据失败，请重试');
    } finally {
      changeLoading(false);
    }
  }

  // 设置加载状态
  function changeLoading(loadingState: boolean) {
    loading.value = loadingState;
    setModalProps({ loading: loadingState });
  }

  // 格式化日期
  function formatDate(dateString?: string): string {
    if (!dateString) return '-';
    return toDateString(dateString) || dateString;
  }

  // 格式化日期时间
  function formatDateTime(dateTimeString?: string): string {
    if (!dateTimeString) return '';
    return toDateTimeString(dateTimeString) || '';
  }

  // 获取状态文本
  function getStatusText(status?: number): string {
    const statusMap = {
      0: '待审核',
      1: '已通过',
      2: '已拒绝',
      3: '已完成'
    };
    return statusMap[status as keyof typeof statusMap] || '-';
  }

  // 获取状态样式类
  function getStatusClass(status?: number): string {
    const classMap = {
      0: 'status-pending',
      1: 'status-approved',
      2: 'status-rejected',
      3: 'status-completed'
    };
    return classMap[status as keyof typeof classMap] || '';
  }

  // 获取考试方式文本
  function getExamModeText(mode?: number): string {
    const modeMap = {
      0: '线上',
      1: '线下'
    };
    return modeMap[mode as keyof typeof modeMap] || '-';
  }

  // 审核结果变化处理
  function handleAuditResultChange() {
    // 根据审核结果自动设置默认意见
    if (auditForm.auditResult === 1) {
      auditForm.auditRemark = '审核通过';
    } else {
      auditForm.auditRemark = '';
    }
  }

  // 提交审核
  async function handleSubmitAudit() {
    try {
      const form = unref(formRef);
      if (!form) return;
      
      await form.validate();

      const resultText = auditForm.auditResult === 1 ? '通过' : '拒绝';

      createConfirm({
        iconType: 'warning',
        title: t('common.confirmTitle', '确认审核'),
        content: `确认${resultText}该考点预约申请吗？`,
        onOk: async () => {
          await submitAudit();
        },
      });
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  }

  // 执行审核提交
  async function submitAudit() {
    try {
      submitLoading.value = true;
      
      // 准备审核数据
      const auditData = {
        id: state.dataForm.id,
        status: auditForm.auditResult, // 1-通过, 2-拒绝
        auditRemark: auditForm.auditRemark,
        auditTime: new Date().toISOString(),
      };

      // 调用审核API
      await auditExamReservation(auditData);

      createMessage.success(t('common.successText', '审核提交成功'));
      closeModal();
      emit('reload');
    } catch (error) {
      console.error('审核提交失败:', error);
      createMessage.error(t('common.errorText', '审核提交失败'));
    } finally {
      submitLoading.value = false;
    }
  }

  // 取消审核
  function handleCancel() {
    closeModal();
  }

  // 关闭模态框处理
  async function onClose() {
    // 可以在这里添加表单校验提示用户是否放弃编辑
    return true;
  }
</script>

<style scoped>
  /* 模态框自定义样式 */
  .audit-modal-custom {
    .ant-modal-content {
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 24px rgba(0, 0, 0, 0.15);
    }

    .ant-modal-header {
      background-color: #fafafa;
      border-bottom: 1px solid #f0f0f0;
      padding: 16px 24px;
    }

    .ant-modal-title {
      font-size: 18px;
      font-weight: 600;
    }

    .ant-modal-body {
      padding: 0;
    }
  }

  /* 主容器 */
  .audit-container {
    display: flex;
    height: 600px;
    background-color: #fff;
  }

  /* 左侧详情区域 */
  .detail-section {
    flex: 1;
    border-right: 1px solid #f0f0f0;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
  }

  /* 区域标题 */
  .section-header {
    padding: 16px 24px;
    background-color: #fafafa;
    border-bottom: 1px solid #f0f0f0;

    .section-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.85);
    }
  }

  /* 详情内容区域 */
  .detail-content {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #d9d9d9 transparent;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
      border-radius: 3px;
    }
  }

  /* 信息卡片 */
  .info-card {
    background-color: #fafafa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    transition: all 0.3s ease;
    border: 1px solid transparent;

    &:hover {
      border-color: #e6f4ff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      background-color: #fff;
    }
  }

  /* 审核历史卡片 */
  .audit-history-card {
    border-left: 4px solid #1890ff;
  }

  /* 详情行 */
  .detail-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 12px;
    align-items: flex-start;

    &:last-child {
      margin-bottom: 0;
    }
  }

  /* 详情项 */
  .detail-item {
    display: flex;
    align-items: flex-start;
    width: 50%;
    padding-right: 16px;
    box-sizing: border-box;
    margin-bottom: 8px;

    &.full-width {
      width: 100%;
      padding-right: 0;
    }
  }

  /* 详情标签 */
  .detail-label {
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
    min-width: 90px;
    margin-right: 8px;
    flex-shrink: 0;
    font-size: 14px;
  }

  /* 详情值 */
  .detail-value {
    color: rgba(0, 0, 0, 0.65);
    flex: 1;
    word-break: break-word;
    font-size: 14px;
    line-height: 1.5;
  }

  /* 状态徽章 */
  .status-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    line-height: 1.4;
  }

  /* 状态样式 */
  .status-pending {
    color: #faad14;
    background-color: #fff7e6;
    border: 1px solid #ffe58f;
  }

  .status-approved {
    color: #52c41a;
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
  }

  .status-rejected {
    color: #f5222d;
    background-color: #fff2f0;
    border: 1px solid #ffccc7;
  }

  .status-completed {
    color: #1890ff;
    background-color: #e6f4ff;
    border: 1px solid #91d5ff;
  }

  /* 备注内容 */
  .note-content {
    background-color: #fff;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #f0f0f0;
    min-height: 60px;
    line-height: 1.6;
  }

  /* 审核意见内容 */
  .audit-remark-content {
    background-color: #fff;
    padding: 12px;
    border-radius: 6px;
    border-left: 3px solid #1890ff;
    border: 1px solid #f0f0f0;
    min-height: 60px;
    line-height: 1.6;
    position: relative;
  }

  /* 审核时间 */
  .audit-time {
    position: absolute;
    bottom: 8px;
    right: 12px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
  }

  /* 右侧审核区域 */
  .audit-section {
    width: 400px;
    display: flex;
    flex-direction: column;
    background-color: #fafafa;
    transition: all 0.3s ease;
  }

  /* 审核表单 */
  .audit-form {
    flex: 1;
    padding: 24px;
    display: flex;
    flex-direction: column;
  }

  .custom-form {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  /* 审核结果样式 */
  .audit-result-item {
    margin-bottom: 24px;
  }

  .audit-radio-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .custom-radio {
    display: flex;
    align-items: center;
    padding: 16px;
    border: 2px solid #d9d9d9;
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(24, 144, 255, 0.05);
      transform: translateX(-100%);
      transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    &:hover {
      border-color: #40a9ff;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);

      &::before {
        transform: translateX(0);
      }
    }

    &.ant-radio-wrapper-checked {
      border-color: #1890ff;
      background-color: #f6ffed;
    }

    .anticon {
      font-size: 20px;
      margin-right: 12px;
      transition: all 0.3s;
    }

    span {
      font-weight: 500;
      font-size: 14px;
      transition: all 0.3s;
    }
  }

  .custom-radio.success {
    &.ant-radio-wrapper-checked {
      border-color: #52c41a;
      background-color: #f6ffed;

      .anticon {
        color: #52c41a;
      }
    }
  }

  .custom-radio.danger {
    &.ant-radio-wrapper-checked {
      border-color: #f5222d;
      background-color: #fff2f0;

      .anticon {
        color: #f5222d;
      }
    }
  }

  /* 审核意见样式 */
  .audit-remark-item {
    flex: 1;
    margin-bottom: 24px;
  }

  .custom-textarea {
    border-radius: 6px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    resize: vertical;

    &:focus {
      border-color: #40a9ff !important;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }

  .error-input {
    border-color: #f5222d !important;

    &:focus {
      border-color: #f5222d !important;
      box-shadow: 0 0 0 2px rgba(245, 34, 45, 0.2) !important;
    }
  }

  /* 操作按钮样式 */
  .audit-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: auto;
  }

  .submit-btn {
    height: 44px;
    border-radius: 6px;
    font-weight: 500;
    font-size: 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &.ant-btn-primary {
      background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
      border: none;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);

      &:hover:not(:disabled) {
        background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
      }

      &:disabled {
        background: #f5f5f5;
        color: #bfbfbf;
        box-shadow: none;
      }
    }
  }

  .cancel-btn {
    height: 44px;
    border-radius: 6px;
    font-weight: 500;
    font-size: 16px;
    border-color: #d9d9d9;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      border-color: #40a9ff;
      color: #40a9ff;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
    }
  }

  /* 响应式调整 */
  @media (max-width: 1024px) {
    .audit-container {
      flex-direction: column;
      height: auto;
    }

    .audit-section {
      width: 100%;
      border-top: 1px solid #f0f0f0;
    }

    .detail-item {
      width: 100%;
      padding-right: 0;
    }
  }
</style>
