<template>
  <div class="xunda-content-wrapper">
    <div class="xunda-content-wrapper-center">
      <div class="xunda-content-wrapper-search-box">
        <!-- <BasicForm
          @register="registerSearchForm"
          :schemas="searchSchemas"
          @advanced-change="redoHeight"
          @submit="handleSearchSubmit"
          @reset="handleSearchReset"
          class="search-form">
        </BasicForm> -->
      </div>
      <div class="xunda-content-wrapper-content bg-white">
        <BasicTable @register="registerTable" ref="tableRef">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="HandleAdd()"> {{ t('common.add2Text', '新增') }}</a-button>
            <a-button
              type="link"
              preIcon="icon-ym icon-ym-btn-download"
              @click="openExportModal(true, { columnList: exportColumnList, selectIds: getSelectRowKeys() })">
              {{ t('common.exportText', '导出') }}</a-button
            >
            <a-button
              type="link"
              preIcon="icon-ym icon-ym-btn-upload"
              @click="openImportModal(true, { url: '/promotion/promotion_policyConfig', menuId: searchInfo.menuId })">
              {{ t('common.importText', '导入') }}</a-button
            >
            <a-button type="link" preIcon="icon-ym icon-ym-btn-clearn" @click="handelBatchRemove()"> {{ t('common.batchDelText', '批量删除') }}</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'id'">
              <xunda-input v-model:value="record['id']" :useMask="column.useMask" :maskConfig="column.maskConfig" :showOverflow="true" detailed />
            </template>
            <template v-if="column.dataIndex === 'semesterId'">
              <xunda-input v-model:value="record['semesterId']" :useMask="column.useMask" :maskConfig="column.maskConfig" :showOverflow="true" detailed />
            </template>
            <template v-if="column.dataIndex === 'groupname'">
              <xunda-input v-model:value="record['groupname']" :useMask="column.useMask" :maskConfig="column.maskConfig" :showOverflow="true" detailed />
            </template>
            <template v-if="column.dataIndex === 'field'">
              <xunda-input v-model:value="record['field']" :useMask="column.useMask" :maskConfig="column.maskConfig" :showOverflow="true" detailed />
            </template>
            <template v-if="column.dataIndex === 'condition'">
              <xunda-input v-model:value="record['condition']" :useMask="column.useMask" :maskConfig="column.maskConfig" :showOverflow="true" detailed />
            </template>
            <template v-if="column.dataIndex === 'value'">
              <xunda-input v-model:value="record['value']" :useMask="column.useMask" :maskConfig="column.maskConfig" :showOverflow="true" detailed />
            </template>
            <template v-if="column.dataIndex === 'valueType'">
              <xunda-input v-model:value="record['valueType']" :useMask="column.useMask" :maskConfig="column.maskConfig" :showOverflow="true" detailed />
            </template>
            <template v-if="column.dataIndex === 'priority'">
              <xunda-input v-model:value="record['priority']" :useMask="column.useMask" :maskConfig="column.maskConfig" :showOverflow="true" detailed />
            </template>
            <template v-if="column.dataIndex === 'mutex'">
              <xunda-input v-model:value="record['mutex']" :useMask="column.useMask" :maskConfig="column.maskConfig" :showOverflow="true" detailed />
            </template>
            <template v-if="column.dataIndex === 'money'">
              <xunda-input v-model:value="record['money']" :useMask="column.useMask" :maskConfig="column.maskConfig" :showOverflow="true" detailed />
            </template>
            <template v-if="column.dataIndex === 'currency'">
              <xunda-input v-model:value="record['currency']" :useMask="column.useMask" :maskConfig="column.maskConfig" :showOverflow="true" detailed />
            </template>
            <template v-if="column.key === 'action'">
              <TableAction
                :actions="[
                  {
                    label: t('common.editText', '编辑'),
                    onClick: handleEdit.bind(null, record),
                  },
                  {
                    label: t('common.delText', '删除'),
                    color: 'error',
                    modelConfirm: {
                      onOk: handleDelete.bind(null, record.id),
                    },
                  },
                  {
                    label: t('common.detailText', '详情'),
                    onClick: HandleDetail.bind(null, record),
                  },
                ]" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form ref="formRef" @reload="reload" />
    <Detail ref="detailRef" @reload="reload" />
    <ExportModal @register="registerExportModal" @download="handleExport" />
    <ImportModal @register="registerImportModal" @reload="reload" />
  </div>
</template>

<script lang="ts" setup>
  import { policyConfigApi, getList, batchDelete, exportData, columns, searchSchemas } from '@/views/plan/semester/components/policyConfig';
  import { getConfigData } from '@/api/onlineDev/visualDev';
  import { ref, reactive, toRefs, onMounted, computed } from 'vue';
  import { useModal } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { BasicForm, useForm } from '@/components/Form';
  import { BasicTable, useTable, TableAction, TableActionType } from '@/components/Table';
  import Form from '@/views/promotion/policyConfig/Form.vue';
  import Detail from '@/views/promotion/policyConfig/Detail.vue';
  import { useRoute } from 'vue-router';
  import { cloneDeep } from 'lodash-es';
  import { ExportModal } from '@/components/CommonModal';
  import { downloadByUrl } from '@/utils/file/download';
  import { ImportModal } from '@/components/CommonModal';
  import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
  import { useUserStore } from '@/store/modules/user';
  import { usePermission } from '@/hooks/web/usePermission';
  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;
  const { hasFormP } = usePermission();
  const route = useRoute();
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const [registerExportModal, { openModal: openExportModal, closeModal: closeExportModal, setModalProps: setExportModalProps }] = useModal();
  const [registerImportModal, { openModal: openImportModal }] = useModal();
  const formRef = ref<any>(null);
  const tableRef = ref<Nullable<TableActionType>>(null);
  const detailRef = ref<any>(null);
  const cacheList = ref<any>([]);
  const defaultSearchInfo = {
    menuId: route.meta.modelId as string,
    moduleId: '***********', //模块ID
    superQueryJson: '',
    dataType: 0,
  };
  const exportColumnList = computed(() => {
    return columns.map(o => {
      return {
        id: o.dataIndex,
        key: o.dataIndex,
        value: o.dataIndex,
        fullName: o.title,
        __config__: {
          xundakey: 'text',
        },
      };
    });
  });
  const searchInfo = reactive({
    ...cloneDeep(defaultSearchInfo),
  });
  const [
    registerSearchForm,
    { submit, setFieldsValue, resetFields, getFieldsValue, clearValidate, updateSchema, removeSchemaByField, appendSchemaByField, validateFields },
  ] = useForm({
    baseColProps: { span: 6 },
    showActionButtonGroup: true,
    showAdvancedButton: true,
    compact: true,
  });
  const [registerTable, { reload, setLoading, getFetchParams, redoHeight, getSelectRowKeys, clearSelectedRowKeys }] = useTable({
    api: getList,
    columns: columns,
    searchInfo: searchInfo,
    clickToRowSelect: false,
    afterFetch: data => {
      const list = data.map(o => ({
        ...o,
      }));
      cacheList.value = cloneDeep(list);
      return list;
    },
    actionColumn: {
      width: 200,
      title: t('common.actionText', '操作'),
      dataIndex: 'action',
      fixed: 'right',
    },
    rowSelection: {
      type: 'checkbox',
      getCheckboxProps: record => ({
        disabled: record.top,
      }),
    },
  });

  function handleSearchReset() {
    clearSelectedRowKeys();
  }

  function handleSearchSubmit(data) {
    clearSelectedRowKeys();
    let obj = {
      ...defaultSearchInfo,
      superQueryJson: searchInfo.superQueryJson,
      ...data,
    };
    Object.keys(searchInfo).map(key => {
      delete searchInfo[key];
    });
    for (let [key, value] of Object.entries(obj)) {
      searchInfo[key.replaceAll('-', '_')] = value;
    }
    console.log(searchInfo);
    reload({ page: 1 });
  }

  // 编辑
  function handleEdit(record) {
    // 不带工作流
    const data = {
      id: record.id,
      menuId: searchInfo.menuId,
      allList: cacheList.value,
    };
    formRef.value?.init(data);
  }

  // 删除
  function handleDelete(id) {
    const query = { ids: [id] };
    batchDelete(query).then(res => {
      createMessage.success(res.msg);
      clearSelectedRowKeys();
      reload();
    });
  }

  // 查看详情
  function HandleDetail(record) {
    // 不带流程
    const data = {
      id: record.id,
    };
    detailRef.value?.init(data);
  }

  // 新增
  function HandleAdd() {
    // 不带流程新增
    const data = {
      id: '',
      menuId: searchInfo.menuId,
      allList: cacheList.value,
    };
    formRef.value?.init(data);
  }

  // 导出
  function handleExport(data) {
    let query = { ...getFetchParams(), ...data };
    exportData(query)
      .then(res => {
        setExportModalProps({ confirmLoading: false });
        if (!res.data.url) return;
        downloadByUrl({ url: res.data.url });
        closeExportModal();
      })
      .catch(() => {
        setExportModalProps({ confirmLoading: false });
      });
  }

  // 批量删除
  function handelBatchRemove() {
    const ids = getSelectRowKeys();
    if (!ids.length) return createMessage.error('请选择一条数据');
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要删除这些数据吗, 是否继续?',
      onOk: () => {
        const query = { ids: ids };
        setLoading(true);
        batchDelete(query).then(res => {
          createMessage.success(res.msg);
          clearSelectedRowKeys();
          reload();
        });
      },
    });
  }

  // 设置查询表单
  function setSearchSchema() {}

  onMounted(() => {
    setSearchSchema();
  });
</script>
