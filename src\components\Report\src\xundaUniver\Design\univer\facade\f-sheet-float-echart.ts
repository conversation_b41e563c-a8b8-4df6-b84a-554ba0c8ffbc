import { XundaSheetsFloatEchartService } from '../services/sheet-float-echart.service';

export class XundaFacadeSheetsFloatEchart {
  constructor(private readonly _xundaSheetsFloatEchartService: XundaSheetsFloatEchartService) {}

  savePiniaStoreId(value: any) {
    this._xundaSheetsFloatEchartService.savePiniaStoreId(value);
  }

  clearFocusDrawingId() {
    this._xundaSheetsFloatEchartService.clearFocusDrawingId();
  }
}
