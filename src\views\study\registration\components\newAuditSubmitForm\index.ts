import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';

// 基础Api
export const registrationApi = '/api/study/registration';

// 获取审核信息
export function getRemarkAsync(id) {
  return defHttp.post({ url: registrationApi + `/getRemark/` + id });
}

// 暂存审核已经
export function setRemarkAsync(data) {
  return defHttp.post({ url: registrationApi + `/setRemark`, data });
}

// 审核/退回
export function auditRegistrationAsync(data) {
  return defHttp.post({ url: registrationApi + `/auditRegistration`, data });
}

// 发送通知
export function createRMessage(data) {
  return defHttp.post({ url: registrationApi + `/createMessage`, data });
}

// 发送通知
export function sendMessage(data) {
  return defHttp.post({ url: registrationApi + `/sendMessage`, data });
}
