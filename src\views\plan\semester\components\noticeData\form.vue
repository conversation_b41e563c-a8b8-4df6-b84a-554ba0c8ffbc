<template>
  <div class="notice-form">
    <a-form ref="formRef" :model="formState" :rules="rules" layout="horizontal">
      <div class="form-actions">
        <a-button type="primary" @click="handlePrint">下载公告</a-button>
      </div>
      <div class="form-header">
        <h2 class="form-title">贵州省职业技能等级认定公告</h2>
        <div class="form-subtitle">
          <a-form-item name="documentNo">
            <template #label>
              <span>文号</span>
              <a-tooltip title='请输入文号，格式为"×-×-×年第×-×号"'>
                <question-circle-outlined class="tip-icon" />
              </a-tooltip>
            </template>
            <a-input v-model:value="formState.documentNo" :disabled="disabled" placeholder="×-×-×年第×-×号" />
          </a-form-item>
        </div>
      </div>

      <div class="form-preamble">
        <a-form-item name="preamble">
          <template #label>
            <span>前言</span>
            <a-tooltip title="请输入前言内容">
              <question-circle-outlined class="tip-icon" />
            </a-tooltip>
          </template>
          <a-col>
            <span></span>
            <xunda-textarea
              v-model:value="formState.preamble"
              :disabled="disabled"
              placeholder="×-×-×-×-×拟面向社会开展职业技能等级认定,严格执行国家职业技能标准，现将有关认定安排公告如下："
              :allowClear="true"
              :style="{ width: '100%' }"
              :autoSize="{ minRows: 4, maxRows: 6 }"
              :showCount="true"
              :maxlength="500">
            </xunda-textarea>
          </a-col>
        </a-form-item>
      </div>

      <div class="form-content">
        <div class="content-item">
          <a-form-item name="profession">
            <template #label>
              <span class="number">一、 认定职业（工种）</span>
              <a-tooltip title="请选择认定职业（工种）">
                <question-circle-outlined class="tip-icon" />
              </a-tooltip>
            </template>
            <xunda-select
              v-model:value="formState.profession"
              :disabled="disabled"
              :options="[
                { key: '工程师', fullName: '工程师' },
                { key: '高级工程师', fullName: '高级工程师' },
                { key: '技师', fullName: '技师' },
                { key: '高级技师', fullName: '高级技师' },
              ]"
              :fieldNames="{
                label: 'fullName',
                value: 'key',
              }"
              placeholder="请选择认定职业"
              :allowClear="true"
              :style="{ width: '100%' }">
            </xunda-select>
          </a-form-item>
        </div>

        <div class="content-item">
          <a-form-item name="level">
            <template #label>
              <span class="number">二、 认定等级</span>
              <a-tooltip title="请选择相应的技能等级">
                <question-circle-outlined class="tip-icon" />
              </a-tooltip>
            </template>
            <xunda-select
              v-model:value="formState.level"
              :disabled="disabled"
              :options="[
                { key: '1', fullName: '1级' },
                { key: '2', fullName: '2级' },
                { key: '3', fullName: '3级' },
                { key: '4', fullName: '4级' },
                { key: '5', fullName: '5级' },
              ]"
              :fieldNames="{
                label: 'fullName',
                value: 'key',
              }"
              placeholder="请选择认定等级"
              :allowClear="true"
              :style="{ width: '100%' }">
            </xunda-select>
          </a-form-item>
        </div>

        <div class="content-item">
          <a-form-item name="applicationConditions">
            <template #label>
              <span class="number">三、申报条件</span>
              <a-tooltip title="请详细描述申报该职业等级认定所需的条件">
                <question-circle-outlined class="tip-icon" />
              </a-tooltip>
            </template>
            <xunda-textarea
              v-model:value="formState.applicationConditions"
              :disabled="disabled"
              placeholder="请输入申报条件"
              :allowClear="true"
              :style="{ width: '100%' }"
              :autoSize="{ minRows: 4, maxRows: 6 }"
              :showCount="true"
              :maxlength="500">
            </xunda-textarea>
          </a-form-item>
        </div>

        <div class="content-item">
          <a-form-item name="certificationStandards">
            <template #label>
              <span class="number">四、认定标准</span>
              <a-tooltip title='请输入依据的国家职业技能标准，格式为"《国家职业技能标准（年份版）-名称》"'>
                <question-circle-outlined class="tip-icon" />
              </a-tooltip>
            </template>
            <a-input v-model:value="formState.certificationStandards" :disabled="disabled" placeholder="《国家职业技能标准（×-×年版）-×-×》" />
          </a-form-item>
        </div>

        <div class="content-item">
          <a-form-item>
            <template #label>
              <span class="number">五、认定方式</span>
              <a-tooltip title="请描述理论考试、技能考核、综合评审等具体认定方式">
                <question-circle-outlined class="tip-icon" />
              </a-tooltip>
            </template>
            <xunda-textarea
              v-model:value="formState.certificationMethod"
              :disabled="disabled"
              placeholder="请输入申报条件"
              :allowClear="true"
              :style="{ width: '100%' }"
              :autoSize="{ minRows: 4, maxRows: 6 }"
              :showCount="true"
              :maxlength="500">
            </xunda-textarea>
            <!-- <div class="sub-items">
              <div class="sub-item">1. 理论考试，机考</div>
              <div class="sub-item">2. 技能考核，机考、模拟操作、现场操作等</div>
              <div class="sub-item">3. 综合评审，审阅申报材料、答辩等</div>
            </div> -->
          </a-form-item>
        </div>

        <div class="content-item">
          <a-form-item name="registrationTime" :rules="[{ required: true, message: '请选择报名截止日期' }]">
            <template #label>
              <span class="number">六、报名时间</span>
              <a-tooltip title="请选择报名截止日期">
                <question-circle-outlined class="tip-icon" />
              </a-tooltip>
            </template>
            <a-col class="align-center">
              <span style="width: 75px">从即日起至</span>
              <XundaDatePicker
                v-model:value="formState.registrationTime"
                :disabled="disabled"
                placeholder="请选择日期"
                :allowClear="true"
                :style="{ width: '130px' }"
                format="YYYY-MM-DD">
              </XundaDatePicker>
            </a-col>
          </a-form-item>
        </div>

        <div class="content-item">
          <a-form-item name="certificationTime" :rules="[{ required: true, message: '请选择认定时间' }]">
            <template #label>
              <span class="number">七、认定时间</span>
              <a-tooltip title="请选择认定考核的具体日期">
                <question-circle-outlined class="tip-icon" />
              </a-tooltip>
            </template>
            <XundaDatePicker
              v-model:value="formState.certificationTime"
              :disabled="disabled"
              placeholder="请选择日期"
              :allowClear="true"
              :style="{ width: '130px' }"
              format="YYYY-MM-DD">
            </XundaDatePicker>
          </a-form-item>
        </div>

        <div class="content-item">
          <a-form-item name="examLocation" :rules="[{ required: true, message: '请输入考区考点' }]">
            <template #label>
              <span class="number">八、考区考点</span>
              <a-tooltip title="请输入具体考试地点">
                <question-circle-outlined class="tip-icon" />
              </a-tooltip>
            </template>
            <a-input v-model:value="formState.examLocation" :disabled="disabled" placeholder="×-×-×" />
          </a-form-item>
        </div>

        <div class="content-item">
          <a-form-item name="feeStandard">
            <template #label>
              <span class="number">九、收费标准</span>
              <a-tooltip title="请输入收费标准，如有免费政策请注明">
                <question-circle-outlined class="tip-icon" />
              </a-tooltip>
            </template>
            <a-input v-model:value="formState.feeStandard" :disabled="disabled" placeholder=" " />
          </a-form-item>
        </div>

        <div class="content-item">
          <a-form-item name="otherInfo">
            <template #label>
              <span class="number">十、其他</span>
              <a-tooltip title="请输入其他需要说明的事项">
                <question-circle-outlined class="tip-icon" />
              </a-tooltip>
            </template>
            <a-textarea v-model:value="formState.otherInfo" :disabled="disabled" :rows="4" />
          </a-form-item>
        </div>

        <div class="content-item">
          <a-form-item name="consultationPhone" :rules="[{ required: true, message: '请输入咨询电话' }]">
            <template #label>
              <span class="number">十一、咨询电话</span>
              <a-tooltip title="请输入有效的咨询电话号码">
                <question-circle-outlined class="tip-icon" />
              </a-tooltip>
            </template>
            <a-input v-model:value="formState.consultationPhone" :disabled="disabled" placeholder="×-×-×" />
          </a-form-item>
        </div>

        <div class="content-item">
          <a-form-item name="supervisionPhone" :rules="[{ required: true, message: '请输入举报监督电话' }]">
            <template #label>
              <span class="number">十二、举报监督电话</span>
              <a-tooltip title="请输入有效的举报监督电话">
                <question-circle-outlined class="tip-icon" />
              </a-tooltip>
            </template>
            <a-input v-model:value="formState.supervisionPhone" :disabled="disabled" placeholder="×-×-×" />
          </a-form-item>
        </div>
        <div class="form-note"> 注：以上认定如遇特殊情况考试时间另行通知。 </div>
        <div class="form-note"> 特此公告。 </div>
        <div class="form-footer">
          <div style="margin-bottom: 10px">
            <span>公告方：</span> <a-input v-model:value="formState.announcer" :disabled="disabled" style="width: 200px" />
          </div>
          <div style="margin-bottom: 10px">
            <span>公告时间：</span>
            <XundaDatePicker
              v-model:value="formState.certificationTime"
              :disabled="disabled"
              placeholder="请选择日期"
              :allowClear="true"
              :style="{ width: '200px' }"
              format="YYYY-MM-DD">
            </XundaDatePicker>
          </div>
        </div>
      </div>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, unref } from 'vue';
  import { message } from 'ant-design-vue';
  import type { Rule } from 'ant-design-vue/es/form';
  import { getNoticeData, printNoticeDataAsync, setNoticeData } from './index';
  import { useMessage } from '@/hooks/web/useMessage';
  import { XundaSelect, XundaInput, XundaInputNumber, XundaDatePicker, XundaTextarea } from '@/components/Xunda';
  import { QuestionCircleOutlined } from '@ant-design/icons-vue';
  import { downloadByUrl } from '@/utils/file/download';

  const { createMessage, createConfirm } = useMessage();
  const props = defineProps({
    semesterId: { type: String, default: '' },
    disabled: { type: Boolean, default: false },
  });

  // 表单引用
  const formRef = ref();
  // 定义组件的 emits
  const emit = defineEmits(['changeLoading', 'handleStep']);
  const changeLoading = loading => {
    emit('changeLoading', loading);
  };

  const defaultFormData = {
    documentNo: '×-×-×年第×-×号',
    preamble: '×-×-×-×-×拟面向社会开展职业技能等级认定,严格执行国家职业技能标准，现将有关认定安排公告如下：',
    profession: '',
    applicationConditions: '',
    certificationStandards: '《国家职业技能标准（×-×年版）-×-×》',
    certificationMethod: '1. 理论考试，机考 \n2. 技能考核，机考、模拟操作、现场操作等\n3. 综合评审，审阅申报材料、答辩等\n',
    registrationTime: null,
    certificationTime: null,
    examLocation: '',
    feeStandard: null,
    consultationPhone: '',
    supervisionPhone: '',
    announcer: '贵州交通技师学院(贵州省交通运输学校)',
    announcementTime: null,
    otherInfo:
      '报考人员需对提交的报考信息与材料的真实性、有效性负责。如提供虚假信息、虚假材料的，一经发现，按相关规定处理，取消报考资格；如已参加考试且成绩合格者取消成绩；已取得相应证书的，宣告证书无效，撤回证书并删除其在人力资源和社会保障部职业技能等级证书全国联网查询系统的查询信息，并将其违规违纪情况记入考生诚信档案。',
  };
  const formState = ref<any>({});
  function getForm() {
    const form = unref(formRef);
    if (!form) {
      throw new Error('form is null!');
    }
    return form;
  }
  function init(data) {
    changeLoading(true);
    getNoticeData({ id: props.semesterId })
      .then(res => {
        initData(res.data ?? {});
        changeLoading(false);
      })
      .catch(() => {
        changeLoading(false);
      });
  }
  function initData(data) {
    formState.value = { ...defaultFormData, ...data };
    console.log('formState:', formState.value);
  }
  async function handleSubmit(type) {
    try {
      if (type === 'next') {
        const values = await getForm()?.validate();
        if (!values) return;
      }
      changeLoading(true);
      setNoticeData({ id: props.semesterId, noticeData: formState.value })
        .then(res => {
          if (res.code === 200 && res.data === true) {
            createMessage.success('公告配置成功');
            changeLoading(false);
            emit('handleStep', type);
          }
        })
        .catch(() => {
          changeLoading(false);
        });
    } catch (_) {
      changeLoading(false);
    }
  }
  // 电话号码验证规则
  const validatePhone = async (_rule: Rule, value: string) => {
    if (!value) {
      return Promise.reject('请输入电话号码');
    }
    const phoneReg = /^1[3-9]\d{9}$|^0\d{2,3}-?\d{7,8}$/;
    if (!phoneReg.test(value)) {
      return Promise.reject('请输入正确的电话号码格式');
    }
    return Promise.resolve();
  };

  // 收费标准验证规则
  const validateFee = async (_rule: Rule, value: number | null) => {
    if (value === null) {
      return Promise.reject('请输入收费标准');
    }
    if (value < 0) {
      return Promise.reject('收费标准不能小于0');
    }
    return Promise.resolve();
  };

  // 集中管理表单验证规则
  const rules = reactive({
    documentNo: [{ required: true, message: '请输入公告文号' }],
    preamble: [{ required: true, message: '请输入公告内容' }],
    profession: [{ required: true, message: '请输入职业名称' }],
    level: [{ required: true, message: '请输入认定等级' }],
    applicationConditions: [{ required: true, message: '请输入申报条件' }],
    certificationStandards: [{ required: true, message: '请输入认定标准' }],
    certificationMethod: [{ required: false, message: '请输入认定方法' }],
    registrationTime: [{ required: true, message: '请选择报名截止日期' }],
    certificationTime: [{ required: true, message: '请选择认定时间' }],
    examLocation: [{ required: true, message: '请输入考区考点' }],
    feeStandard: [{ validator: validateFee, trigger: 'change' }],
    otherInfo: [{ required: false, message: '请输入其他信息' }],
    consultationPhone: [{ required: true, message: '请输入咨询电话', validator: validatePhone, trigger: 'blur' }],
    supervisionPhone: [{ required: true, message: '请输入监督电话', validator: validatePhone, trigger: 'blur' }],
    announcer: [{ required: true, message: '请输入公告发布人' }],
    announcementTime: [{ required: true, message: '请选择公告发布时间' }],
  });

  function handlePrint() {
    printNoticeDataAsync(props.semesterId, 'noticeData').then(res => {
      if (res.code === 200) {
        downloadByUrl({ url: res.data?.url });
      }
    });
  }

  // 暴露表单方法给父组件
  defineExpose({
    init,
    handleSubmit,
  });
</script>

<style lang="less" scoped>
  .tip-icon {
    margin-left: 4px;
    color: #999;
    cursor: pointer;
    &:hover {
      color: #1890ff;
    }
  }

  .notice-form {
    padding: 40px;
    background: #fff;
    border-radius: 2px;
    max-width: 900px;
    margin: 0 auto;

    .form-code {
      text-align: left;
      margin-bottom: 20px;
      font-size: 16px;
    }

    .form-header {
      text-align: center;
      margin-bottom: 30px;

      .form-title {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 16px;
      }

      .form-subtitle {
        :deep(.ant-input) {
          text-align: center;
          font-size: 16px;
          border: none;
          border-bottom: 1px solid #d9d9d9;
          border-radius: 0;
        }
      }
    }

    .form-preamble {
      margin-bottom: 24px;
      :deep(.ant-input) {
        border: none;
        border-bottom: 1px solid #d9d9d9;
        border-radius: 0;
      }
    }

    .form-content {
      .content-item {
        margin-bottom: 20px;

        .item-title {
          margin-bottom: 8px;
          font-weight: 500;

          .number {
            font-weight: normal;
          }

          .red {
            color: #ff4d4f;
          }
        }

        .sub-items {
          padding-left: 20px;

          .sub-item {
            margin-bottom: 8px;
          }
        }

        :deep(.ant-input),
        :deep(.ant-input-number) {
          border: none;
          border-bottom: 1px solid #d9d9d9;
          border-radius: 0;
        }
      }
    }

    .form-note {
      margin: 24px 0;
      font-weight: bold;
      font-size: 16px;
      color: #333;
    }

    .form-footer {
      margin: 24px 0;
      text-align: right;
    }

    .form-actions {
      margin-top: 40px;
      text-align: center;
    }
  }
  .align-center {
    display: flex;
    align-items: center;
  }
</style>
