<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" width="600px" :minHeight="100" :showOkBtn="false">
    <template #insertFooter> </template>
    <!-- 表单 -->
    <a-row class="dynamic-form">
      <a-form :colon="false" size="middle" layout="horizontal" labelAlign="right" :labelCol="{ style: { width: '100px' } }" :model="dataForm" ref="formRef">
        <a-row :gutter="15">
          <!-- 具体表单 -->
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="id">
              <template #label>主键 </template>
              <p>{{ dataForm.id }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="studetnExamId">
              <template #label>试卷 </template>
              <p>{{ dataForm.studetnExamId }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="parentId">
              <template #label>父题目 </template>
              <p>{{ dataForm.parentId }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="questionId">
              <template #label>题库题目标识 </template>
              <p>{{ dataForm.questionId }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="questionTypeId">
              <template #label>题型 </template>
              <p>{{ dataForm.questionTypeId }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="paperQuestionId">
              <template #label>试卷题目标识 </template>
              <p>{{ dataForm.paperQuestionId }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="no">
              <template #label>序号 </template>
              <p>{{ dataForm.no }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="displayOrder">
              <template #label>题序 </template>
              <p>{{ dataForm.displayOrder }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="content">
              <template #label>题干 </template>
              <p>{{ dataForm.content }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="option">
              <template #label>选项 </template>
              <p>{{ dataForm.option }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="answer">
              <template #label>参考答案 </template>
              <p>{{ dataForm.answer }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="studentAnswer">
              <template #label>答案 </template>
              <p>{{ dataForm.studentAnswer }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="score">
              <template #label>分值 </template>
              <p>{{ dataForm.score }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="rightFlag">
              <template #label>是否正确 </template>
              <p>{{ xundaUtils.isRender(dataForm.rightFlag) }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="realScore">
              <template #label>实际得分 </template>
              <p>{{ dataForm.realScore }} </p>
            </a-form-item>
          </a-col>
          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { getDetailInfo } from '@/views/exam/studentQuestion';
  import { reactive, toRefs, nextTick, ref } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { usePermission } from '@/hooks/web/usePermission';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
  import { xundaUtils } from '@/utils/xunda';
  interface State {
    dataForm: any;
    title: string;
    optionsObj: any;
  }
  defineOptions({ name: 'Detail' });
  const emit = defineEmits(['reload']);
  const { createMessage, createConfirm } = useMessage();
  const [registerModal, { openModal, setModalProps, closeModal }] = useModal();
  const { t } = useI18n();
  const detailRef = ref<any>(null);
  const visitRecordGridRef = ref<any>(null);
  const state = reactive<State>({
    dataForm: {},
    title: t('common.detailText', '详情'),
    optionsObj: {
      //选项配置
      defaultProps: { label: 'fullName', value: 'enCode' }, // 默认下拉选择键值
    },
  });

  const { title, dataForm, optionsObj } = toRefs(state);
  // 表单权限
  const { hasFormP } = usePermission();

  defineExpose({ init });

  function init(data) {
    getAllSelectOptions();
    state.dataForm.id = data.id;
    openModal();
    nextTick(() => {
      setTimeout(initData, 0);
    });
  }

  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      closeModal();
    }
  }

  function getData(id) {
    getDetailInfo(id).then(res => {
      state.dataForm = res.data || {};
      nextTick(() => {
        changeLoading(false);
      });
    });
  }

  function setFormProps(data) {
    setModalProps(data);
  }

  function changeLoading(loading) {
    setFormProps({ loading });
  }

  function getAllSelectOptions() {}
</script>
