<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" width="800px" :minHeight="500" :showOkBtn="false" class="detail-modal-custom">
    <!-- 详情内容 -->
    <div class="detail-container">
      <div class="detail-content">
        <div class="detail-row">
          <div class="detail-item">
            <label class="detail-label">单位名称：</label>
            <span class="detail-value">{{ dataForm.unitName || '-' }}</span>
          </div>
          <div class="detail-item">
            <label class="detail-label">联系人：</label>
            <span class="detail-value">{{ dataForm.contactPerson || '-' }}</span>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item">
            <label class="detail-label">联系电话：</label>
            <span class="detail-value">{{ dataForm.contactPhone || '-' }}</span>
          </div>
          <div class="detail-item">
            <label class="detail-label">考试方式：</label>
            <span class="detail-value">{{ dataForm.examMode === 0 ? '线上' : dataForm.examMode === 1 ? '线下' : '-' }}</span>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item">
            <label class="detail-label">考点名称：</label>
            <span class="detail-value">{{ dataForm.placeName || '-' }}</span>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item">
            <label class="detail-label">考试日期：</label>
            <span class="detail-value">{{ formatDate(dataForm.examDate) }}</span>
          </div>
          <div class="detail-item">
            <label class="detail-label">开始时间：</label>
            <span class="detail-value">{{ dataForm.startTime || '-' }}</span>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item">
            <label class="detail-label">持续时长：</label>
            <span class="detail-value">{{ dataForm.durationHours ? `${dataForm.durationHours}小时` : '-' }}</span>
          </div>
          <div class="detail-item">
            <label class="detail-label">预计人数：</label>
            <span class="detail-value">{{ dataForm.estimatedCount || '-' }}</span>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item">
            <label class="detail-label">状态：</label>
            <span class="detail-value" :class="getStatusClass(dataForm.status)">
              {{ getStatusText(dataForm.status) }}
            </span>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item full-width">
            <label class="detail-label">备注：</label>
            <div class="detail-value">{{ dataForm.note || '-' }}</div>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item">
            <label class="detail-label">审核意见：</label>
            <span class="detail-value">{{ dataForm.auditRemark || '-' }}</span>
          </div>
          <div class="detail-item">
            <label class="detail-label">审核时间：</label>
            <span class="detail-value">{{ formatDate(dataForm.auditTime) }}</span>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item">
            <label class="detail-label">创建时间：</label>
            <span class="detail-value">{{ formatDate(dataForm.creatorTime) }}</span>
          </div>
          <div class="detail-item">
            <label class="detail-label">最后修改：</label>
            <span class="detail-value">{{ formatDate(dataForm.lastModifyTime) }}</span>
          </div>
        </div>
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { getDetailInfo } from './index';
  import { reactive, toRefs, nextTick } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { useI18n } from '@/hooks/web/useI18n';
  import { toDateString } from '@/utils/xunda';

  interface State {
    dataForm: Record<string, any>;
    title: string;
  }

  defineOptions({ name: 'Detail' });
  const emit = defineEmits(['reload']);
  const [registerModal, { openModal, setModalProps }] = useModal();
  const { t } = useI18n();

  const state = reactive<State>({
    dataForm: {},
    title: t('common.detailText', '详情'),
  });

  const { title, dataForm } = toRefs(state);

  defineExpose({ init });

  // 初始化详情
  function init(data: { id: string }) {
    state.dataForm.id = data.id;
    openModal();
    nextTick(() => {
      initData();
    });
  }

  // 加载数据
  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    }
  }

  // 获取详情数据
  function getData(id: string) {
    getDetailInfo(id)
      .then(res => {
        state.dataForm = res.data || {};
        changeLoading(false);
      })
      .catch(() => {
        changeLoading(false);
      });
  }

  // 设置加载状态
  function changeLoading(loading: boolean) {
    setModalProps({ loading });
  }

  // 格式化日期时间
  function formatDate(dateString?: string): string {
    if (!dateString) return '-';
    return toDateString(dateString) || dateString;
  }

  // 获取状态文本
  function getStatusText(status?: number): string {
    if (status === 0) return '待审核';
    if (status === 1) return '已通过';
    if (status === 2) return '已拒绝';
    if (status === 3) return '已完成';
    return '-';
  }

  // 获取状态样式类
  function getStatusClass(status?: number): string {
    if (status === 0) return 'status-pending';
    if (status === 1) return 'status-approved';
    if (status === 2) return 'status-rejected';
    if (status === 3) return 'status-completed';
    return '';
  }
</script>

<style scoped>
  .detail-modal-custom {
    .ant-modal-content {
      border-radius: 8px;
      overflow: hidden;
    }

    .ant-modal-header {
      background-color: #fafafa;
      border-bottom: 1px solid #f0f0f0;
      padding: 16px 24px;
    }

    .ant-modal-body {
      padding: 24px;
    }
  }

  .detail-container {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
  }

  .detail-content {
    padding: 16px 0;
  }

  .detail-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 16px;
    align-items: flex-start;
  }

  .detail-item {
    display: flex;
    align-items: flex-start;
    width: 50%;
    padding-right: 20px;
    box-sizing: border-box;
    margin-bottom: 8px;
  }

  .detail-item.full-width {
    width: 100%;
  }

  .detail-label {
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
    min-width: 100px;
    margin-right: 8px;
    flex-shrink: 0;
  }

  .detail-value {
    color: rgba(0, 0, 0, 0.65);
    flex: 1;
    word-break: break-word;
  }

  /* 状态样式 */
  .status-pending {
    color: #faad14;
  }

  .status-approved {
    color: #52c41a;
  }

  .status-rejected {
    color: #f5222d;
  }

  .status-completed {
    color: #1890ff;
  }
</style>