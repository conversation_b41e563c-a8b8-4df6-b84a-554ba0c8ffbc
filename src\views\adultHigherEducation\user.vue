<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" destroyOnClose :title="'预报名人员'">
    <template #centerToolbar>
      <a-space :size="10">
        <a-button preIcon="icon-ym icon-ym-btn-export" type="primary" @click="exportNoticeHandler"> {{ t('生成预报名公告信息') }}</a-button>
        <CustomUpload
          accept=".xlsx"
          buttonText="单位人员导入"
          tipContent="只能上传PDF文件，单个文件大小不超过5MB"
          :fileSize="5"
          sizeUnit="MB"
          customUploadUrl="/api/study/registration/importUnitUsers"
          :uploadParams="{ acceptType: 'xlsx', semesterId: semesterId }"
          @success="handleUploadSuccess"
          @error="handleUploadError"
          :showFileList="false" />
      </a-space>
    </template>

    <div class="xunda-content-wrapper">
      <div class="xunda-content-wrapper-center">
        <div class="xunda-content-wrapper-search-box">
          <BasicForm
            @register="registerSearchForm"
            :schemas="userSearchSchemas"
            @submit="handleLeftSearchSubmit"
            @reset="handleLeftSearchReset"
            class="search-form">
          </BasicForm>
        </div>
        <div class="xunda-content-wrapper-content bg-white section-card">
          <BasicTable @register="registerTableLeft" ref="tableLeftRef">
            <template #tableTitle>
              <div class="section-title">
                <span class="title-text">已审核报名数据</span>
              </div>
              <a-button preIcon="icon-ym icon-ym-btn-export" type="primary" @click="exportThePreRegistrationDataHandler"> {{ t('导出预报名数据') }}</a-button>
              <a-button type="primary" danger preIcon="icon-ym icon-ym-btn-clearn" @click="handleBatchRemoveAudit()"> {{ t('批量取消审核') }}</a-button>
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <TableAction
                  :actions="[
                    {
                      label: t('取消审核'),
                      color: 'error',
                      onClick: handleAudit.bind(null, record),
                    },
                  ]" />
              </template>
            </template>
          </BasicTable>
        </div>
      </div>
      <div class="xunda-content-wrapper-center">
        <div class="xunda-content-wrapper-search-box">
          <BasicForm
            @register="registerSearchForm"
            :schemas="userSearchSchemas"
            @submit="handleRightSearchSubmit"
            @reset="handleRightSearchReset"
            class="search-form">
          </BasicForm>
        </div>
        <div class="xunda-content-wrapper-content bg-white section-card">
          <BasicTable @register="registerTableRight" ref="tableRightRef">
            <template #tableTitle>
              <div class="section-title">
                <span class="title-text">未审核报名数据</span>
              </div>
              <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="handleBatchAudit()" :disabled="semesterId == null || semesterId === ''">
                {{ t('批量审核') }}</a-button
              >
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <TableAction
                  :actions="[
                    {
                      label: t('审核'),
                      color: 'success',
                      disabled: !(record.state === '待审核'),
                      onClick: handleAudit.bind(null, record),
                    },
                  ]" />
              </template>
            </template>
          </BasicTable>
        </div>
      </div>
      <audit-form @register="registerAuditForm" @reload="reloadAll" />
    </div>
  </BasicPopup>
</template>

<script lang="ts" setup>
  import { BasicPopup, usePopup } from '@/components/Popup';
  import { exportThePreRegistrationData, getUserList, batchSimpleAudit, userColumns, userSearchSchemas } from './user';
  import { ref, reactive, onMounted, nextTick } from 'vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { BasicForm, useForm } from '@/components/Form';
  import { BasicTable, useTable, TableAction, TableActionType } from '@/components/Table';
  import { useRoute } from 'vue-router';
  import { cloneDeep } from 'lodash-es';
  import { usePermission } from '@/hooks/web/usePermission';
  import { isArray } from '@/utils/is';
  import CustomUpload from '@/views/study/registration/components/CustomUpload/index.vue';
  import { downloadByUrl } from '@/utils/file/download';
  import { useModal } from '@/components/Modal';
  import AuditForm from '@/views/study/registration/auditForm.vue';

  const [registerPopup, { openPopup, setPopupProps }] = usePopup();
  const [registerAuditForm, { openModal: openAuditModal }] = useModal();

  const emit = defineEmits(['reload']);
  defineExpose({ init });

  const { hasFormP } = usePermission();
  const route = useRoute();
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const tableLeftRef = ref<Nullable<TableActionType>>(null);
  const tableRightRef = ref<Nullable<TableActionType>>(null);
  const cacheList = ref<any>([]);
  const semesterId = ref('');
  const defaultSearchInfo = {
    menuId: route.meta.modelId as string,
    moduleId: '***********', //模块ID
    superQueryJson: '',
    dataType: 0,
    semesterId: semesterId.value,
  };
  const searchLeftInfo = reactive({
    ...cloneDeep(defaultSearchInfo),
  });

  const [registerSearchForm] = useForm({
    baseColProps: { span: 8 },
    showActionButtonGroup: true,
    showAdvancedButton: true,
    autoAdvancedLine: 2,
    compact: true,
    submitOnReset: true,
  });
  const tableLeftGetList = params => {
    params.semesterId = semesterId.value;
    params.auditFlag = true;
    return getUserList(params);
  };

  const [registerTableLeft] = useTable({
    api: tableLeftGetList,
    columns: userColumns,
    immediate: false,
    searchInfo: searchLeftInfo,
    clickToRowSelect: false,
    afterFetch: data => {
      const list = data.map(o => ({
        ...o,
      }));
      cacheList.value = cloneDeep(list);
      return list;
    },
    actionColumn: {
      width: 80,
      title: t('common.actionText', '操作'),
      dataIndex: 'action',
      fixed: 'right',
    },
    rowSelection: {
      type: 'checkbox',
      getCheckboxProps: record => ({
        disabled: record.top,
      }),
    },
  });

  function handleLeftSearchReset() {
    tableLeftRef.value?.clearSelectedRowKeys();
  }

  function handleLeftSearchSubmit(data) {
    let obj = {
      ...defaultSearchInfo,
      superQueryJson: searchLeftInfo.superQueryJson,
      ...data,
    };
    Object.keys(searchLeftInfo).map(key => {
      delete searchLeftInfo[key];
    });
    for (let [key, value] of Object.entries(obj)) {
      searchLeftInfo[key.replaceAll('-', '_')] = value;
    }
    console.log(searchLeftInfo);
    tableLeftRef.value?.reload({ page: 1 });
  }

  const searchRightInfo = reactive({
    ...cloneDeep(defaultSearchInfo),
  });

  const tableRightParams = ref<any>({});

  const tableRightGetList = params => {
    params.semesterId = semesterId.value;
    params.auditFlag = false;
    tableRightParams.value = params;
    return getUserList(params);
  };
  const [registerTableRight] = useTable({
    api: tableRightGetList,
    columns: userColumns,
    immediate: false,
    searchInfo: searchRightInfo,
    clickToRowSelect: false,
    afterFetch: data => {
      const list = data.map(o => ({
        ...o,
      }));
      cacheList.value = cloneDeep(list);
      return list;
    },
    actionColumn: {
      width: 80,
      title: t('common.actionText', '操作'),
      dataIndex: 'action',
      fixed: 'right',
    },
    rowSelection: {
      type: 'checkbox',
      getCheckboxProps: record => ({
        disabled: record.top,
      }),
    },
  });

  function handleRightSearchReset() {
    tableRightRef.value?.clearSelectedRowKeys();
  }

  function handleRightSearchSubmit(data) {
    let obj = {
      ...defaultSearchInfo,
      superQueryJson: searchRightInfo.superQueryJson,
      ...data,
    };
    Object.keys(searchRightInfo).map(key => {
      delete searchRightInfo[key];
    });
    for (let [key, value] of Object.entries(obj)) {
      searchRightInfo[key.replaceAll('-', '_')] = value;
    }
    console.log(searchRightInfo);
    tableRightRef.value?.reload({ page: 1 });
  }

  function handleUploadSuccess(data) {
    createMessage.success('上传成功', data);
    reloadAll();
  }
  function handleUploadError(e) {
    var data = JSON.parse(e.data?.toString() || '');
    const { createErrorModal } = useMessage();
    if (isArray(data)) {
      createErrorModal({
        title: '上传失败',
        content: data.join('<br>'),
      });
    } else {
      createErrorModal({
        title: '上传失败',
        content: data,
      });
    }
  }

  /**
   * 重新加载所有表格数据
   *
   * 该函数将清除左侧和右侧表格的选中行，并将两个表格重新加载到第一页。
   */
  function reloadAll() {
    tableLeftRef.value?.clearSelectedRowKeys();
    tableRightRef.value?.clearSelectedRowKeys();
    tableLeftRef.value?.reload({ page: 1 });
    tableRightRef.value?.reload({ page: 1 });
  }

  /**
   * 对指定查询条件的数据进行批量审核处理
   *
   * @param query 查询条件
   */
  function doAdd(query) {
    tableRightRef.value?.setLoading(true);
    batchSimpleAudit(query)
      .then(res => {
        createMessage.success(res.msg);
        reloadAll();
      })
      .finally(() => {
        tableRightRef.value?.setLoading(false);
      });
  }

  // 打开审核弹窗
  function handleAudit(record) {
    openAuditModal(true, { record, registrationParams: tableRightParams.value });
  }

  /**
   * 处理批量审核函数
   *
   * @returns void
   */
  function handleBatchAudit() {
    const ids = tableRightRef.value?.getSelectRowKeys() ?? [];
    if (!ids.length) return createMessage.error('请至少选择一条数据');
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要将选中的数据加入左侧班级吗？',
      onOk: () => {
        const query = { registrationIds: ids, semesterId: semesterId.value, auditFlag: true };
        doAdd(query);
      },
    });
  }
  /**
   * 执行删除操作
   *
   * @param query 删除操作的查询参数
   */
  function doRemove(query) {
    tableLeftRef.value?.setLoading(true);
    batchSimpleAudit(query)
      .then(res => {
        createMessage.success(res.msg);
        reloadAll();
      })
      .finally(() => {
        tableLeftRef.value?.setLoading(false);
      });
  }

  function handleRemove(id) {
    const query = { registrationIds: [id], semesterId: semesterId.value };
    doRemove(query);
  }

  /**
   * 批量取消审核
   */
  function handleBatchRemoveAudit() {
    const ids = tableLeftRef.value?.getSelectRowKeys() ?? [];
    if (!ids.length) return createMessage.error('请至少选择一条数据');
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要批量取消审核吗, 是否继续?',
      onOk: () => {
        const query = { registrationIds: ids, semesterId: semesterId.value };
        doRemove(query);
      },
    });
  }

  function init(data) {
    semesterId.value = data.id;
    openPopup();
    nextTick(() => {
      reloadAll();
    });
  }

  /**
   * 导出预注册数据处理器
   *
   * 调用导出预注册数据接口，并根据返回结果下载文件
   */
  function exportThePreRegistrationDataHandler() {
    exportThePreRegistrationData({ semesterId: semesterId.value }).then(async res => {
      if (res.data && res.data.url) {
        await downloadByUrl({ url: res.data.url, fileName: res.data.name });
      }
    });
  }

  function exportNoticeHandler() {
    setPopupProps({ loading: true });
    setTimeout(() => {
      createMessage.success('正在导出');
      setPopupProps({ loading: false });
    }, 1000);
  }

  // 设置查询表单
  function setSearchSchema() {}

  onMounted(() => {
    setSearchSchema();
  });
</script>
<style lang="less" scoped>
  .xunda-content-wrapper {
    display: flex;
    height: 100%;

    &-left {
      width: 300px;
      margin-right: 16px;
      background-color: #fff;
      border-radius: 2px;
    }
  }

  .section-card {
    border-radius: 6px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
    padding: 16px;
    margin-bottom: 16px;

    &:hover {
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    }
  }

  .section-title {
    display: flex;
    align-items: center;

    .title-text {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      position: relative;
      padding-left: 10px;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 16px;
        background-color: #1890ff;
        border-radius: 2px;
      }
    }
  }
</style>
