import { useI18n } from '@/hooks/web/useI18n';
import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();

// 基础Api
export const semesterApi = '/api/plan/semester';

export function getNoticeData(data) {
  return defHttp.post({ url: semesterApi + `/getNoticeData`, data });
}
export function setNoticeData(data) {
  return defHttp.post({ url: semesterApi + `/setNoticeData`, data });
}

export function printNoticeDataAsync(id, type) {
  // 调用defHttp的post方法，发送请求
  return defHttp.post({ url: semesterApi + `/printNoticeData/` + id + '/' + type });
  // 请求URL由semesterApi、`/printNoticeData/`和id拼接而成
}
