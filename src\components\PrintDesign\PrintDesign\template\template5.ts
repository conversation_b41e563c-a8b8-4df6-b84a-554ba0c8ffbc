export default [
  {
    options: {
      left: 205.5,
      top: 39,
      height: 33,
      width: 190.5,
      title: '解除劳动合同协议',
      coordinateSync: false,
      widthHeightSync: false,
      fontSize: 21,
      fontWeight: 'bold',
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      right: 396,
      bottom: 51,
      vCenter: 300.75,
      hCenter: 34.5,
      fontFamily: 'SimSun',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 46.5,
      top: 94.5,
      height: 16.5,
      width: 213,
      title: '甲方（用人单位）：',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 257.99146270751953,
      bottom: 65.24574279785156,
      vCenter: 151.49146270751953,
      hCenter: 56.99574279785156,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 321,
      top: 94.5,
      height: 16.5,
      width: 213,
      title: '法定代表人：',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 257.99146270751953,
      bottom: 65.24574279785156,
      vCenter: 151.49146270751953,
      hCenter: 56.99574279785156,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 321,
      top: 126,
      height: 16.5,
      width: 213,
      title: '身份证号码：',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 258.2428894042969,
      bottom: 85.74715805053711,
      vCenter: 151.74288940429688,
      hCenter: 77.49715805053711,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 46.5,
      top: 126,
      height: 16.5,
      width: 213,
      title: '乙方（职工）：',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 258.2428894042969,
      bottom: 85.74715805053711,
      vCenter: 151.74288940429688,
      hCenter: 77.49715805053711,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 46.5,
      top: 157.5,
      height: 384,
      width: 487.5,
      title:
        '依据《劳动合同法》第三十六条的相关规定，在平等自愿的基础上，经甲乙双方协商一致解除劳动合同关系，达成如下协议:\n一、双方一致同意于\n二、乙方应按照公司离职规定办理工作交接及离职手续。\n三、乙方薪资结算至\n四、甲方为乙方缴纳的社会保险及公积金至\n五、 甲方同意向乙方支付经济补偿金共计人民币\n\n\n六、乙方承诺:\n1、已确认与甲方就结算数据达成一致，自收到上述款项后，在甲方劳动关系存续之间的全部工资待遇(包括但不限于工资、奖金、福利、津贴、补贴、加班费、社保公积金、经济补偿金)等全部权利义务均已结清，双方再无任何争议。\n2、乙方承诺不再对本协议内容及与甲方的劳动关系事项提出任何仲裁、诉讼请求或者其它要求。乙方放弃以任何形式向甲方主张任何权利。\n3、乙方离开甲方后，不得向任何第三方泄漏甲方的商业秘密(包括甲方客户的商业秘密、信息)，不从事任何损害甲方利益和名誉的活动。如乙方违反上述承诺，将承担相应的法律责任及赔偿给甲方所造成的全部损失。\n七、任何一方均不得向第三方泄露本协议书的内容。否则对方有权追究泄秘方的法律和经济赔偿责任。\n八、本协议一式二份，甲、乙双方各执一份，自甲、乙双方签字、盖章生效。\n',
      right: 534,
      bottom: 553.5,
      vCenter: 290.25,
      hCenter: 345,
      coordinateSync: false,
      widthHeightSync: false,
      fontSize: 11.25,
      lineHeight: 18,
      longTextIndent: 22.5,
      fontFamily: 'SimSun',
    },
    printElementType: { title: '长文本', type: 'longText' },
  },
  {
    options: {
      left: 300,
      top: 198,
      height: 13.5,
      width: 231,
      title: '解除劳动合同，双方的劳动权利义务终止。',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      right: 530.25,
      bottom: 180.75,
      vCenter: 414.75,
      hCenter: 174,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 174,
      top: 198,
      height: 12,
      width: 120,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      hideTitle: true,
      fontSize: 12,
      borderBottom: 'solid',
      right: 295.5,
      bottom: 179.25,
      vCenter: 235.5,
      hCenter: 174.375,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 336,
      top: 232.5,
      height: 12,
      width: 120,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      hideTitle: true,
      fontSize: 12,
      borderBottom: 'solid',
      right: 454.74609375,
      bottom: 224.49609375,
      vCenter: 394.74609375,
      hCenter: 218.49609375,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 298.5,
      top: 232.5,
      height: 13.5,
      width: 37.5,
      title: '，将于',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      right: 530.25,
      bottom: 180.75,
      vCenter: 414.75,
      hCenter: 174,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 460.5,
      top: 232.5,
      height: 13.5,
      width: 37.5,
      title: '发放。',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      right: 497.49609375,
      bottom: 225.99609375,
      vCenter: 478.74609375,
      hCenter: 219.24609375,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 174,
      top: 232.5,
      height: 12,
      width: 120,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      hideTitle: true,
      fontSize: 12,
      borderBottom: 'solid',
      right: 293.25,
      bottom: 222.75,
      vCenter: 233.25,
      hCenter: 216.75,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 411,
      top: 252,
      height: 13.5,
      width: 37.5,
      title: '。',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      right: 448.74609375,
      bottom: 244.74609375,
      vCenter: 429.99609375,
      hCenter: 237.99609375,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 288,
      top: 252,
      height: 12,
      width: 120,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      hideTitle: true,
      fontSize: 12,
      borderBottom: 'solid',
      right: 407.49609375,
      bottom: 243.24609375,
      vCenter: 347.49609375,
      hCenter: 237.24609375,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 441,
      top: 268.5,
      height: 12,
      width: 60,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      hideTitle: true,
      fontSize: 12,
      borderBottom: 'solid',
      right: 499.5,
      bottom: 261,
      vCenter: 469.5,
      hCenter: 255,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 319.5,
      top: 270,
      height: 12,
      width: 50,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      hideTitle: true,
      fontSize: 12,
      borderBottom: 'solid',
      right: 435.24609375,
      bottom: 261.24609375,
      vCenter: 375.24609375,
      hCenter: 255.24609375,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 369,
      top: 270,
      height: 13.5,
      width: 72,
      title: '元，（大写：',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      right: 441.99609375,
      bottom: 260.49609375,
      vCenter: 423.24609375,
      hCenter: 253.74609375,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 501,
      top: 270,
      height: 13.5,
      width: 37.5,
      title: '整），',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      right: 539.25,
      bottom: 261.75,
      vCenter: 521.25,
      hCenter: 255,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 285,
      top: 286.5,
      height: 12,
      width: 112,
      title: ' ',
      coordinateSync: false,
      widthHeightSync: false,
      hideTitle: true,
      fontSize: 12,
      borderBottom: 'solid',
      right: 429.24609375,
      bottom: 279.99609375,
      vCenter: 369.24609375,
      hCenter: 273.99609375,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 399,
      top: 286.5,
      height: 13.5,
      width: 135,
      title: '一次性支付给乙方，乙方',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      right: 502.5,
      bottom: 282,
      vCenter: 467.25,
      hCenter: 275.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 46.5,
      top: 286.5,
      height: 13.5,
      width: 240,
      title: '在乙方完成工作交接并办理完离职手续后，于',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      right: 309,
      bottom: 279,
      vCenter: 177.75,
      hCenter: 272.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 46.5,
      top: 304.5,
      height: 13.5,
      width: 99,
      title: '予以接受。',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      right: 150.99609375,
      bottom: 302.49609375,
      vCenter: 99.24609375,
      hCenter: 295.74609375,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 330,
      top: 583.5,
      height: 16.5,
      width: 204,
      title: '乙方：（签名）',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 543,
      bottom: 585.75,
      vCenter: 436.5,
      hCenter: 577.5,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 48,
      top: 583.5,
      height: 16.5,
      width: 213,
      title: '甲方：（盖章）',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 257.99146270751953,
      bottom: 65.24574279785156,
      vCenter: 151.49146270751953,
      hCenter: 56.99574279785156,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 48,
      top: 624,
      height: 16.5,
      width: 213,
      title: '日期：',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 420.75,
      bottom: 615,
      vCenter: 324,
      hCenter: 606.75,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 330,
      top: 624,
      height: 16.5,
      width: 202.5,
      title: '日期：',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 260.99146270751953,
      bottom: 639.7435684204102,
      vCenter: 154.49146270751953,
      hCenter: 631.4935684204102,
    },
    printElementType: { title: '文本', type: 'text' },
  },
];
