import { FormSchema } from "@/components/Form";
import { useI18n } from "@/hooks/web/useI18n";
import { BasicColumn } from "@/components/Table/src/types/table";
import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from "@/utils/xunda";

// 基础Api
export const planHotelApi = '/api/plan/hotel';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: planHotelApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: planHotelApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: planHotelApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: planHotelApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: planHotelApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: planHotelApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: planHotelApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: planHotelApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: planHotelApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: planHotelApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: planHotelApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: planHotelApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: planHotelApi + `/getForSelect`, data });
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'name',
    label: t('名称'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
];


/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('名称'),
    dataIndex: 'name',
    sorter: true,
    width: 120,
  },
  {
    title: t('地址'),
    dataIndex: 'address',
    width: 120,
  },
  {
    title: t('描述'),
    dataIndex: 'description',
    width: 120,
  },
  // {
  //   title: t('经度'),
  //   dataIndex: 'latitude',
  //   width: 120,
  // },
  // {
  //   title: t('纬度'),
  //   dataIndex: 'longtitude',
  //   width: 120,
  // },
  // {
  //   title: t('图片'),
  //   dataIndex: 'picture',
  //   width: 120,
  // },
];
