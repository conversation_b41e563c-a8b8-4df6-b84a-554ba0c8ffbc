<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    width="600px"
    :minHeight="100"
    :cancelText="t('common.cancelText', '取消')"
    :okText="t('common.okText', '确定')"
    @ok="handleSubmit"
    :closeFunc="onClose">
    <template #title>
      <a-space :size="10">
        <div class="text-16px font-medium">{{ title }}</div>
        <a-space-compact size="small" block v-if="dataForm.id">
          <a-tooltip :title="t('common.prevRecord')">
            <a-button size="small" :disabled="getPrevDisabled" @click="handlePrev">
              <i class="icon-ym icon-ym-caret-left text-10px"></i>
            </a-button>
          </a-tooltip>
          <a-tooltip :title="t('common.nextRecord')">
            <a-button size="small" :disabled="getNextDisabled" @click="handleNext">
              <i class="icon-ym icon-ym-caret-right text-10px"></i>
            </a-button>
          </a-tooltip>
        </a-space-compact>
      </a-space>
    </template>
    <template #insertFooter>
      <div class="loat-left mt-5px">
        <XundaCheckboxSingle v-model:value="submitType" :label="continueText" />
      </div>
    </template>
    <a-row class="dynamic-form">
      <a-form
        :colon="false"
        size="middle"
        layout="horizontal"
        labelAlign="left"
        :labelCol="{ style: { width: '100px' } }"
        :model="dataForm"
        :rules="dataRule"
        ref="formRef">
        <a-row :gutter="15">
          <!-- 具体表单 -->
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="semesterId">
              <template #label>计划 </template>
              <XundaSelect
                v-model:value="dataForm.semesterId"
                :disabled="false"
                @change="changeData('semesterId', -1)"
                placeholder="请输入计划"
                :allowClear="true"
                :style="{ width: '100%' }"
                :showSearch="false"
                :options="optionsObj.semesterIdOptions"
                :fieldNames="optionsObj.semesterIdProps">
              </XundaSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="userId">
              <template #label>用户 </template>
              <XundaSelect
                v-model:value="dataForm.userId"
                :disabled="false"
                @change="changeData('userId', -1)"
                placeholder="请输入用户"
                :allowClear="true"
                :style="{ width: '100%' }"
                :showSearch="false"
                :options="optionsObj.userIdOptions"
                :fieldNames="optionsObj.userIdProps">
              </XundaSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="name">
              <template #label>姓名 </template>
              <XundaInput
                v-model:value="dataForm.name"
                :disabled="false"
                @change="changeData('name', -1)"
                placeholder="请输入姓名"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="12" class="ant-col-item" :hidden="false">
            <a-form-item name="firstName">
              <template #label>名 </template>
              <XundaInput
                v-model:value="dataForm.firstName"
                :disabled="false"
                @change="changeData('firstName', -1)"
                placeholder="名"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="lastName">
              <template #label>姓 </template>
              <XundaInput
                v-model:value="dataForm.lastName"
                :disabled="false"
                @change="changeData('lastName', -1)"
                placeholder="姓"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="englishName">
              <template #label>英文名 </template>
              <XundaInput
                v-model:value="dataForm.englishName"
                :disabled="false"
                @change="changeData('englishName', -1)"
                placeholder="英文名"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="studentType">
              <template #label>学员类型 </template>
              <XundaSelect
                v-model:value="dataForm.studentType"
                :disabled="false"
                @change="changeData('studentType', -1)"
                placeholder="学员类型"
                :allowClear="true"
                :style="{ width: '100%' }"
                :showSearch="false"
                :options="optionsObj.studentTypeOptions"
                :fieldNames="optionsObj.studentTypeProps">
              </XundaSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="sex">
              <template #label>性别 </template>
              <XundaSelect
                v-model:value="dataForm.sex"
                :disabled="false"
                @change="changeData('sex', -1)"
                placeholder="请选择性别"
                :allowClear="true"
                :style="{ width: '100%' }"
                :showSearch="false"
                :options="optionsObj.sexOptions"
                :fieldNames="optionsObj.sexProps">
              </XundaSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="birthday">
              <template #label>生日 </template>
              <XundaDatePicker
                v-model:value="dataForm.birthday"
                @change="changeData('birthday', -1)"
                placeholder="请选择生日"
                :allowClear="true"
                :style="{ width: '100%' }"
                format="yyyy-MM-dd"
                :startTime="xundaUtils.getRelationDate(true, 1, 1, '1746547200000', '')"
                :endTime="xundaUtils.getRelationDate(true, 1, 1, '1746633600000', '')">
              </XundaDatePicker>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="nationality">
              <template #label>国籍 </template>
              <XundaInput
                v-model:value="dataForm.nationality"
                :disabled="false"
                @change="changeData('nationality', -1)"
                placeholder="请输入国籍"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="idCardType">
              <template #label>证件类型 </template>
              <XundaSelect
                v-model:value="dataForm.idCardType"
                @change="changeData('idCardType', -1)"
                placeholder="请选择证件类型"
                :templateJson="state.interfaceRes.idCardType"
                :allowClear="true"
                :style="{ width: '100%' }"
                :showSearch="true"
                :options="optionsObj.idCardTypeOptions"
                :fieldNames="optionsObj.idCardTypeProps">
              </XundaSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="idNo">
              <template #label>证件号 </template>
              <XundaInput
                v-model:value="dataForm.idNo"
                :disabled="false"
                @change="changeData('idNo', -1)"
                placeholder="请输入证件号"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="passportNo">
              <template #label>护照号 </template>
              <XundaInput
                v-model:value="dataForm.passportNo"
                :disabled="false"
                @change="changeData('passportNo', -1)"
                placeholder="护照号"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="email">
              <template #label>邮箱 </template>
              <XundaInput
                v-model:value="dataForm.email"
                :disabled="false"
                @change="changeData('email', -1)"
                placeholder="邮箱"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="phone1">
              <template #label>电话 </template>
              <XundaInput
                v-model:value="dataForm.phone1"
                :disabled="false"
                @change="changeData('phone1', -1)"
                placeholder="电话"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="phone2">
              <template #label>备用电话 </template>
              <XundaInput
                v-model:value="dataForm.phone2"
                :disabled="false"
                @change="changeData('phone2', -1)"
                placeholder="备用电话"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="emergencyPhone">
              <template #label>紧急联系电话 </template>
              <XundaInput
                v-model:value="dataForm.emergencyPhone"
                :disabled="false"
                @change="changeData('emergencyPhone', -1)"
                placeholder="紧急联系电话"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="school">
              <template #label>就读/毕业学校 </template>
              <XundaInput
                v-model:value="dataForm.school"
                :disabled="false"
                @change="changeData('school', -1)"
                placeholder="就读/毕业学校"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="major">
              <template #label>专业 </template>
              <XundaInput
                v-model:value="dataForm.major"
                :disabled="false"
                @change="changeData('major', -1)"
                placeholder="专业"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="note">
              <template #label>备注 </template>
              <XundaTextarea
                v-model:value="dataForm.note"
                @change="changeData('note', -1)"
                placeholder="备注"
                :allowClear="true"
                :style="{ width: '100%' }"
                :autoSize="{ minRows: 4, maxRows: 4 }"
                :showCount="true">
              </XundaTextarea>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="flowId">
              <template #label>流程 </template>
              <XundaInput
                v-model:value="dataForm.flowId"
                :disabled="false"
                @change="changeData('flowId', -1)"
                placeholder="流程"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="flowTaskId">
              <template #label>流程实例 </template>
              <XundaInput
                v-model:value="dataForm.flowTaskId"
                :disabled="false"
                @change="changeData('flowTaskId', -1)"
                placeholder="流程实例"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="attachment">
              <template #label>附件 </template>
              <XundaUploadFile
                v-model:value="dataForm.attachment"
                @change="changeData('attachment', -1)"
                :fileSize="10"
                sizeUnit="MB"
                :limit="9"
                pathType="selfPath"
                :sortRule="[3]"
                timeFormat="YYYY"
                folder="attachment"
                buttonText="点击上传">
              </XundaUploadFile>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="planTemplateId">
              <template #label>附加信息模板 </template>
              <XundaInput
                v-model:value="dataForm.planTemplateId"
                :disabled="false"
                @change="changeData('planTemplateId', -1)"
                placeholder="附加信息模板"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="applyFormValue">
              <template #label>扩展信息 </template>
              <XundaInput
                v-model:value="dataForm.applyFormValue"
                :disabled="false"
                @change="changeData('applyFormValue', -1)"
                placeholder="扩展信息"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="photo">
              <template #label>照片 </template>
              <XundaUploadImg
                v-model:value="dataForm.photo"
                @change="changeData('photo', -1)"
                :fileSize="10"
                sizeUnit="MB"
                :limit="9"
                pathType="selfPath"
                :sortRule="[3]"
                timeFormat="YYYY"
                folder="photo"
                tipText="请上传图片">
              </XundaUploadImg>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="totalFee">
              <template #label>总费用 </template>
              <XundaInputNumber
                v-model:value="dataForm.totalFee"
                :disabled="false"
                @change="changeData('totalFee', -1)"
                placeholder="请输入总费用"
                :allowClear="true"
                :style="{ width: '100%' }"
                :step="1"
                :precision="2"
                isAmountChinese
                thousands
                :controls="true">
              </XundaInputNumber>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="policyDedude">
              <template #label>政策优惠 </template>
              <XundaInputNumber
                v-model:value="dataForm.policyDedude"
                :disabled="false"
                @change="changeData('policyDedude', -1)"
                placeholder="请输入政策优惠"
                :allowClear="true"
                :style="{ width: '100%' }"
                :step="1"
                :precision="2"
                isAmountChinese
                thousands
                :controls="true">
              </XundaInputNumber>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="policyDeduceDetial">
              <template #label>政策优惠明细 </template>
              <XundaTextarea
                v-model:value="dataForm.policyDeduceDetial"
                :disabled="false"
                @change="changeData('policyDeduceDetial', -1)"
                placeholder="请输入政策优惠明细"
                :allowClear="true"
                :style="{ width: '100%' }"
                :autoSize="{ minRows: 4, maxRows: 4 }"
                :showCount="true">
              </XundaTextarea>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="shouldPay">
              <template #label>应缴 </template>
              <XundaInputNumber
                v-model:value="dataForm.shouldPay"
                :disabled="false"
                @change="changeData('shouldPay', -1)"
                placeholder="请输入"
                :style="{ width: '100%' }"
                :step="1"
                :precision="2"
                isAmountChinese
                thousands
                :controls="true">
              </XundaInputNumber>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="payIn">
              <template #label>实缴 </template>
              <XundaInputNumber
                v-model:value="dataForm.payIn"
                :disabled="false"
                @change="changeData('payIn', -1)"
                placeholder="请输入"
                :style="{ width: '100%' }"
                :step="1"
                :precision="2"
                isAmountChinese
                thousands
                :controls="true">
              </XundaInputNumber>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="payRetrun">
              <template #label>退费 </template>
              <XundaInputNumber
                v-model:value="dataForm.payRetrun"
                :disabled="false"
                @change="changeData('payRetrun', -1)"
                placeholder="请输入"
                :style="{ width: '100%' }"
                :step="1"
                :precision="2"
                isAmountChinese
                thousands
                :controls="true">
              </XundaInputNumber>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="payDiff">
              <template #label>费用差额 </template>
              <XundaInputNumber
                v-model:value="dataForm.payDiff"
                :disabled="false"
                @change="changeData('payDiff', -1)"
                placeholder="请输入"
                :style="{ width: '100%' }"
                :step="1"
                :precision="2"
                isAmountChinese
                thousands
                :controls="true">
              </XundaInputNumber>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="payState">
              <template #label>支付状态 </template>
              <XundaSelect
                v-model:value="dataForm.payState"
                :disabled="false"
                @change="changeData('payState', -1)"
                placeholder="请选择支付状态"
                :allowClear="true"
                :style="{ width: '100%' }"
                showSearch
                :options="optionsObj.payStateOptions"
                :fieldNames="optionsObj.payStateFieldNames">
              </XundaSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="sign">
              <template #label>签名图片 </template>
              <XundaUploadImg
                v-model:value="dataForm.sign"
                :disabled="false"
                @change="changeData('sign', -1)"
                :fileSize="10"
                sizeUnit="MB"
                :limit="1"
                pathType="selfPath"
                :sortRule="[3]"
                timeFormat="YYYY"
                folder="sign"
                tipText="请上传图片">
              </XundaUploadImg>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="version">
              <template #label>报名模板版本 </template>
              <XundaInput
                v-model:value="dataForm.version"
                :disabled="false"
                @change="changeData('version', -1)"
                placeholder="请输入报名模板版本"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="state">
              <template #label>学生状态 </template>
              <XundaSelect
                v-model:value="dataForm.state"
                :disabled="false"
                @change="changeData('state', -1)"
                placeholder="请选择学生状态"
                :templateJson="state.interfaceRes.state"
                :allowClear="true"
                :style="{ width: '100%' }"
                :showSearch="true"
                :options="optionsObj.stateOptions"
                :fieldNames="optionsObj.stateProps">
              </XundaSelect>
            </a-form-item>
          </a-col>
          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { create, update, getInfo } from '@/views/study/registration';
  import { reactive, toRefs, nextTick, ref, unref, computed, inject } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useUserStore } from '@/store/modules/user';
  import type { FormInstance } from 'ant-design-vue';
  import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
  // 表单权限
  import { usePermission } from '@/hooks/web/usePermission';
  import { xundaUtils } from '@/utils/xunda';
  import { getSemesterForSelect } from '../../plan/semester/components/feeConfig/index';

  interface State {
    dataForm: any;
    tableRows: any;
    dataRule: any;
    optionsObj: any;
    childIndex: any;
    isEdit: any;
    interfaceRes: any;
    //可选范围默认值
    ableAll: any;
    //掩码配置
    maskConfig: any;
    //定位属性
    locationScope: any;
    title: string;
    continueText: string;
    allList: any[];
    currIndex: number;
    isContinue: boolean;
    submitType: number;
    showContinueBtn: boolean;
  }

  const emit = defineEmits(['reload']);
  const getLeftTreeActiveInfo: (() => any) | null = inject('getLeftTreeActiveInfo', null);
  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const [registerModal, { openModal, setModalProps }] = useModal();
  const formRef = ref<FormInstance>();
  const state = reactive<State>({
    dataForm: {
      id: '',
      semesterId: '',
      userId: '',
      name: '',
      firstName: '',
      lastName: '',
      englishName: '',
      studentType: '',
      sex: '',
      birthday: undefined,
      nationality: '',
      idCardType: '',
      idNo: '',
      passportNo: '',
      email: '',
      phone1: '',
      phone2: '',
      emergencyPhone: '',
      school: '',
      major: '',
      note: '',
      flowId: '',
      flowTaskId: '',
      attachment: '',
      planTemplateId: '',
      applyFormValue: '',
      photo: '',
      totalFee: undefined,
      policyDedude: undefined,
      policyDeduceDetial: '',
      shouldPay: undefined,
      payIn: undefined,
      payRetrun: undefined,
      payDiff: undefined,
      payState: '',
      sign: '',
      version: '',
      state: '',
    },
    tableRows: {},
    dataRule: {
      id: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '主键不能为空'),
          trigger: 'blur',
        },
      ],
      semesterId: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '计划不能为空'),
          trigger: 'blur',
        },
      ],
      userId: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '用户不能为空'),
          trigger: 'blur',
        },
      ],
      name: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '姓名不能为空'),
          trigger: 'blur',
        },
      ],
      sex: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '性别不能为空'),
          trigger: 'blur',
        },
      ],
      nationality: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '国籍不能为空'),
          trigger: 'blur',
        },
      ],
      idCardType: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '证件类型不能为空'),
          trigger: 'blur',
        },
      ],
      idNo: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '证件号不能为空'),
          trigger: 'blur',
        },
      ],
      totalFee: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '总费用不能为空'),
          trigger: 'blur',
        },
      ],
      policyDedude: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '政策优惠不能为空'),
          trigger: 'blur',
        },
      ],
      policyDeduceDetial: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '政策优惠明细不能为空'),
          trigger: 'blur',
        },
      ],
      shouldPay: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '应缴不能为空'),
          trigger: 'blur',
        },
      ],
      payIn: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '实缴不能为空'),
          trigger: 'blur',
        },
      ],
      payRetrun: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '退费不能为空'),
          trigger: 'blur',
        },
      ],
      payDiff: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '费用差额不能为空'),
          trigger: 'blur',
        },
      ],
      payState: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '支付状态不能为空'),
          trigger: 'blur',
        },
      ],
      version: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '报名模板版本不能为空'),
          trigger: 'blur',
        },
      ],
      state: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '学生状态不能为空'),
          trigger: 'blur',
        },
      ],
    },
    optionsObj: {
      //选项配置
      defaultProps: { label: 'fullName', value: 'enCode' }, // 默认下拉选择键值
    },
    childIndex: -1,
    isEdit: false,
    interfaceRes: {},
    //可选范围默认值
    ableAll: {},

    //掩码配置
    maskConfig: {},
    //定位属性
    locationScope: {
      faddressDetail: [],
    },
    title: '',
    continueText: '',
    allList: [],
    currIndex: 0,
    isContinue: false,
    submitType: 0,
    showContinueBtn: true,
  });
  const { title, continueText, showContinueBtn, dataRule, dataForm, optionsObj, ableAll, maskConfig, submitType } = toRefs(state);

  const getPrevDisabled = computed(() => state.currIndex === 0);
  const getNextDisabled = computed(() => state.currIndex === state.allList.length - 1);
  // 表单权限
  const { hasFormP } = usePermission();

  defineExpose({ init });

  function init(data) {
    state.submitType = 0;
    state.isContinue = false;
    state.title = !data.id ? t('common.add2Text', '新增') : t('common.editText', '编辑');
    state.continueText = !data.id ? t('common.continueAndAddText', '确定并新增') : t('common.continueText', '确定并继续');
    setFormProps({ continueLoading: false });
    state.dataForm.id = data.id;
    openModal();
    state.allList = data.allList;
    state.currIndex = state.allList.length && data.id ? state.allList.findIndex(item => item.id === data.id) : 0;
    getAllSelectOptions();
    nextTick(() => {
      getForm().resetFields();
      setTimeout(initData, 0);
    });
  }
  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      // 设置默认值
      state.dataForm = {
        id: '',
        semesterId: '',
        userId: '',
        name: '',
        firstName: '',
        lastName: '',
        englishName: '',
        studentType: '',
        sex: '',
        birthday: undefined,
        nationality: '',
        idCardType: '',
        idNo: '',
        passportNo: '',
        email: '',
        phone1: '',
        phone2: '',
        emergencyPhone: '',
        school: '',
        major: '',
        note: '',
        flowId: '',
        flowTaskId: '',
        attachment: '',
        planTemplateId: '',
        applyFormValue: '',
        photo: '',
        totalFee: undefined,
        policyDedude: undefined,
        policyDeduceDetial: '',
        shouldPay: undefined,
        payIn: undefined,
        payRetrun: undefined,
        payDiff: undefined,
        payState: '',
        sign: '',
        version: '',
        state: '',
      };

      if (getLeftTreeActiveInfo) state.dataForm = { ...state.dataForm, ...(getLeftTreeActiveInfo() || {}) };
      state.childIndex = -1;
      changeLoading(false);
    }
  }
  function getForm() {
    const form = unref(formRef);
    if (!form) {
      throw new Error('form is null!');
    }
    return form;
  }
  function getData(id) {
    getInfo(id).then(res => {
      state.dataForm = res.data || {};
      state.dataForm.attachment = res.data.attachment ? JSON.parse(res.data.attachment) : [];
      state.dataForm.photo = res.data.attachment ? JSON.parse(res.data.photo) : [];
      state.childIndex = -1;
      changeLoading(false);
    });
  }
  async function handleSubmit(type) {
    try {
      const values = await getForm()?.validate();
      if (!values) return;
      setFormProps({ confirmLoading: true });
      const formMethod = state.dataForm.id ? update : create;
      state.dataForm.attachment = JSON.stringify(state.dataForm.attachment);
      state.dataForm.photo = JSON.stringify(state.dataForm.photo);
      console.log('values', state.dataForm);
      formMethod(state.dataForm)
        .then(res => {
          createMessage.success(res.msg);
          setFormProps({ confirmLoading: false });
          if (state.submitType == 1) {
            initData();
            state.isContinue = true;
          } else {
            setFormProps({ open: false });
            emit('reload');
          }
        })
        .catch(() => {
          setFormProps({ confirmLoading: false });
        });
    } catch (_) {}
  }

  // 跳转上一个
  function handlePrev() {
    state.currIndex--;
    handleGetNewInfo();
  }

  // 跳转下一个
  function handleNext() {
    state.currIndex++;
    handleGetNewInfo();
  }

  // 重新获取编辑信息
  function handleGetNewInfo() {
    changeLoading(true);
    getForm().resetFields();
    const id = state.allList[state.currIndex].id;
    getData(id);
  }

  function setFormProps(data) {
    setModalProps(data);
  }

  //
  function changeLoading(loading) {
    setModalProps({ loading });
  }

  // 关闭弹窗
  async function onClose() {
    if (state.isContinue) emit('reload');
    return true;
  }

  function changeData(model, index) {
    state.isEdit = false;
    state.childIndex = index;
    for (let key in state.interfaceRes) {
      if (key != model) {
        let faceReList = state.interfaceRes[key];
        for (let i = 0; i < faceReList.length; i++) {
          let relationField = faceReList[i].relationField;
          if (relationField) {
            let modelAll = relationField.split('-');
            let faceMode = '';
            let faceMode2 = modelAll.length == 2 ? modelAll[0].substring(0, modelAll[0].length - 4) + modelAll[1] : '';
            for (let i = 0; i < modelAll.length; i++) {
              faceMode += modelAll[i];
            }
            if (faceMode == model || faceMode2 == model) {
              let options = 'get' + key + 'Options';
              eval(options)(true);
              changeData(key, index);
            }
          }
        }
      }
    }
  }
  function getAllSelectOptions() {
    optionsObj.value.semesterIdProps = { value: 'id', label: 'name' };
    getSemesterForSelect(
      {
        dataType: 1,
      },
      res => {
        optionsObj.value.semesterIdOptions = res.data.list;
      },
    );
  }
</script>
