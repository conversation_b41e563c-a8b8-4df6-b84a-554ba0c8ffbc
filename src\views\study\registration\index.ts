import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';
import { FieldNames } from '../../../components/Tree/src/types/tree';

// 基础Api路径
const registrationApiPath = '/api/study/registration';

// 注册API对象
export const registrationApi = {
  baseUrl: registrationApiPath,
  uploadAttachment: data => {
    return defHttp.post({
      url: registrationApiPath + `/uploadAttachment`,
      data,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
};

// 获取列表
export function getList(data) {
  return defHttp.post({ url: registrationApiPath + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: registrationApiPath, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: registrationApiPath + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: registrationApiPath + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: registrationApiPath + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: registrationApiPath + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: registrationApiPath + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: registrationApiPath + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: registrationApiPath + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: registrationApiPath + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: registrationApiPath + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: registrationApiPath + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: registrationApiPath + `/getForSelect`, data });
}

// 审核
export function auditStudent(data) {
  return defHttp.post({ url: registrationApiPath + `/auditStudent`, data });
}

// 审核
export function createRMessage(data) {
  return defHttp.post({ url: registrationApiPath + `/createMessage`, data });
}

// 审核
export function sendMessage(data) {
  return defHttp.post({ url: registrationApiPath + `/sendMessage`, data });
}

// 审核
export function getSemesterTypeAsync(id) {
  return defHttp.post({ url: registrationApiPath + `/getSemesterType/` + id });
}

// 审核
export function batchDownloadAttachmentAsync(data: { ids: string[] }) {
  return defHttp.post({ url: registrationApiPath + `/batchDownloadAttachment`, data });
}

export function getNextForAuditAsync(data) {
  return defHttp.post({ url: registrationApiPath + `/getNextForAudit`, data });
}

export function batchDownloadAsync(data) {
  return defHttp.post({ url: registrationApiPath + `/batchDownload`, data });
}

export function getBatchImageFileAsync(data) {
  return defHttp.post({ url: registrationApiPath + `/getBatchImageFile`, data });
}

// 上传附件 - 已移至registrationApi对象

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'name',
    label: t('姓名'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
  {
    field: 'auditFlag',
    label: t('审核状态'),
    component: 'Select',
    componentProps: {
      submitOnPressEnter: true,
      options: [
        {
          id: 1,
          fullName: '已审核',
        },
        {
          id: 0,
          fullName: '待审核',
        },
      ],
    },
  },

  {
    field: 'payFlag',
    label: t('缴费状态'),
    component: 'Select',
    componentProps: {
      submitOnPressEnter: true,
      options: [
        {
          id: 1,
          fullName: '已缴费',
        },
        {
          id: 0,
          fullName: '未缴费',
        },
      ],
    },
  },

  {
    field: 'sex',
    label: t('性别'),
    component: 'Select',
    ifShow: false,
    componentProps: {
      submitOnPressEnter: true,
      options: [
        {
          id: '男',
          fullName: '男',
        },
        {
          id: '女',
          fullName: '女',
        },
      ],
    },
  },
  {
    field: 'name',
    label: t('是否参加培训'),
    component: 'Select',
    ifShow: false,
    componentProps: {
      submitOnPressEnter: true,
      options: [
        {
          id: 1,
          fullName: '参加',
        },
        {
          id: 0,
          fullName: '不参加',
        },
      ],
    },
  },

  {
    field: 'name',
    label: t('身份'),
    component: 'Select',
    ifShow: false,
    componentProps: {
      submitOnPressEnter: true,
      options: [
        {
          id: 1,
          fullName: '校内学生',
        },
        {
          id: 2,
          fullName: '校内老师',
        },
        {
          id: 3,
          fullName: '校外人员',
        },
      ],
    },
  },
];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('计划'),
    dataIndex: 'semesterName',
    width: 300,
    fixed: 'left',
  },
  {
    title: t('姓名'),
    dataIndex: 'name',
    width: 120,
    fixed: 'left',
  },
  {
    title: t('电话'),
    dataIndex: 'phone1',
    width: 120,
  },
  {
    title: t('学员类型'),
    dataIndex: 'studentType',
    width: 120,
  },
  {
    title: t('单位'),
    dataIndex: 'unitName',
    width: 120,
  },
  // {
  //   title: t('英文名'),
  //   dataIndex: 'englishName',
  //   width: 120,
  // },
  // {
  //   title: t('性别'),
  //   dataIndex: 'sex',
  //   width: 120,
  // },
  // {
  //   title: t('生日'),
  //   dataIndex: 'birthday',
  //   width: 120,
  //   customRender: ({ record }) => {
  //     return xundaUtils.toDateString(record.birthday);
  //   },
  // },
  // {
  //   title: t('国籍'),
  //   dataIndex: 'nationality',
  //   width: 120,
  // },
  // {
  //   title: t('证件类型'),
  //   dataIndex: 'idCardType',
  //   width: 120,
  // },
  // {
  //   title: t('证件号'),
  //   dataIndex: 'idNo',
  //   width: 200,
  // },
  // {
  //   title: t('护照号'),
  //   dataIndex: 'passportNo',
  //   width: 120,
  // },
  // {
  //   title: t('邮箱'),
  //   dataIndex: 'email',
  //   width: 120,
  // },
  // {
  //   title: t('备用电话'),
  //   dataIndex: 'phone2',
  //   width: 120,
  // },
  // {
  //   title: t('紧急联系电话'),
  //   dataIndex: 'emergencyPhone',
  //   width: 120,
  // },
  // {
  //   title: t('就读/毕业学校'),
  //   dataIndex: 'school',
  //   width: 120,
  // },
  // {
  //   title: t('专业'),
  //   dataIndex: 'major',
  //   width: 120,
  // },
  // {
  //   title: t('备注'),
  //   dataIndex: 'note',
  //   width: 120,
  // },
  // {
  //   title: t('照片'),
  //   dataIndex: 'photo',
  //   width: 120,
  // },
  // {
  //   title: t('总费用'),
  //   dataIndex: 'totalFee',
  //   width: 120,
  // },
  // {
  //   title: t('政策优惠'),
  //   dataIndex: 'policyDedude',
  //   width: 120,
  // },
  // {
  //   title: t('政策优惠明细'),
  //   dataIndex: 'policyDeduceDetial',
  //   width: 120,
  // },
  {
    title: t('应缴'),
    dataIndex: 'shouldPay',
    width: 120,
  },
  {
    title: t('实缴'),
    dataIndex: 'payIn',
    width: 120,
  },
  {
    title: t('待缴'),
    dataIndex: 'payWait',
    width: 120,
  },
  // {
  //   title: t('退费'),
  //   dataIndex: 'payRetrun',
  //   width: 120,
  // },
  // {
  //   title: t('费用差额'),
  //   dataIndex: 'payDiff',
  //   width: 120,
  // },
  {
    title: t('支付状态'),
    dataIndex: 'payState',
    width: 120,
  },

  {
    title: t('附件上传列'),
    dataIndex: 'attachment',
    width: 100,
    fixed: 'right',
  },
  {
    title: t('学生状态'),
    dataIndex: 'state',
    width: 120,
    fixed: 'right',
  },
];

export const semesterApi = '/api/plan/semester';
// 获取列表
export function getSemesterList(data) {
  return defHttp.post({ url: semesterApi + `/getList`, data });
}
/**
 * 表格列配置
 */
export const semesterColumns: BasicColumn[] = [
  {
    title: t('计划名称'),
    dataIndex: 'name',
    width: 120,
  },
];

/**
 * 搜索表单配置
 */
export const semesterSearchSchemas: FormSchema[] = [
  {
    field: 'name',
    label: t('姓名'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
];
