import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';
import { FormSchema } from '@/components/Form';

// 基础Api
export const organizationApi = '/api/ea/organization';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: organizationApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: organizationApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: organizationApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: organizationApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: organizationApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: organizationApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: organizationApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: organizationApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: organizationApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: organizationApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: organizationApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: organizationApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: organizationApi + `/getForSelect`, data });
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'name',
    label: t('单位名称'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
  {
    field: 'code',
    label: t('机构代码'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
  {
    field: 'contactPerson',
    label: t('联系人'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('单位名称'),
    dataIndex: 'name',
    width: 200,
  },
  {
    title: t('机构代码'),
    dataIndex: 'code',
    width: 150,
  },
  {
    title: t('联系人'),
    dataIndex: 'contactPerson',
    width: 100,
  },
  {
    title: t('联系电话'),
    dataIndex: 'contactPhone',
    width: 150,
  },
  {
    title: t('所属地区'),
    dataIndex: 'area',
    width: 150,
  },
  {
    title: t('详细地址'),
    dataIndex: 'address',
    width: 250,
  },
  {
    title: t('备注'),
    dataIndex: 'note',
    width: 200,
  },
  {
    title: t('排序码'),
    dataIndex: 'sortCode',
    width: 100,
  },
];
