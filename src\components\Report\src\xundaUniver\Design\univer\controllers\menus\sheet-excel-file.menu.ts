import { IMenuButtonItem, IMenuSelectorItem, MenuItemType } from '@univerjs/ui';
import {
  XundaSheetsDownloadExcelFileOperation,
  XundaSheetsImportCsvFileOperation,
  XundaSheetsImportExcelFileOperation,
} from '../../commands/operations/sheet-excel-file.operation';
import { XundaCommandIds } from '../../utils/define';

export const XundaSheetsExcelFileMenuFactory = (): IMenuSelectorItem => {
  return {
    id: XundaCommandIds.excelFileOperations,
    type: MenuItemType.SUBITEMS,
    icon: 'DirectExportSingle',
    tooltip: 'xundaSheetExcelFileMenu.tooltip',
  };
};

export const XundaSheetsImportExcelFileMenuFactory = (): IMenuButtonItem => {
  return {
    id: XundaSheetsImportExcelFileOperation.id,
    type: MenuItemType.BUTTON,
    icon: 'UploadSingle',
    tooltip: 'xundaSheetImportExcelFileMenu.tooltip',
    title: 'xundaSheetImportExcelFileMenu.title',
  };
};

export const XundaSheetsDownloadExcelFileMenuFactory = (): IMenuButtonItem => {
  return {
    id: XundaSheetsDownloadExcelFileOperation.id,
    type: MenuItemType.BUTTON,
    icon: 'ExportSingle',
    tooltip: 'xundaSheetDownloadExcelFileMenu.tooltip',
    title: 'xundaSheetDownloadExcelFileMenu.title',
  };
};

export const XundaSheetsImportCsvFileMenuFactory = (): IMenuButtonItem => {
  return {
    id: XundaSheetsImportCsvFileOperation.id,
    type: MenuItemType.BUTTON,
    icon: 'UpperFloorSingle',
    tooltip: 'xundaSheetImportCsvFileMenu.tooltip',
    title: 'xundaSheetImportCsvFileMenu.title',
  };
};
