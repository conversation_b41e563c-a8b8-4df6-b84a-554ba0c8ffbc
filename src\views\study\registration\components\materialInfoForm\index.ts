import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';

export const registrationApi = '/api/study/registration';

export function getMaterialInfoAsync(id) {
  return defHttp.get({ url: registrationApi + `/getMaterialInfo/` + id });
}
