import { IMenuButtonItem, MenuItemType } from '@univerjs/ui';
import { XundaSheetsPreviewOperation } from '../../commands/operations/sheet-preview.operation';

export const XundaSheetsPreviewMenuFactory = (): IMenuButtonItem => {
  return {
    id: XundaSheetsPreviewOperation.id,
    type: MenuItemType.BUTTON,
    icon: 'ViewModeSingle',
    tooltip: 'xundaSheetPreviewMenu.tooltip',
    title: 'xundaSheetPreviewMenu.title',
  };
};
