export interface SingleEntities {
  id: number
  ffullName: string
  foptionsA: string | number
  foptionsB: string | number
  foptionsC: string | number
  foptionsD: string | number
  fanswer: string 
}

export interface MoreEntities {
  id: number
  ffullName: string
  foptionsA: string | number
  foptionsB: string | number
  foptionsC: string | number
  foptionsD: string | number
  fanswer: string | []
}
export interface JudgmentEntities {
  id: number
  ffullName: string
  fanswer: string
}
export interface EssayQuestionEntities {
  id: number
  ffullName: string
  fanswer: string
}