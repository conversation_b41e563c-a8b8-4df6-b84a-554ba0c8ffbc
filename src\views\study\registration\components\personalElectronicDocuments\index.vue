<template>
  <!-- 文件下载按钮 - 仅在学期类别为0002时显示 -->
  <a-card v-if="semesterCatogry === '0002'" title="个人电子资料" :bordered="false" class="doc-card">
    <div class="pdf-container">
      <a-button class="fancy-button-primary" @click="handlerDownload(1)">
        <template #icon><download-outlined /></template>
        {{ t('第一套') }}
      </a-button>

      <a-button class="fancy-button-secondary" @click="handlerDownload(2)">
        <template #icon><download-outlined /></template>
        {{ t('第二套') }}
      </a-button>
    </div>
  </a-card>

  <!-- 卡片容器 -->
  <a-row :gutter="16">
    <!-- 申报表预览 - 仅在学期类别为0001时显示 -->
    <a-col :span="12">
      <a-card title="申报表" :bordered="false" class="doc-card">
        <div v-for="item in dataForm.apply" :key="item.id" class="pdf-container">
          <PdfViewer :file="item" :min-height="800" :show-title="true" :showDownloadButton="true" :showContent="true" />
        </div>
      </a-card>
    </a-col>

    <!-- PDF文件上传区域 -->
    <a-col :span="12">
      <a-card title="申报表（盖章）上传" :bordered="false" class="doc-card">
        <div class="pdf-container">
          <CustomUpload
            v-model:value="dataForm.personalFile"
            accept=".pdf"
            buttonText="上传"
            tipContent="只能上传PDF文件，单个文件大小不超过5MB"
            :fileSize="5"
            sizeUnit="MB"
            customUploadUrl="/api/study/registration/uploaderAttachment"
            :uploadParams="uploadParams"
            @success="uploadSuccess"
            @error="uploadError"
            @loading="changeLoading"
            :showFileList="false"
            :limit="2" />
        </div>
        <!-- 已上传PDF文件预览 -->
        <div v-for="item in dataForm.personalFile" :key="item.id" class="pdf-container">
          <PdfViewer :file="item" :min-height="800" :showDownloadButton="true" :show-delete-button="true" @delete="deleteFile" :showContent="true" />
        </div>
      </a-card>
    </a-col>
  </a-row>
</template>

<script setup lang="ts">
  /**
   * 个人电子文档组件 - 提供PDF文件上传、预览和管理功能
   */
  import { reactive, toRefs, computed } from 'vue';
  import { DownloadOutlined } from '@ant-design/icons-vue';
  import { deleteAttachmentAsync, getFileAsync, getImageFileAsync } from './index';
  import { useI18n } from '@/hooks/web/useI18n';
  import { downloadByUrl } from '@/utils/file/download';
  import { useMessage } from '@/hooks/web/useMessage';
  import PdfViewer from '@/views/study/registration/components/pdfViewer';
  import CustomUpload from '../CustomUpload/index.vue';

  // 消息和确认框组件
  const { createMessage, createConfirm } = useMessage();
  // 国际化
  const { t } = useI18n();

  // 组件属性定义
  const props = defineProps({
    /** 注册ID，用于获取和上传文件 */
    registrationId: {
      type: String,
      default: '',
    },
    /** 学期类别，决定是否显示下载按钮 */
    semesterCatogry: {
      type: String,
      default: '',
    },
  });

  // 初始化响应式状态
  const state = reactive({
    dataForm: {
      personalFile: [],
      apply: [],
    },
  });

  // 解构响应式状态以便在模板中使用
  const { dataForm } = toRefs(state);

  // 定义组件的事件
  const emit = defineEmits(['changeLoading', 'update:semesterCatogry']);

  /**
   * 更改加载状态并触发事件
   */
  const changeLoading = (loading: boolean): void => {
    emit('changeLoading', loading);
  };

  /**
   * 初始化表单数据
   */
  function init(): void {
    state.dataForm.personalFile = [];
    if (props.registrationId) {
      getFile();
    }
  }

  /**
   * 上传参数计算属性
   */
  const uploadParams = computed(() => ({
    id: props.registrationId,
    semesterCatogry: props.semesterCatogry,
  }));

  /**
   * 文件上传成功处理函数
   */
  function uploadSuccess(): void {
    createMessage.success('上传成功');
    changeLoading(false);
    getFile();
  }

  /**
   * 获取文件列表
   */
  function getFile(): void {
    if (!props.registrationId) return;

    changeLoading(true);
    getFileAsync({ id: props.registrationId })
      .then(res => {
        state.dataForm = res.data;
        emit('update:semesterCatogry', res.data.semesterCatogry);
      })
      .finally(() => {
        changeLoading(false);
      });
  }

  /**
   * 文件下载处理函数
   */
  function handlerDownload(type: number): void {
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要导出相关文件吗?',
      onOk: () => {
        changeLoading(true);
        getImageFileAsync({ id: props.registrationId, type })
          .then(res => {
            if (res.data?.url) {
              downloadByUrl({ url: res.data.url, fileName: res.data.name });
            }
          })
          .finally(() => {
            changeLoading(false);
          });
      },
    });
  }

  /**
   * 删除文件处理函数
   */
  function deleteFile(file: any): void {
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要删除该文件吗?',
      onOk: () => {
        changeLoading(true);
        deleteAttachmentAsync({
          id: props.registrationId,
          fileId: file.fileId,
        })
          .then(res => {
            if (res.code === 200) {
              createMessage.success(res.msg);
              getFile();
            } else {
              createMessage.error(res.msg);
            }
          })
          .finally(() => {
            changeLoading(false);
          });
      },
    });
  }

  /**
   * 上传错误处理函数
   */
  function uploadError(e: any): void {
    createMessage.error(e.msg || '上传失败');
    changeLoading(false);
  }

  // 暴露方法给父组件
  defineExpose({ init });
</script>

<style lang="less" scoped>
  .doc-card {
    margin-top: 16px;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  }

  .action-buttons {
    display: flex;
    gap: 16px;
    justify-content: center;
    margin: 20px 0;

    .ant-btn {
      min-width: 120px;
      height: 40px;
      font-size: 14px;
      border-radius: 4px;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
      }
    }
  }

  .pdf-container {
    margin: 16px 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    :deep(.file-viewer-container) {
      height: 100%;
    }
  }

  /* 精美按钮样式 */
  .fancy-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin: 24px 0;
    flex-wrap: wrap;
  }

  .fancy-button {
    position: relative;
    min-width: 140px;
    height: 48px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 8px;
    padding: 0 24px;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    line-height: 1;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(120deg, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 50%, rgba(255, 255, 255, 0) 70%);
      transform: translateX(-100%);
      transition: all 0.8s ease;
    }

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);

      &::before {
        transform: translateX(100%);
      }
    }

    &:active {
      transform: translateY(1px);
    }

    .anticon {
      margin-right: 8px;
      font-size: 16px;
      vertical-align: middle;
      position: relative;
      top: -1px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }
  }

  .fancy-button-primary {
    background: linear-gradient(45deg, #1890ff, #40a9ff);
    color: white;
    margin: auto 10px;
    &:hover {
      background: linear-gradient(45deg, #0078f0, #1890ff);
      box-shadow: 0 8px 25px -5px rgba(24, 144, 255, 0.3);
    }
  }

  .fancy-button-secondary {
    background: linear-gradient(45deg, #722ed1, #a855f7);
    color: white;
    margin: auto 10px;

    &:hover {
      background: linear-gradient(45deg, #5b21b6, #9333ea);
      box-shadow: 0 8px 25px -5px rgba(114, 46, 209, 0.3);
    }
  }
</style>
