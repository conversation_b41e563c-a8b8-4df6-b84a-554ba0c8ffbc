import { XundaSheetsCellEchartService } from '../services/sheet-cell-echart.service';

export class XundaFacadeSheetsCellEchart {
  constructor(private readonly _xundaSheetsCellEchartService: XundaSheetsCellEchartService) {}

  insertCellEchart(file: File, row: number, col: number, echartConfig: any) {
    return this._xundaSheetsCellEchartService.insertCellEchart(file, row, col, echartConfig);
  }
}
