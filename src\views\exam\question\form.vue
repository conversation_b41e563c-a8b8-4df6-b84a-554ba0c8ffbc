<template>
  <BasicModal v-bind="$attrs" @register="registerModal" width="1000px" :minHeight="100"
    :cancelText="t('common.cancelText', '取消')" :okText="t('common.okText', '确定')" @ok="handleSubmit"
    :closeFunc="onClose">
    <template #title>
      <a-space :size="10">
        <div class="text-16px font-medium">{{ title }}</div>
        <a-space-compact size="small" block v-if="dataForm.id">
          <a-tooltip :title="t('common.prevRecord')">
            <a-button size="small" :disabled="getPrevDisabled" @click="handlePrev">
              <i class="icon-ym icon-ym-caret-left text-10px"></i>
            </a-button>
          </a-tooltip>
          <a-tooltip :title="t('common.nextRecord')">
            <a-button size="small" :disabled="getNextDisabled" @click="handleNext">
              <i class="icon-ym icon-ym-caret-right text-10px"></i>
            </a-button>
          </a-tooltip>
        </a-space-compact>
      </a-space>
    </template>
    <template #insertFooter>
      <div class="loat-left mt-5px">
        <XundaCheckboxSingle v-model:value="submitType" :label="continueText" />
      </div>
    </template>
    <a-row class="dynamic-form">
      <a-form :colon="false" size="middle" layout="horizontal" labelAlign="left"
        :labelCol="{ style: { width: '100px' } }" :model="dataForm" :rules="dataRule" ref="formRef">
        <a-row :gutter="15">
          <!-- 具体表单 -->
          <!-- 题型按钮 -->
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-space style="margin-bottom: 16px;">
              <a-button v-for="(item, index) in questionTypes" :key="item.type"
                :type="activeType === item.type ? 'primary' : 'default'" @click="handleTypeChange(item.type)">
                {{ item.label }}
              </a-button>
            </a-space>
          </a-col>
          <!-- <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="parentId">
              <template #label>父题目 </template>
              <XundaSelect
                v-model:value="dataForm.parentId"
                :disabled="false"
                @change="changeData('parentId', -1)"
                placeholder="父题目"
                :allowClear="true"
                :style="{ width: '100%' }"
                showSearch
                :options="optionsObj.parentIdOptions"
                :fieldNames="optionsObj.parentIdFieldNames">
              </XundaSelect>
            </a-form-item>
          </a-col> -->
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="courseId">
              <template #label>课程 </template>
              <XundaSelect v-model:value="dataForm.courseId" :disabled="false" @change="changeData('courseId', -1)"
                placeholder="请选择课程" :allowClear="true" :style="{ width: '100%' }" showSearch
                :options="optionsObj.courseIdOptions" :fieldNames="optionsObj.courseIdFieldNames">
              </XundaSelect>
            </a-form-item>
          </a-col>
          

          <component :is="activeComponent" ref="currentEditor" :formData="state.dataForm" />

          <a-divider />
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="analysis">
              <template #label>解析 </template>
              <XundaInput v-model:value="dataForm.analysis" :disabled="false" @change="changeData('analysis', -1)"
                placeholder="解析" :allowClear="true" :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="difficulty">
              <template #label>难度系数 </template>
              <XundaRate v-model:value="dataForm.difficulty" :disabled="false" @change="changeData('difficulty', -1)"
                placeholder="难度系数" :allowClear="true" :style="{ width: '100%' }" :count="5" allow-half>
              </XundaRate>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="knowledgeTag">
              <template #label>知识点 </template>
              <XundaInput v-model:value="dataForm.knowledgeTag" :disabled="false"
                @change="changeData('knowledgeTag', -1)" placeholder="知识点" :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
</template>
<script lang="ts" setup>
import { create, update, getInfo } from '@/views/exam/question';
import { reactive, toRefs, nextTick, ref, unref, computed, inject, watch } from 'vue';
import { BasicModal, useModal } from '@/components/Modal';
import { useMessage } from '@/hooks/web/useMessage';
import { useI18n } from '@/hooks/web/useI18n';
import { useUserStore } from '@/store/modules/user';
import type { FormInstance } from 'ant-design-vue';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
// 表单权限
import { usePermission } from '@/hooks/web/usePermission';

import { getForSelect as getCourseForSelect } from '@/views/ea/course';

// 导入题型
import SingleChoiceEditor from '../questionComponent/SingleChoiceEditor.vue'
import MultipleChoiceEditor from '../questionComponent/MultipleChoiceEditor.vue'
import TrueFalseEditor from '../questionComponent/TrueFalseEditor.vue'
import FillBlankEditor from '../questionComponent/FillBlankEditor.vue'
import ShortAnswerEditor from '../questionComponent/ShortAnswerEditor.vue'


interface State {
  dataForm: any;
  tableRows: any;
  dataRule: any;
  optionsObj: any;
  childIndex: any;
  isEdit: any;
  interfaceRes: any;
  //可选范围默认值
  ableAll: any;
  //掩码配置
  maskConfig: any;
  //定位属性
  locationScope: any;
  title: string;
  continueText: string;
  allList: any[];
  currIndex: number;
  isContinue: boolean;
  submitType: number;
  showContinueBtn: boolean;
}



// 题型配置
const questionTypes = [
  { type: 'single', label: '单选题', component: SingleChoiceEditor },
  { type: 'multi', label: '多选题', component: MultipleChoiceEditor },
  { type: 'truefalse', label: '判断题', component: TrueFalseEditor },
  { type: 'fillblank', label: '填空', component: FillBlankEditor },
  { type: 'shortanswer', label: '简答', component: ShortAnswerEditor },
]

// 当前激活题型
const activeType = ref('single')

// 保存上次选择的key
const LAST_SELECTION_KEY = 'question_form_last_selection'

// 保存上次选择
function saveLastSelection() {
  const lastSelection = {
    questionTypeId: activeType.value,
    courseId: state.dataForm.courseId,
    timestamp: Date.now()
  }
  localStorage.setItem(LAST_SELECTION_KEY, JSON.stringify(lastSelection))
}

// 恢复上次选择
function restoreLastSelection() {
  try {
    const saved = localStorage.getItem(LAST_SELECTION_KEY)
    if (saved) {
      const lastSelection = JSON.parse(saved)
      // 检查是否在24小时内保存的
      const isRecent = Date.now() - lastSelection.timestamp < 24 * 60 * 60 * 1000
      if (isRecent) {
        return lastSelection
      }
    }
  } catch (error) {
    console.warn('Failed to restore last selection:', error)
  }
  return null
}

// 监听题型变化，自动设置questionTypeId
watch(activeType, (newType) => {
  if (newType) {
    state.dataForm.questionTypeId = newType
  }
})

// 当前组件引用（ref 方式访问其方法）
const currentEditor = ref()

// 动态获取当前组件
const activeComponent = computed(() => {
  const found = questionTypes.find(q => q.type === activeType.value)
  return found ? found.component : null
})
const emit = defineEmits(['reload']);
const getLeftTreeActiveInfo: (() => any) | null = inject('getLeftTreeActiveInfo', null);
const userStore = useUserStore();
const userInfo = userStore.getUserInfo;
const { createMessage, createConfirm } = useMessage();
const { t } = useI18n();
const [registerModal, { openModal, setModalProps }] = useModal();
const formRef = ref<FormInstance>();
const state = reactive<State>({
  dataForm: {
    id: '',
    parentId: '',
    courseId: '',
    questionTypeId: '',
    difficulty: undefined,
    analysis: '',
    content: '',
    option: '',
    answer: '',
    displayOrder: undefined,
    knowledgeTag: '',
  },
  tableRows: {},
  dataRule: {
    courseId: [
      {
        required: 'true',
        message: t('sys.validate.textRequiredSuffix', '课程不能为空'),
        trigger: 'blur',
      },
    ],
    questionTypeId: [
      {
        required: 'true',
        message: t('sys.validate.textRequiredSuffix', '题型不能为空'),
        trigger: 'blur',
      },
    ],
    content: [
      {
        required: 'true',
        message: t('sys.validate.textRequiredSuffix', '题干不能为空'),
        trigger: 'blur',
      },
    ],
    answer: [
      {
        required: 'true',
        message: t('sys.validate.textRequiredSuffix', '答案不能为空'),
        trigger: 'blur',
      },
    ],
  },
  optionsObj: {
    //选项配置
    defaultProps: { label: 'fullName', value: 'enCode' }, // 默认下拉选择键值
  },
  childIndex: -1,
  isEdit: false,
  interfaceRes: {},
  //可选范围默认值
  ableAll: {},

  //掩码配置
  maskConfig: {},
  //定位属性
  locationScope: {
    faddressDetail: [],
  },
  title: '',
  continueText: '',
  allList: [],
  currIndex: 0,
  isContinue: false,
  submitType: 0,
  showContinueBtn: true,
});
const { title, continueText, showContinueBtn, dataRule, dataForm, optionsObj, ableAll, maskConfig, submitType } = toRefs(state);

const getPrevDisabled = computed(() => state.currIndex === 0);
const getNextDisabled = computed(() => state.currIndex === state.allList.length - 1);
// 表单权限
const { hasFormP } = usePermission();

defineExpose({ init });

function setDefaultData() {
  // 尝试恢复上次的选择
  const lastSelection = restoreLastSelection()
  
  Object.assign(state.dataForm, {
    id: '',
    parentId: '',
    courseId: lastSelection?.courseId || '',
    questionTypeId: lastSelection?.questionTypeId || 'single',
    difficulty: undefined,
    analysis: '',
    content: '',
    option: '',
    answer: '',
    displayOrder: undefined,
    knowledgeTag: '',
  });
  
  // 设置题型
  if (lastSelection?.questionTypeId) {
    activeType.value = lastSelection.questionTypeId
  }
}

function init(data) {
  state.submitType = 0;
  state.isContinue = false;
  state.title = !data.id ? t('common.add2Text', '新增') : t('common.editText', '编辑');
  state.continueText = !data.id ? t('common.continueAndAddText', '确定并新增') : t('common.continueText', '确定并继续');
  setFormProps({ continueLoading: false });

  if (!data.id) {
    setDefaultData(); // 新增时清空并恢复上次选择
  } else {
    // 编辑时设置id，确保能正确加载数据
    state.dataForm.id = data.id;
  }

  openModal();
  state.allList = data.allList;
  state.currIndex = state.allList.length && data.id ? state.allList.findIndex(item => item.id === data.id) : 0;
  getAllSelectOptions();
  nextTick(() => {
    getForm().resetFields();
    setTimeout(initData, 0);
  });
}

function initData() {
  changeLoading(true)
  if (state.dataForm.id) {
    getData(state.dataForm.id)
  } else {
    // 新增时，如果没有在setDefaultData中设置题型，则使用默认值
    if (!state.dataForm.questionTypeId) {
      activeType.value = 'single'
      state.dataForm.questionTypeId = 'single'
    }
    
    if (getLeftTreeActiveInfo) {
      const treeInfo = getLeftTreeActiveInfo()
      if (treeInfo) {
        // 保存当前已恢复的courseId，避免被覆盖
        const currentCourseId = state.dataForm.courseId
        Object.assign(state.dataForm, treeInfo)
        // 如果之前有恢复的courseId，则优先使用恢复的值
        if (currentCourseId) {
          state.dataForm.courseId = currentCourseId
        }
      }
    }
    state.childIndex = -1
    changeLoading(false)
  }
}

function getForm() {
  const form = unref(formRef);
  if (!form) {
    throw new Error('form is null!');
  }
  return form;
}

function getData(id) {
  changeLoading(true)
  getInfo(id).then(res => {
    const data = res.data || {}
    if (data.questionTypeId) activeType.value = data.questionTypeId
    Object.assign(state.dataForm, data)
    state.childIndex = -1
    changeLoading(false)
  })
}

async function handleSubmit(type) {
  try {
    const values = await getForm()?.validate();
    if (!values) return;
    if (currentEditor.value && currentEditor.value.validate) {
      const childValid = currentEditor.value.validate();
      if (!childValid) return;
    }
    if (currentEditor.value && currentEditor.value.getData) {
      const childData = currentEditor.value.getData();
      Object.assign(state.dataForm, childData);
    }
    if (!state.dataForm.questionTypeId) {
      state.dataForm.questionTypeId = activeType.value;
    }
    setFormProps({ confirmLoading: true });
    const formMethod = state.dataForm.id ? update : create;
    formMethod(state.dataForm)
      .then(res => {
        createMessage.success(res.msg);
        setFormProps({ confirmLoading: false });
        // 保存当前选择，供下次使用
        saveLastSelection();
        if (state.submitType == 1) {
          setDefaultData(); // 继续新增时清空
          initData();
          state.isContinue = true;
        } else {
          setFormProps({ open: false });
          emit('reload');
        }
      })
      .catch(() => {
        setFormProps({ confirmLoading: false });
      });
  } catch (_) { }
}

// 跳转上一个
function handlePrev() {
  state.currIndex--;
  handleGetNewInfo();
}

// 跳转下一个
function handleNext() {
  state.currIndex++;
  handleGetNewInfo();
}

// 重新获取编辑信息
function handleGetNewInfo() {
  changeLoading(true);
  getForm().resetFields();
  const id = state.allList[state.currIndex].id;
  getData(id);
}

function setFormProps(data) {
  setModalProps(data);
}

//
function changeLoading(loading) {
  setModalProps({ loading });
}

// 关闭弹窗
async function onClose() {
  if (state.isContinue) emit('reload');
  return true;
}

function changeData(model, index) {
  state.isEdit = false;
  state.childIndex = index;
  for (let key in state.interfaceRes) {
    if (key != model) {
      let faceReList = state.interfaceRes[key];
      for (let i = 0; i < faceReList.length; i++) {
        let relationField = faceReList[i].relationField;
        if (relationField) {
          let modelAll = relationField.split('-');
          let faceMode = '';
          let faceMode2 = modelAll.length == 2 ? modelAll[0].substring(0, modelAll[0].length - 4) + modelAll[1] : '';
          for (let i = 0; i < modelAll.length; i++) {
            faceMode += modelAll[i];
          }
          if (faceMode == model || faceMode2 == model) {
            let options = 'get' + key + 'Options';
            eval(options)(true);
            changeData(key, index);
          }
        }
      }
    }
  }
}

function getAllSelectOptions() {
    state.optionsObj.courseIdFieldNames = { label: 'name', value: 'id' };
    state.optionsObj.courseIdOptions = [];
    getCourseForSelect({
      dataType: 1,
    }).then(res => {
      state.optionsObj.courseIdOptions = res.data.list;
    });
  }

// 题型切换
const handleTypeChange = (type) => {
  activeType.value = type
  state.dataForm.option = ''
  state.dataForm.answer = ''
  state.dataForm.questionTypeId = type
}

</script>
