<template>
  <div class="form-table">
    <div class="form-header">
      <h2 class="form-title">生产经营单位"三项岗位"人员安全生产培训申请登记表</h2>
    </div>
    <div class="training-type">
      <span class="checkbox"> 取证培训 <span v-if="formData.trainingCategory === '取证培训'"> ☑ </span><span v-else>☐</span> </span>
      <span class="checkbox"> 复审培训 <span v-if="formData.trainingCategory === '复审培训'"> ☑ </span><span v-else>☐</span> </span>
      <span class="checkbox"> 换证培训 <span v-if="formData.trainingCategory === '换证培训'"> ☑ </span><span v-else>☐</span> </span>
    </div>
    <table class="info-table">
      <tbody>
        <tr>
          <td width="15%" class="label-field">单位名称</td>
          <td colspan="5"
            ><span class="form-data-field">{{ formData.unitName || '' }}</span></td
          >
          <td rowspan="5" class="photo-cell">
            <div v-if="formData.userImg" class="photo-container">
              <a-image
                v-if="formData.userImg"
                :src="apiUrl + formData.userImg.url"
                :preview="{
                  src: apiUrl + formData.userImg.url,
                  mask: '点击预览',
                }" />
            </div>
            <div v-else class="photo-placeholder">
              <div class="placeholder-text">1寸证件照</div>
            </div>
          </td>
        </tr>
        <tr>
          <td class="label-field">单位地址</td>
          <td colspan="3"
            ><span class="form-data-field">{{ formData.unitAddress || '' }}</span></td
          >
          <td class="label-field">邮编</td>
          <td
            ><span class="form-data-field">{{ formData.postCode || '' }}</span></td
          >
        </tr>
        <tr>
          <td class="label-field">姓&nbsp;&nbsp;&nbsp;名</td>
          <td
            ><span class="form-data-field">{{ formData.name || '' }}</span></td
          >
          <td class="label-field">性别</td>
          <td
            ><span class="form-data-field">{{ formData.sex || '' }}</span></td
          >
          <td class="label-field">民族</td>
          <td
            ><span class="form-data-field">{{ formData.nation || '' }}</span></td
          >
        </tr>
        <tr>
          <td class="label-field">健康状况</td>
          <td
            ><span class="form-data-field">{{ formData.healthState || '' }}</span></td
          >
          <td class="label-field">身份证号</td>
          <td colspan="4"
            ><span class="form-data-field">{{ formData.idNo || '' }}</span></td
          >
        </tr>
        <tr>
          <td class="label-field">申请资格类型</td>
          <td colspan="5">
            <div class="qualification-types">
              <span class="checkbox">特种作业人员 <span v-if="formData.qualificationType === '特种作业人员'"> ☑ </span><span v-else>☐</span> </span>
              <span class="checkbox">主要负责人 <span v-if="formData.qualificationType === '主要负责人'"> ☑ </span><span v-else>☐</span> </span>
              <span class="checkbox">安全生产管理人员 <span v-if="formData.qualificationType === '安全生产管理人员'"> ☑ </span><span v-else>☐</span> </span>
            </div>
          </td>
        </tr>
        <tr>
          <td class="label-field">职&nbsp;&nbsp;&nbsp;务</td>
          <td
            ><span class="form-data-field">{{ formData.appointment || '' }}</span></td
          >
          <td class="label-field">何时任现职</td>
          <td
            ><span class="form-data-field">{{ formatDate(formData.tenureTime) || '' }}</span></td
          >
          <td class="label-field">何时从事现行业</td>
          <td colspan="2"
            ><span class="form-data-field">{{ formatDate(formData.employmentTime) || '' }}</span></td
          >
        </tr>
        <tr>
          <td class="label-field">毕业院校</td>
          <td colspan="3"
            ><span class="form-data-field">{{ formData.graduateSchool || '' }}</span></td
          >
          <td class="label-field">所学专业</td>
          <td colspan="2"
            ><span class="form-data-field">{{ formData.major || '' }}</span></td
          >
        </tr>
        <tr>
          <td class="label-field">学&nbsp;&nbsp;&nbsp;历</td>
          <td
            ><span class="form-data-field">{{ formData.educationalBackground || '' }}</span></td
          >
          <td class="label-field">是否全日制</td>
          <td
            ><span class="form-data-field">{{ formData.studyType ? '全日制' : '否' }}</span></td
          >
          <td class="label-field">联系电话</td>
          <td colspan="2"
            ><span class="form-data-field">{{ formData.phone || '' }}</span></td
          >
        </tr>
        <tr>
          <td class="label-field">资格证编号</td>
          <td colspan="2"
            ><span class="form-data-field">{{ formData.certificateNo || '' }}</span></td
          >
          <td class="label-field">初次取证时间</td>
          <td
            ><span class="form-data-field">{{ formatDate(formData.certificateReceiveTime) || '' }}</span></td
          >
          <td class="label-field">上次换证时间</td>
          <td
            ><span class="form-data-field">{{ formatDate(formData.changeCertificateTime) || '' }}</span></td
          >
        </tr>
        <tr>
          <td class="label-field">工作简历</td>
          <td colspan="6" class="work-history">
            <div class="history-content">{{ formatWorkExperiences(formData.workExperiences) }}</div>
          </td>
        </tr>
        <tr>
          <td class="label-field">单位意见</td>
          <td colspan="6" class="company-opinion">
            <div class="opinion-content">
              <div>是否在证件有效期内受到处罚或证件被扣口，情况为：</div>
              <div>是否同意报名口。&nbsp;&nbsp;&nbsp;&nbsp;其他说明：</div>
              <div class="signature-line">
                <span>（单位盖章）</span>
                <span class="date">负责人：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;年&nbsp;&nbsp;月&nbsp;&nbsp;日</span>
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td class="label-field">健康承诺</td>
          <td colspan="6" class="health-commitment">
            <div class="commitment-content"
              >本人身体健康，无妨碍从事相应特种作业的器质性心脏病、癫痫病、美尼尔氏症、眩晕症、癔病、震颤麻痹症、精神病、痴呆症，以及其他疾病和生理缺陷。<strong
                >（本承诺仅适用于特种作业人员）</strong
              ></div
            >
          </td>
        </tr>
        <tr>
          <td colspan="5" class="signature-row">
            <div class="signature-content"><strong>本人对以上申报材料及健康承诺的真实性负责</strong> </div> </td
          ><td colspan="2" class="health-commitment">
            <div class="commitment-content">本人签字（按手印）：</div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue';

  const props = defineProps({
    formData: {
      type: Object,
      default: () => ({
        studyType: true,
      }),
    },
    apiUrl: {
      type: String,
      default: '',
    },
  });

  const emit = defineEmits(['update:formData']);

  // 格式化日期的方法
  const formatDate = dateString => {
    if (!dateString) return '';
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  };

  // 格式化工作经历的方法
  const formatWorkExperiences = workExperiences => {
    if (!workExperiences || !Array.isArray(workExperiences) || workExperiences.length === 0) {
      return '';
    }

    return workExperiences
      .map(exp => {
        const startDate = formatDate(exp.startData);
        const endDate = formatDate(exp.endData);
        const dateRange = `${startDate} 至 ${endDate}`;
        return `${dateRange}：${exp.unitName || ''}，担任${exp.job || ''}职位`;
      })
      .join('\n');
  };
</script>

<style lang="less" scoped>
  .form-table {
    width: 100%;
    font-size: 14px;
    padding: 20px;
    border: none;

    .form-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 16px;

      .form-title {
        font-size: 20px;
        font-weight: bold;
        margin: 8px 0;
      }
    }

    .training-type {
      display: flex;
      justify-content: center;
      gap: 48px;
      margin-bottom: 16px;
      font-size: 14px;

      .checkbox {
        user-select: none;
      }
    }

    .info-table {
      width: 100%;
      border-collapse: collapse;
      table-layout: fixed;

      td {
        border: 1px solid #000;
        padding: 6px 10px;
        font-size: 14px;
        height: 38px;
        vertical-align: middle;
      }

      .label-field {
        font-weight: normal;
        text-align: center;
        background-color: #fff;
        white-space: nowrap;
      }

      .form-data-field {
        padding: 0 4px;
        min-width: 40px;
        display: inline-block;
      }

      .photo-cell {
        width: 120px;
        padding: 4px;
        vertical-align: middle;
        text-align: center;
        border: 1px solid #000;

        .photo-container {
          width: 100%;
          height: 140px;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .photo-placeholder {
          width: 100%;
          height: 140px;
          border: 1px dashed #999;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #f5f5f5;

          .placeholder-text {
            color: #666;
            font-size: 12px;
          }
        }
      }

      .qualification-types {
        display: flex;
        gap: 32px;
        justify-content: center;
        padding: 4px 0;

        .checkbox {
          font-size: 14px;
          user-select: none;
        }
      }

      .work-history {
        height: 120px;
        vertical-align: top;

        .history-content {
          width: 100%;
          height: 100%;
          line-height: 1.6;
          white-space: pre-line;
          padding: 8px 0;
        }
      }

      .company-opinion {
        padding: 10px;
        height: 100px;

        .opinion-content {
          line-height: 2.2;
        }

        .signature-line {
          display: flex;
          justify-content: space-between;
          margin-top: 16px;
        }
      }

      .health-commitment {
        padding: 10px;
        height: 80px;

        .commitment-content {
          line-height: 1.6;
        }
      }

      .signature-row {
        text-align: center;
        padding: 8px;

        .signature-content {
          line-height: 1.5;
        }
      }
    }

    @media print {
      padding: 0;

      .info-table {
        td {
          border: 1px solid #000 !important;
        }

        .photo-cell {
          border: 1px solid #000 !important;
        }
      }
    }
  }
</style>
