<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    width="600px"
    :minHeight="100"
    :cancelText="t('common.cancelText', '取消')"
    :okText="t('common.okText', '确定')"
    @ok="handleSubmit"
    :closeFunc="onClose">
    <template #title>
      <a-space :size="10">
        <div class="text-16px font-medium">{{ title }}</div>
        <a-space-compact size="small" block v-if="dataForm.id">
          <a-tooltip :title="t('common.prevRecord')">
            <a-button size="small" :disabled="getPrevDisabled" @click="handlePrev">
              <i class="icon-ym icon-ym-caret-left text-10px"></i>
            </a-button>
          </a-tooltip>
          <a-tooltip :title="t('common.nextRecord')">
            <a-button size="small" :disabled="getNextDisabled" @click="handleNext">
              <i class="icon-ym icon-ym-caret-right text-10px"></i>
            </a-button>
          </a-tooltip>
        </a-space-compact>
      </a-space>
    </template>
    <template #insertFooter>
      <div class="loat-left mt-5px">
        <XundaCheckboxSingle v-model:value="submitType" :label="continueText" />
      </div>
    </template>
    <a-row class="dynamic-form">
      <a-form
        :colon="false"
        size="middle"
        layout="horizontal"
        labelAlign="left"
        :labelCol="{ style: { width: '100px' } }"
        :model="dataForm"
        :rules="dataRule"
        ref="formRef">
        <a-row :gutter="15">
          <!-- 具体表单 -->
          <a-col :span="24" class="ant-col-item" :hidden="true">
            <a-form-item name="semesterId">
              <template #label>计划 </template>
              <XundaInput
                v-model:value="dataForm.semesterId"
                :disabled="false"
                @change="changeData('semesterId', -1)"
                placeholder="请输入计划"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="true">
            <a-form-item name="groupName">
              <template #label>优惠组 </template>
              <XundaInput
                v-model:value="dataForm.groupName"
                :disabled="false"
                @change="changeData('groupName', -1)"
                placeholder="请输入优惠组"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="field">
              <template #label>优惠类型 </template>
              <XundaSelect
                v-model:value="dataForm.field"
                :disabled="false"
                @change="handleFieldChange"
                :options="[
                  {
                    key: '学员类型',
                    fullName: '学员类型',
                  },
                  {
                    key: '单位码',
                    fullName: '单位码',
                  },
                ]"
                :fieldNames="{
                  label: 'fullName',
                  value: 'key',
                }"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="conditionType">
              <template #label>条件类型 </template>
              <XundaSelect
                v-model:value="dataForm.conditionType"
                :disabled="false"
                :options="[
                  {
                    key: '等于',
                    fullName: '等于',
                  },
                  {
                    key: '不等于',
                    fullName: '不等于',
                  },
                ]"
                :fieldNames="{
                  label: 'fullName',
                  value: 'key',
                }"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="conditionValue">
              <template #label>符合条件 </template>
              <XundaSelect
                v-if="dataForm.field === '学员类型'"
                v-model:value="dataForm.conditionValue"
                :disabled="false"
                @change="changeData('conditionValue', -1)"
                :options="[
                  {
                    key: '校内学生',
                    fullName: '校内学生',
                  },
                  {
                    key: '校内教师',
                    fullName: '校内教师',
                  },
                  {
                    key: '其他',
                    fullName: '其他',
                  },
                ]"
                :fieldNames="{
                  label: 'fullName',
                  value: 'key',
                }"
                placeholder="请选择符合条件"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaSelect>
              <XundaInput
                v-else
                v-model:value="dataForm.conditionValue"
                :disabled="false"
                @change="changeData('conditionValue', -1)"
                placeholder="符合条件"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="true">
            <a-form-item name="valueType">
              <template #label>值数据类型 </template>
              <XundaInput
                v-model:value="dataForm.valueType"
                :disabled="false"
                @change="changeData('valueType', -1)"
                placeholder="值数据类型"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="true">
            <a-form-item name="priority">
              <template #label
                >优先级<a-tooltip :title="'(大的优先)'" placement="top">
                  <QuestionCircleOutlined style="margin-left: 4px" />
                </a-tooltip>
              </template>
              <XundaInputNumber
                v-model:value="dataForm.priority"
                :disabled="false"
                @change="changeData('priority', -1)"
                placeholder="请输入优先级(大的优先)"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInputNumber>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="true">
            <a-form-item name="mutex">
              <template #label>互斥项 </template>
              <XundaInput
                v-model:value="dataForm.mutex"
                :disabled="false"
                @change="changeData('mutex', -1)"
                placeholder="请输入互斥项"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="money">
              <template #label>优惠金额 </template>
              <XundaInputNumber
                v-model:value="dataForm.money"
                :disabled="false"
                @change="changeData('money', -1)"
                placeholder="请输入优惠金额"
                :allowClear="true"
                :style="{ width: '100%' }"
                :precision="2"
                addonAfter="元"
                :controls="true">
              </XundaInputNumber>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="true">
            <a-form-item name="currency">
              <template #label>币种 </template>
              <XundaSelect
                v-model:value="dataForm.currency"
                :disabled="false"
                @change="changeData('currency', -1)"
                placeholder="币种"
                :allowClear="true"
                :style="{ width: '100%' }"
                showSearch
                :options="optionsObj.currencyOptions"
                :fieldNames="optionsObj.currencyFieldNames">
              </XundaSelect>
            </a-form-item>
          </a-col>
          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { create, update, getInfo } from './index';
  import { reactive, toRefs, nextTick, ref, unref, computed, inject } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useUserStore } from '@/store/modules/user';
  import type { FormInstance } from 'ant-design-vue';
  import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
  import { PaperClipOutlined, EyeOutlined, DownloadOutlined, CloseOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue';
  // 表单权限
  import { usePermission } from '@/hooks/web/usePermission';
  import { XundaSelect } from '@/components/Xunda';
  import { add } from '../../../../../components/Form/src/componentMap';
  interface State {
    dataForm: any;
    tableRows: any;
    dataRule: any;
    optionsObj: any;
    childIndex: any;
    isEdit: any;
    interfaceRes: any;
    //可选范围默认值
    ableAll: any;
    //掩码配置
    maskConfig: any;
    //定位属性
    locationScope: any;
    title: string;
    continueText: string;
    allList: any[];
    currIndex: number;
    isContinue: boolean;
    submitType: number;
    showContinueBtn: boolean;
  }

  const emit = defineEmits(['reload']);
  const getLeftTreeActiveInfo: (() => any) | null = inject('getLeftTreeActiveInfo', null);
  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const [registerModal, { openModal, setModalProps }] = useModal();
  const formRef = ref<FormInstance>();
  const state = reactive<State>({
    dataForm: {
      id: '',
      semesterId: '',
      groupname: '',
      field: '',
      condition: '',
      value: '',
      valueType: '',
      priority: undefined,
      mutex: '',
      money: undefined,
      currency: '',
    },
    tableRows: {},
    dataRule: {
      id: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '标识不能为空'),
          trigger: 'blur',
        },
      ],
      semesterId: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '计划不能为空'),
          trigger: 'blur',
        },
      ],
      field: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '优惠类型不能为空'),
          trigger: 'blur',
        },
      ],
      conditionType: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '条件类型不能为空'),
          trigger: 'blur',
        },
      ],
      conditionValue: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '符合条件不能为空'),
          trigger: 'blur',
        },
      ],
      groupName: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '优惠组不能为空'),
          trigger: 'blur',
        },
      ],
      priority: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '优先级(大的优先)不能为空'),
          trigger: 'blur',
        },
      ],
      mutex: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '互斥项不能为空'),
          trigger: 'blur',
        },
      ],
      money: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '优惠金额不能为空'),
          trigger: 'blur',
        },
      ],
    },
    optionsObj: {
      //选项配置
      defaultProps: { label: 'fullName', value: 'enCode' }, // 默认下拉选择键值
    },
    childIndex: -1,
    isEdit: false,
    interfaceRes: {},
    //可选范围默认值
    ableAll: {},

    //掩码配置
    maskConfig: {},
    //定位属性
    locationScope: {
      faddressDetail: [],
    },
    title: '',
    continueText: '',
    allList: [],
    currIndex: 0,
    isContinue: false,
    submitType: 0,
    showContinueBtn: true,
  });
  const { title, continueText, showContinueBtn, dataRule, dataForm, optionsObj, ableAll, maskConfig, submitType } = toRefs(state);

  const getPrevDisabled = computed(() => state.currIndex === 0);
  const getNextDisabled = computed(() => state.currIndex === state.allList.length - 1);
  // 表单权限
  const { hasFormP } = usePermission();

  const semesterId = ref<string>('');
  defineExpose({ init });

  function init(data) {
    if (data.semesterId) semesterId.value = data.semesterId;
    state.submitType = 0;
    state.isContinue = false;
    state.title = !data.id ? t('common.add2Text', '新增') : t('common.editText', '编辑');
    state.continueText = !data.id ? t('common.continueAndAddText', '确定并新增') : t('common.continueText', '确定并继续');
    setFormProps({ continueLoading: false });
    state.dataForm.id = data.id;
    openModal();
    state.allList = data.allList;
    state.currIndex = state.allList.length && data.id ? state.allList.findIndex(item => item.id === data.id) : 0;
    getAllSelectOptions();
    nextTick(() => {
      getForm().resetFields();
      setTimeout(initData, 0);
    });
  }
  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      // 设置默认值
      state.dataForm = {
        id: '',
        semesterId: semesterId.value,
        groupName: '自建',
        field: '',
        conditionType: '',
        conditionValue: '',
        valueType: '',
        priority: 99,
        mutex: '无',
        money: undefined,
        currency: '001',
      };

      if (getLeftTreeActiveInfo) state.dataForm = { ...state.dataForm, ...(getLeftTreeActiveInfo() || {}) };
      state.childIndex = -1;
      changeLoading(false);
    }
  }
  function getForm() {
    const form = unref(formRef);
    if (!form) {
      throw new Error('form is null!');
    }
    return form;
  }
  function getData(id) {
    getInfo(id).then(res => {
      state.dataForm = res.data || {};
      state.childIndex = -1;
      changeLoading(false);
    });
  }
  async function handleSubmit(type) {
    try {
      const values = await getForm()?.validate();
      if (!values) return;
      setFormProps({ confirmLoading: true });
      const formMethod = state.dataForm.id ? update : create;
      console.log('values', state.dataForm);
      formMethod(state.dataForm)
        .then(res => {
          createMessage.success(res.msg);
          setFormProps({ confirmLoading: false });
          if (state.submitType == 1) {
            initData();
            state.isContinue = true;
          } else {
            setFormProps({ open: false });
            emit('reload');
          }
        })
        .catch(() => {
          setFormProps({ confirmLoading: false });
        });
    } catch (_) {}
  }

  // 跳转上一个
  function handlePrev() {
    state.currIndex--;
    handleGetNewInfo();
  }

  // 跳转下一个
  function handleNext() {
    state.currIndex++;
    handleGetNewInfo();
  }

  // 重新获取编辑信息
  function handleGetNewInfo() {
    changeLoading(true);
    getForm().resetFields();
    const id = state.allList[state.currIndex].id;
    getData(id);
  }

  function setFormProps(data) {
    setModalProps(data);
  }

  //
  function changeLoading(loading) {
    setModalProps({ loading });
  }

  // 关闭弹窗
  async function onClose() {
    if (state.isContinue) emit('reload');
    return true;
  }

  function handleFieldChange(value) {
    // 当字段值改变时，如果从学员类型改为其他类型，则清空conditionValue
    if (value !== '学员类型') {
      state.dataForm.conditionValue = '';
    }
    // 调用原来的changeData方法
    changeData('field', -1);
  }

  function changeData(model, index) {
    state.isEdit = false;
    state.childIndex = index;
    for (let key in state.interfaceRes) {
      if (key != model) {
        let faceReList = state.interfaceRes[key];
        for (let i = 0; i < faceReList.length; i++) {
          let relationField = faceReList[i].relationField;
          if (relationField) {
            let modelAll = relationField.split('-');
            let faceMode = '';
            let faceMode2 = modelAll.length == 2 ? modelAll[0].substring(0, modelAll[0].length - 4) + modelAll[1] : '';
            for (let i = 0; i < modelAll.length; i++) {
              faceMode += modelAll[i];
            }
            if (faceMode == model || faceMode2 == model) {
              let options = 'get' + key + 'Options';
              eval(options)(true);
              changeData(key, index);
            }
          }
        }
      }
    }
  }
  function getAllSelectOptions() {}
</script>
