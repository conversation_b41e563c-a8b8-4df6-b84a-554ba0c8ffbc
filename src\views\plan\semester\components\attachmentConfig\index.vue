<template>
  <div class="attachment-config">
    <div class="config-title-section">
      <h2 class="config-title">附件配置表</h2>
      <p class="config-description">请在下表中添加、编辑或删除您需要的附件配置项，确保填写所有必填字段。</p>
    </div>
    <div class="table-operations">
      <a-space>
        <a-button type="primary" @click="handleAdd" v-if="!props.publishFlag">
          <template #icon><plus-outlined /></template>
          添加附件配置
        </a-button>
      </a-space>
    </div>
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :pagination="false"
      bordered
      :row-key="record => record.id"
      :locale="{ emptyText: '暂无附件配置数据' }">
      <!-- 序号列 -->
      <template #index="{ index }">
        {{ index + 1 }}
      </template>

      <!-- 名称列 -->
      <template #name="{ text, record }">
        <a-input v-if="editable" v-model:value="record.name" placeholder="请输入附件名称" allow-clear :status="!record.name && record.isNew ? 'error' : ''" />
        <span v-else>{{ text }}</span>
      </template>

      <!-- 对应字段列 -->
      <template #field="{ text, record }">
        <a-input
          v-if="editable"
          v-model:value="record.field"
          placeholder="请输入对应字段名"
          allow-clear
          :status="!record.field && record.isNew ? 'error' : ''" />
        <span v-else>{{ text }}</span>
      </template>

      <!-- 说明列 -->
      <template #description="{ text, record }">
        <a-textarea v-if="editable" v-model:value="record.description" placeholder="请输入附件说明信息" :auto-size="{ minRows: 1, maxRows: 3 }" allow-clear />
        <span v-else>{{ text || '-' }}</span>
      </template>

      <!-- 模版文件列 -->
      <template #template="{ text, record }">
        <div v-if="editable">
          <XundaUploadFile
            v-model:value="record.template"
            type="attachment"
            :limit="1"
            :accept="'.doc,.docx,.xls,.xlsx,.pdf'"
            buttonText="上传模版文件"
            tipText="请上传doc、docx、xls、xlsx、pdf 格式文件"
            @change="files => handleFileChange(record, 'template', files)" />
        </div>
        <div v-else>
          <div v-if="record.template && record.template.length" class="file-list">
            <div v-for="file in record.template" :key="file.fileId" class="file-item">
              <FileOutlined />
              <a @click="handlePreviewFile(file)">{{ file.name }}</a>
            </div>
          </div>
          <span v-else>-</span>
        </div>
      </template>

      <!-- 填写说明列 -->
      <template #img="{ text, record }">
        <div v-if="editable">
          <XundaUploadFile
            v-model:value="record.img"
            type="attachment"
            :limit="5"
            :accept="'.jpg,.jpeg,.png'"
            buttonText="上传填写说明图片"
            tipText="请上传jpg、jpeg、png格式图片"
            @change="files => handleFileChange(record, 'img', files)" />
        </div>
        <div v-else>
          <div v-if="record.img && record.img.length" class="image-list">
            <div v-for="img in record.img" :key="img.fileId" class="image-item">
              <PictureOutlined />
              <a @click="handlePreviewFile(img)">{{ img.name }}</a>
            </div>
          </div>
          <span v-else>-</span>
        </div>
      </template>

      <!-- 操作列 -->
      <template #operation="{ record }">
        <a-space>
          <a-popconfirm
            title="确定要删除这个附件配置吗？"
            description="删除后将无法恢复，请谨慎操作"
            @confirm="handleDelete(record)"
            v-if="!props.publishFlag"
            ok-text="确定"
            cancel-text="取消">
            <a-button type="link" danger size="small">
              <template #icon><delete-outlined /></template>
              删除
            </a-button>
          </a-popconfirm>
          <span v-else>-</span>
        </a-space>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, computed, watch } from 'vue';
  import { PlusOutlined, DeleteOutlined, FileOutlined, PictureOutlined } from '@ant-design/icons-vue';
  import XundaUploadFile from '@/components/Xunda/Upload/src/UploadFile.vue';
  import { message } from 'ant-design-vue';
  import { cloneDeep } from 'lodash-es';
  import { getAttachmentConfigAsync, setAttachmentConfigAsync } from '.';
  import { useGlobSetting } from '@/hooks/setting';

  const emit = defineEmits(['changeLoading', 'handleStep', 'update:semesterId', 'update:isCopy']);
  /**
   * 切换加载状态
   * @param {boolean} loading - 加载状态标志
   * @description 通过emit向父组件发送加载状态变化的事件
   */
  const changeLoading = loading => {
    emit('changeLoading', loading);
  };
  const props = defineProps({
    semesterId: {
      type: String,
      required: true,
    },
    disabled: { type: Boolean, default: false },
    isCopy: { type: Boolean, default: false },
    publishFlag: { type: Boolean, default: true },
  });
  interface TableItem {
    id: string;
    name: string;
    field: string;
    description: string;
    template?: any[]; // 模板文件
    img?: any[]; // 填写说明图片
    editable?: boolean;
    isNew?: boolean;
  }

  const editable = ref(false);
  const apiUrl = ref(useGlobSetting().apiUrl);

  const columns = computed(() => {
    const baseColumns = [
      {
        title: '序号',
        dataIndex: 'index',
        width: 80,
        align: 'center',
        slots: { customRender: 'index' },
      },
      {
        title: '附件名称',
        dataIndex: 'name',
        slots: { customRender: 'name' },
      },
      {
        title: '对应字段',
        dataIndex: 'field',
        slots: { customRender: 'field' },
      },

      {
        title: '模版文件',
        dataIndex: 'template',
        slots: { customRender: 'template' },
      },
      {
        title: '填写说明',
        dataIndex: 'img',
        slots: { customRender: 'img' },
      },
      {
        title: '说明',
        dataIndex: 'description',
        slots: { customRender: 'description' },
      },
    ];

    if (!props.publishFlag) {
      baseColumns.push({
        title: '操作',
        dataIndex: 'operation',
        width: 200,
        align: 'center',
        slots: { customRender: 'operation' },
      });
    }

    return baseColumns;
  });

  const dataSource = ref<TableItem[]>([]);
  const cacheData = ref<TableItem[]>([]);

  /**
   * 生成唯一ID
   * @returns {string} 基于时间戳和随机数的唯一标识符
   * @description 为新添加的表单项生成唯一标识
   */
  const generateId = () => {
    return `${Date.now()}-${Math.floor(Math.random() * 1000)}`;
  };

  /**
   * 添加新行数据
   * @description 创建一个新的附件配置项并添加到数据源中
   */
  const handleAdd = () => {
    const newItem: TableItem = {
      id: generateId(),
      name: '',
      field: '',
      description: '',
      template: [],
      img: [],
      editable: true,
      isNew: true,
    };
    dataSource.value.push(newItem);
    cacheData.value = cloneDeep(dataSource.value);
    message.info('请填写新添加行的所有必要信息');
  };

  /**
   * 删除行数据
   * @param {TableItem} record - 要删除的行记录对象
   * @description 从数据源中移除指定的附件配置项
   */
  const handleDelete = (record: TableItem) => {
    dataSource.value = dataSource.value.filter(item => item.id !== record.id);
    message.success('删除成功');
  };

  /**
   * 初始化组件数据
   * @param {any} data - 可选的初始化数据
   * @description 根据权限设置编辑状态，并从服务器获取附件配置数据
   */
  function init(data) {
    editable.value = !props.disabled && !props.publishFlag;
    changeLoading(true);
    getAttachmentConfigAsync(props.semesterId)
      .then(res => {
        changeLoading(false);
        dataSource.value = res.data;
        cacheData.value = cloneDeep(res.data);
      })
      .catch(err => {
        changeLoading(false);
        message.error('加载附件配置数据失败，请稍后重试');
      });
  }
  /**
   * 预览文件
   * @param {Object} file - 文件对象
   * @description 在新窗口中打开文件预览
   */
  const handlePreviewFile = file => {
    if (file && file.url) {
      window.open(apiUrl.value + file.url, '_blank');
    }
  };

  /**
   * 处理文件变化
   * @param {TableItem} record - 当前行记录
   * @param {string} field - 字段名称 ('template' | 'img')
   * @param {Array} files - 新的文件列表
   */
  const handleFileChange = (record: TableItem, field: string, files: any[]) => {
    // 确保数据源中的记录被更新
    const index = dataSource.value.findIndex(item => item.id === record.id);
    if (index > -1) {
      dataSource.value[index][field] = files;
      // 更新缓存数据
      cacheData.value = cloneDeep(dataSource.value);
    }
  };

  // 监听数据源变化
  watch(
    () => dataSource.value,
    newVal => {
      // 确保所有记录都有必要的字段
      newVal.forEach(item => {
        if (!item.template) item.template = [];
        if (!item.img) item.img = [];
      });
    },
    { deep: true },
  );

  /**
   * 提交附件配置数据
   * @param {string} type - 提交后下一步操作类型
   * @description 验证并保存附件配置数据到服务器
   */
  async function handleSubmit(type) {
    // 表单验证
    const hasEmptyFields = dataSource.value.some(item => !item.name || !item.field);
    if (hasEmptyFields) {
      message.warning('请填写所有必填项（附件名称和对应字段）');
      return;
    }

    const params = {
      semesterId: props.semesterId,
      attachmentConfig: dataSource.value,
    };

    changeLoading(true);
    setAttachmentConfigAsync(params)
      .then(res => {
        message.success('附件配置保存成功');
        emit('handleStep', type);
        changeLoading(false);
      })
      .catch(err => {
        changeLoading(false);
        message.error('保存失败，请检查数据后重试');
      });
  }

  // 对外暴露的方法
  defineExpose({
    init,
    handleSubmit,
  });
</script>

<style lang="less" scoped>
  .attachment-config {
    padding: 24px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    width: 100%;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .table-operations {
      margin-bottom: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .config-title-section {
      margin-bottom: 20px;
      padding-bottom: 16px;
      border-bottom: 1px solid #f0f0f0;

      .config-title {
        font-size: 18px;
        font-weight: 600;
        color: #1a1a1a;
        margin-bottom: 8px;
      }

      .config-description {
        font-size: 14px;
        color: #666;
        margin: 0;
        line-height: 1.5;
      }
    }

    :deep(.ant-table) {
      .ant-table-thead > tr > th {
        background: #f0f5ff;
        font-weight: 600;
        padding: 12px 16px;
        color: #1a1a1a;
      }

      .ant-table-tbody > tr:hover > td {
        background: #f0f7ff;
      }

      .ant-table-tbody > tr > td {
        padding: 12px 16px;
        transition: all 0.2s;
      }

      .ant-input {
        width: 100%;
        padding: 8px 12px;
        border-radius: 4px;

        &:hover,
        &:focus {
          border-color: @primary-color;
          box-shadow: 0 0 0 2px fade(@primary-color, 20%);
        }
      }

      .ant-btn-link {
        transition: all 0.3s;
        &:hover {
          transform: scale(1.05);
        }
      }
    }

    .file-list,
    .image-list {
      .file-item,
      .image-item {
        margin-bottom: 8px;
        display: flex;
        align-items: center;

        .anticon {
          margin-right: 8px;
          color: #1890ff;
        }

        a {
          color: #1890ff;
          text-decoration: none;
          transition: all 0.3s ease;

          &:hover {
            color: #40a9ff;
            text-decoration: underline;
          }
        }
      }
    }

    .image-list {
      .image-item {
        .anticon {
          color: #13c2c2;
        }
      }
    }
  }
</style>
