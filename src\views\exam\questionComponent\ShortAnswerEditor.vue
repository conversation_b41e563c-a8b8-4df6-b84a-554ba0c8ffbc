<template>
    <div>
        <!-- 题干 -->
        <a-col :span="24" class="ant-col-item">
            <div class="form-item-wrapper">
                <div class="form-label">题干</div>
                <XundaEditor v-model:value="formData.content" :disabled="false" :allowClear="false" :height="300"
                    :style="{ width: '100%' }" />
            </div>
        </a-col>

        <!-- 答案列表 -->
        <a-col :span="24" class="ant-col-item">
            <div class="form-item-wrapper">
                <div class="form-label">参考答案</div>
                <div v-for="(ans, index) in localAnswers" :key="index" style="margin-bottom: 8px;">
                    <a-input v-model:value="localAnswers[index]" placeholder="输入参考答案" allow-clear />
                </div>
                <a-button type="dashed" @click="addAnswer">+ 添加更多</a-button>
            </div>
        </a-col>
    </div>
</template>

<script setup>
import { reactive, defineExpose, computed, ref, watch } from 'vue'
import { message } from 'ant-design-vue'

// 定义props，接收父组件的dataForm
const props = defineProps({
  formData: {
    type: Object,
    required: true
  }
})

// 直接使用父组件的formData
const formData = props.formData

// 本地答案数据
const localAnswers = ref([''])

// 初始化答案数据
const initAnswers = () => {
  // 如果数据为空或无效，重置为默认状态
  if (!formData.answer || formData.answer === '') {
    localAnswers.value = ['']
    return
  }
  
  if (typeof formData.answer === 'string') {
    const answerArray = formData.answer.split(';').filter(a => a.trim() !== '')
    localAnswers.value = answerArray.length > 0 ? answerArray : ['']
  } else if (Array.isArray(formData.answer)) {
    const answerArray = formData.answer.filter(a => a.trim() !== '')
    localAnswers.value = answerArray.length > 0 ? answerArray : ['']
  } else {
    localAnswers.value = ['']
  }
}

// 监听父组件数据变化，初始化答案
watch(() => [props.formData.answer, props.formData.content], () => {
  initAnswers()
}, { immediate: true, deep: true })

// 监听本地答案变化，同步到父组件
watch(localAnswers, (newAnswers) => {
  const validAnswers = newAnswers.filter(a => a.trim() !== '')
  formData.answer = validAnswers.join(';')
}, { deep: true })

// 添加一个新的参考答案
const addAnswer = () => {
  localAnswers.value.push('')
}

// 获取数据
const getData = () => {
    return {
        content: formData.content,
        answers: localAnswers.value.filter(a => a.trim() !== ''),
    }
}

// 校验方法
const validate = () => {
    if (!formData.content || !formData.content.trim()) {
        message.error('题干不能为空')
        return false
    }
    const validAnswers = localAnswers.value.filter(a => a.trim() !== '')
    if (validAnswers.length === 0) {
        message.error('请至少填写一个参考答案')
        return false
    }
    return true
}

defineExpose({ validate, getData })
</script>

<style scoped>
.ant-col-item {
    margin-bottom: 16px;
}

.form-item-wrapper {
  margin-bottom: 16px;
}

.form-label {
  font-weight: 500;
  margin-bottom: 8px;
  color: rgba(0, 0, 0, 0.85);
}
</style>
