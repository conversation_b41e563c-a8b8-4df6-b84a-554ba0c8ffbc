import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';

// 基础Api
export const studentCourseApi = '/api/ea/studentCourse';

/**
 * 获取列表
 * @param data
 * @returns
 */
export function getClassesList(data) {
  return defHttp.post({ url: studentCourseApi + `/getList`, data });
}

/**
 * 搜索表单配置
 */
export const classesSearchSchemas: FormSchema[] = [
  {
    field: 'name',
    label: t('班级名称'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
];

/**
 * 表格列配置
 */
export const classesColumns: BasicColumn[] = [
  {
    title: t('班级名称'),
    dataIndex: 'name',
    width: 120,
  },
];
