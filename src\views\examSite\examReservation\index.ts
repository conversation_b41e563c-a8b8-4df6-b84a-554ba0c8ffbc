import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';
import { FormSchema } from '@/components/Form';

// 基础Api
export const examReservationApi = '/api/exam/examReservation';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: examReservationApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: examReservationApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: examReservationApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: examReservationApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: examReservationApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: examReservationApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: examReservationApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: examReservationApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: examReservationApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: examReservationApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: examReservationApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: examReservationApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: examReservationApi + `/getForSelect`, data });
}

// 审核考点预约
export function auditExamReservation(data) {
  return defHttp.post({ url: examReservationApi + `/auditReservation`, data });
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'unitName',
    label: t('单位名称'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
  {
    field: 'contactPerson',
    label: t('联系人'),
    component: 'Input',
  },
  {
    field: 'examMode',
    label: t('考试方式'),
    component: 'Select',
    componentProps: {
      options: [
        { fullName: t('线上'), id: 0 },
        { fullName: t('线下'), id: 1 },
      ],
    },
  },
  {
    field: 'status',
    label: t('状态'),
    component: 'Select',
    componentProps: {
      options: [
        { fullName: t('待审核'), id: 0 },
        { fullName: t('已通过'), id: 1 },
        { fullName: t('已拒绝'), id: 2 },
        { fullName: t('已完成'), id: 3 },
      ],
    },
  },
];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('单位名称'),
    dataIndex: 'unitName',
    sorter: true,
    width: 150,
  },
  {
    title: t('联系人'),
    dataIndex: 'contactPerson',
    sorter: true,
    width: 100,
  },
  {
    title: t('联系电话'),
    dataIndex: 'contactPhone',
    sorter: true,
    width: 120,
  },
  {
    title: t('考试方式'),
    dataIndex: 'examMode',
    sorter: true,
    width: 100,
    customRender: ({ value }) => {
      if (value === 0) {
        return t('线上');
      } else if (value === 1) {
        return t('线下');
      }
      return value;
    },
  },
  {
    title: t('考点名称'),
    dataIndex: 'placeName',
    sorter: true,
    width: 150,
  },
  {
    title: t('考试日期'),
    dataIndex: 'examDate',
    sorter: true,
    width: 120,
    customRender: ({ value }) => {
      return value ? xundaUtils.toDateString(value) : '-';
    },
  },
  {
    title: t('开始时间'),
    dataIndex: 'startTime',
    sorter: true,
    width: 100,
  },
  {
    title: t('持续时长(小时)'),
    dataIndex: 'durationHours',
    sorter: true,
    width: 120,
  },
  {
    title: t('预计人数'),
    dataIndex: 'estimatedCount',
    sorter: true,
    width: 100,
  },
  {
    title: t('状态'),
    dataIndex: 'status',
    sorter: true,
    width: 100,
    customRender: ({ value }) => {
      if (value === 0) {
        return t('待审核');
      } else if (value === 1) {
        return t('已通过');
      } else if (value === 2) {
        return t('已拒绝');
      } else if (value === 3) {
        return t('已完成');
      }
      return value;
    },
  },
];
