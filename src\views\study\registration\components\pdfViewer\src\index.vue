<template>
  <a-card class="pdf-card">
    <template #title v-if="showTitle">
      <div class="card-title">
        <FilePdfOutlined class="file-icon" />
        <span class="file-name">{{ getFileName }}</span>
      </div>
    </template>
    <template #extra>
      <a-space>
        <a-button type="primary" @click="handlePreview" v-if="showPreviewButton">
          <template #icon><EyeOutlined /></template>
          预览
        </a-button>
        <a-button @click="handleDownload" v-if="showDownloadButton" :loading="isDownloading">
          <template #icon><DownloadOutlined /></template>
          下载
        </a-button>
        <a-button @click="handlePrint" v-if="showPrintButton" :loading="isPrinting">
          <template #icon><PrinterOutlined /></template>
          打印
        </a-button>
        <a-button danger @click="handleDelete" v-if="showDeleteButton">
          <template #icon><DeleteOutlined /></template>
          删除
        </a-button>
      </a-space>
    </template>
    <div class="pdf-content" v-if="showContent">
      <iframe v-if="fileUrl" :src="iframeSrc" class="pdf-iframe" @load="handleIframeLoad" @error="handleIframeError"></iframe>
      <div v-else class="no-file">
        <FileExclamationOutlined />
        <p>暂无PDF文件</p>
      </div>
    </div>
  </a-card>
</template>

<script lang="ts" setup>
  import { computed, ref } from 'vue';
  import { FilePdfOutlined, EyeOutlined, DownloadOutlined, FileExclamationOutlined, PrinterOutlined, DeleteOutlined } from '@ant-design/icons-vue';
  import { useGlobSetting } from '@/hooks/setting';
  import { message } from 'ant-design-vue';
  import type { PropType } from 'vue';
  import { fileItem } from '@/components/Xunda/Upload/src/props';
  import { getDownloadUrl } from '@/api/basic/common';
  import { downloadByUrl } from '@/utils/file/download';

  const props = defineProps({
    type: { type: String, default: 'annex' },
    file: {
      type: Object as PropType<fileItem>,
      default: () => ({}),
    },
    fileName: {
      type: String,
      default: '',
    },
    height: {
      type: [String, Number],
      default: '100%',
    },
    minHeight: {
      type: [String, Number],
      default: '1200px',
    },
    showPreviewButton: {
      type: Boolean,
      default: false,
    },
    showDownloadButton: {
      type: Boolean,
      default: true,
    },
    showPrintButton: {
      type: Boolean,
      default: true,
    },
    showDeleteButton: {
      type: Boolean,
      default: false,
    },
    showTitle: {
      type: Boolean,
      default: true,
    },
    showContent: {
      type: Boolean,
      default: true,
    },
  });

  const isDownloading = ref(false);
  const isPrinting = ref(false);
  const iframeLoaded = ref(false);

  const emit = defineEmits(['delete']);

  const globSetting = useGlobSetting();
  const apiUrl = computed(() => globSetting.apiUrl);

  const getFileName = computed(() => {
    if (props.fileName) return props.fileName;
    if (props.file?.name) return props.file.name.split(',').pop() || '未知文件';
    if (props.file?.url) return props.file.url.split('/').pop() || '未知文件';
    return '未知文件';
  });

  const fileUrl = computed(() => {
    if (!props.file?.url) return '';
    return props.file.url.startsWith('http') ? props.file.url : `${apiUrl.value}${props.file.url}`;
  });

  const iframeSrc = computed(() => {
    if (!fileUrl.value) return '';
    return fileUrl.value + '#toolbar=0';
  });

  const handleIframeLoad = () => {
    iframeLoaded.value = true;
  };

  const handleIframeError = () => {
    message.error('PDF文件加载失败');
  };

  const handlePreview = () => {
    if (!fileUrl.value) {
      message.error('无效的文件地址');
      return;
    }
  };

  const handleDownload = async () => {
    if (!props.file) {
      message.error('无效的文件');
      return;
    }

    try {
      isDownloading.value = true;

      if (props.file.fileId) {
        const res = await getDownloadUrl(props.type, props.file.fileId);
        await downloadByUrl({ url: res.data.url, fileName: getFileName.value });
      } else if (props.file.url) {
        await downloadByUrl({ url: props.file.url, fileName: getFileName.value });
      } else {
        throw new Error('无效的文件地址');
      }

      message.success('下载成功');
    } catch (error) {
      message.error('下载失败：' + (error instanceof Error ? error.message : '未知错误'));
    } finally {
      isDownloading.value = false;
    }
  };

  const handlePrint = () => {
    if (!fileUrl.value) {
      message.error('无效的文件地址');
      return;
    }

    isPrinting.value = true;
    setTimeout(() => {
      if (isPrinting.value) {
        isPrinting.value = false;
      }
    }, 1000);

    // 创建iframe用于打印PDF
    const iframe = document.createElement('iframe');
    iframe.style.position = 'fixed';
    iframe.style.left = '-9999px';
    iframe.style.top = '-9999px';
    iframe.src = fileUrl.value;

    const cleanup = () => {
      if (document.body.contains(iframe)) {
        document.body.removeChild(iframe);
      }
      isPrinting.value = false;
    };

    iframe.onload = () => {
      try {
        const contentWindow = iframe.contentWindow;
        if (!contentWindow) {
          throw new Error('打印窗口创建失败');
        }

        // 触发打印
        contentWindow.print();
      } catch (error) {
        message.error('打印失败：' + (error instanceof Error ? error.message : '未知错误'));
        cleanup();
      }
    };

    iframe.onerror = () => {
      message.error('打印文件加载失败');
      cleanup();
    };

    document.body.appendChild(iframe);
  };

  const handleDelete = () => {
    if (!props.file) {
      message.error('无效的文件');
      return;
    }

    emit('delete', props.file);
  };
</script>

<style lang="less" scoped>
  .pdf-card {
    height: v-bind('showContent ? "100%" : "auto"');
    min-height: v-bind('showContent ? (typeof minHeight === "number" ? minHeight + "px" : minHeight) : "auto"');
    display: flex;
    flex-direction: column;
    position: relative;

    :deep(.ant-card-head) {
      min-height: 48px;
      padding: 0 16px;
      flex-shrink: 0;

      .ant-card-head-wrapper {
        min-height: 48px;
      }
    }

    :deep(.ant-card-body) {
      flex: 1;
      padding: 16px;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }

    .card-title {
      display: flex;
      align-items: center;
      gap: 8px;

      .file-icon {
        font-size: 16px;
        color: #ff4d4f;
      }

      .file-name {
        font-size: 14px;
        color: #1f1f1f;
        margin-right: 8px;
        word-break: break-all;
        max-width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .pdf-content {
      flex: 1;
      position: relative;
      background-color: #f5f5f5;
      border-radius: 2px;
      overflow: hidden;
      height: calc(100% - 48px);
      display: v-bind('showContent ? "block" : "none"');
    }

    .pdf-iframe {
      min-height: v-bind('typeof minHeight === "number" ? minHeight + "px" : minHeight');
      width: 100%;
      height: 100%;
      border: none;
    }

    .no-file {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      color: #999;

      .anticon {
        font-size: 32px;
        margin-bottom: 8px;
      }

      p {
        margin: 0;
        font-size: 14px;
      }
    }
  }
</style>
