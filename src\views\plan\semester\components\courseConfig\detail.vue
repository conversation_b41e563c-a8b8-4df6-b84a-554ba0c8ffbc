<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" width="100%" :minHeight="100" :showOkBtn="false" class="course-detail-modal">
    <template #insertFooter>
      <a-button type="primary" @click="closeModal" class="close-btn">
        {{ t('common.close', '关闭') }}
      </a-button>
    </template>
    <!-- 表单 -->
    <div class="detail-container">
      <a-form :colon="false" size="middle" layout="horizontal" labelAlign="right" :labelCol="{ style: { width: '120px' } }" :model="dataForm" ref="formRef">
        <!-- 基本信息 -->
        <div class="form-group">
          <div class="group-header">
            <span class="group-title"><icon-info-circle class="group-icon" /> 基本信息</span>
            <div class="divider"></div>
          </div>
          <a-card class="form-card" :bordered="false">
            <a-row :gutter="15">
              <a-col :span="24" class="ant-col-item">
                <a-form-item name="schoolName">
                  <template #label><span class="label-text">学校</span></template>
                  <div class="detail-value">{{ dataForm.schoolName || '-' }}</div>
                </a-form-item>
              </a-col>
              <a-col :span="24" class="ant-col-item">
                <a-form-item name="campusName">
                  <template #label><span class="label-text">校区</span></template>
                  <div class="detail-value">{{ dataForm.campusName || '-' }}</div>
                </a-form-item>
              </a-col>
              <a-col :span="24" class="ant-col-item">
                <a-form-item name="semesterName">
                  <template #label><span class="label-text">计划</span></template>
                  <div class="detail-value">{{ dataForm.planSemesterName || '-' }}</div>
                </a-form-item>
              </a-col>
              <a-col :span="24" class="ant-col-item">
                <a-form-item name="no">
                  <template #label><span class="label-text">课程号</span></template>
                  <div class="detail-value">{{ dataForm.no || '-' }}</div>
                </a-form-item>
              </a-col>
              <a-col :span="24" class="ant-col-item">
                <a-form-item name="name">
                  <template #label><span class="label-text">课程名(中)</span></template>
                  <div class="detail-value">{{ dataForm.name || '-' }}</div>
                </a-form-item>
              </a-col>
              <a-col :span="24" class="ant-col-item">
                <a-form-item name="englishName">
                  <template #label><span class="label-text">课程名(英)</span></template>
                  <div class="detail-value">{{ dataForm.englishName || '-' }}</div>
                </a-form-item>
              </a-col>
              <a-col :span="24" class="ant-col-item">
                <a-form-item name="templateFlag">
                  <template #label><span class="label-text">模板课程</span></template>
                  <div class="detail-value status-tag" :class="'status-' + dataForm.templateFlag">
                    <span class="dot" :class="'dot-' + dataForm.templateFlag"></span>
                    {{ xundaUtils.isRender(dataForm.templateFlag) || '-' }}
                  </div>
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
        </div>

        <!-- 课程详情 -->
        <div class="form-group">
          <div class="group-header">
            <span class="group-title"><icon-book class="group-icon" /> 课程详情</span>
            <div class="divider"></div>
          </div>
          <a-card class="form-card" :bordered="false">
            <a-row :gutter="15">
              <a-col :span="24" class="ant-col-item">
                <a-form-item name="courseType">
                  <template #label><span class="label-text">课程类型</span></template>
                  <div class="detail-value">{{ dataForm.courseType || '-' }}</div>
                </a-form-item>
              </a-col>
              <a-col :span="24" class="ant-col-item">
                <a-form-item name="description">
                  <template #label><span class="label-text">说明</span></template>
                  <div class="detail-value">{{ dataForm.description || '-' }}</div>
                </a-form-item>
              </a-col>
              <a-col :span="24" class="ant-col-item">
                <a-form-item name="scoreConfig">
                  <template #label><span class="label-text">成绩配置</span></template>
                  <div class="detail-value">{{ dataForm.scoreConfig || '-' }}</div>
                </a-form-item>
              </a-col>
              <a-col :span="24" class="ant-col-item">
                <a-form-item name="id">
                  <template #label><span class="label-text">系统 ID</span></template>
                  <div class="detail-value">{{ dataForm.id || '-' }}</div>
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
        </div>

        <!-- 统计数据 -->
        <div class="form-group">
          <div class="group-header">
            <span class="group-title"><icon-bar-chart class="group-icon" /> 统计数据</span>
            <div class="divider"></div>
          </div>
          <a-card class="form-card" :bordered="false">
            <a-row :gutter="15">
              <a-col :span="24" class="ant-col-item">
                <a-form-item name="credit">
                  <template #label><span class="label-text">学分</span></template>
                  <div class="detail-value number-highlight">{{ dataForm.credit || '-' }}</div>
                </a-form-item>
              </a-col>
              <a-col :span="24" class="ant-col-item">
                <a-form-item name="useCount">
                  <template #label><span class="label-text">开课次数</span></template>
                  <div class="detail-value number-highlight">{{ dataForm.useCount || '-' }}</div>
                </a-form-item>
              </a-col>
              <a-col :span="24" class="ant-col-item">
                <a-form-item name="notInCount">
                  <template #label><span class="label-text">不计课程数</span></template>
                  <div class="detail-value number-highlight">{{ dataForm.notInCount || '-' }}</div>
                </a-form-item>
              </a-col>
              <a-col :span="24" class="ant-col-item stat-item">
                <a-form-item name="total">
                  <template #label><span class="label-text">可选人数</span></template>
                  <div class="detail-value number-highlight total-value">
                    <icon-team class="stat-icon" />
                    {{ dataForm.total || '-' }}
                  </div>
                </a-form-item>
              </a-col>
              <a-col :span="24" class="ant-col-item stat-item">
                <a-form-item name="selected">
                  <template #label><span class="label-text">已选人数</span></template>
                  <div class="detail-value number-highlight selected-value">
                    <icon-user-add class="stat-icon" />
                    {{ dataForm.selected || '-' }}
                  </div>
                </a-form-item>
              </a-col>
              <a-col :span="24" class="ant-col-item stat-item">
                <a-form-item name="remain">
                  <template #label><span class="label-text">剩余空位</span></template>
                  <div class="detail-value number-highlight remain-value">
                    <icon-scissor class="stat-icon" />
                    {{ dataForm.remain || '-' }}
                  </div>
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
        </div>
      </a-form>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { getDetailInfo } from '.';
  import { reactive, toRefs, nextTick, ref, h } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { usePermission } from '@/hooks/web/usePermission';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
  import { xundaUtils } from '@/utils/xunda';
  import {
    InfoCircleOutlined as IconInfoCircle,
    BookOutlined as IconBook,
    BarChartOutlined as IconBarChart,
    TeamOutlined as IconTeam,
    UserAddOutlined as IconUserAdd,
    ScissorOutlined as IconScissor,
  } from '@ant-design/icons-vue';
  interface State {
    dataForm: any;
    title: string;
    maskConfig: any;
    optionsObj: any;
  }
  defineOptions({ name: 'Detail' });
  const emit = defineEmits(['reload']);
  const { createMessage, createConfirm } = useMessage();
  const [registerModal, { openModal, setModalProps, closeModal }] = useModal();
  const { t } = useI18n();
  const detailRef = ref<any>(null);
  const visitRecordGridRef = ref<any>(null);
  const state = reactive<State>({
    dataForm: {},
    title: t('common.detailText', '详情'),
    maskConfig: {},
    optionsObj: {
      //选项配置
      defaultProps: { label: 'fullName', value: 'enCode' }, // 默认下拉选择键值
    },
  });

  const { title, dataForm, optionsObj, maskConfig } = toRefs(state);
  // 表单权限
  const { hasFormP } = usePermission();

  defineExpose({ init });

  function init(data) {
    getAllSelectOptions();
    state.dataForm.id = data.id;
    openModal();
    nextTick(() => {
      setTimeout(initData, 0);
    });
    setModalProps({
      onCancel: () => {
        closeModal();
      },
    });
  }

  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      closeModal();
    }
  }

  function getData(id) {
    getDetailInfo(id).then(res => {
      state.dataForm = res.data || {};
      nextTick(() => {
        changeLoading(false);
      });
    });
  }

  function setFormProps(data) {
    setModalProps(data);
  }

  function changeLoading(loading) {
    setFormProps({ loading });
  }

  function getAllSelectOptions() {}
</script>

<style lang="less" scoped>
  .course-detail-modal {
    :deep(.ant-modal-content) {
      border-radius: 8px;
      overflow: hidden;

      .ant-modal-header {
        padding: 16px 24px;
        border-bottom: 1px solid #f0f0f0;
        background-color: #fafafa;

        .ant-modal-title {
          font-size: 18px;
          font-weight: 500;
          color: #262626;
        }
      }

      .ant-modal-body {
        padding: 20px 24px;
      }

      .ant-modal-footer {
        padding: 12px 24px;
        border-top: 1px solid #f0f0f0;
        text-align: right;

        .close-btn {
          min-width: 80px;
        }
      }
    }

    .detail-container {
      max-height: 65vh;
      overflow-y: auto;
      padding: 0 12px;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-track {
        background-color: rgba(0, 0, 0, 0.05);
      }
    }

    .form-group {
      margin-bottom: 24px;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

      .group-header {
        display: flex;
        flex-direction: column;
        padding: 12px 16px 0;

        .group-title {
          font-size: 16px;
          font-weight: 600;
          color: #1f1f1f;
          margin-bottom: 8px;
          display: flex;
          align-items: center;

          .group-icon {
            margin-right: 8px;
            font-size: 18px;
            color: #1890ff;
          }
        }

        .divider {
          height: 1px;
          background: linear-gradient(to right, #1890ff, #e8e8e8);
          margin-bottom: 8px;
        }
      }

      .form-card {
        background-color: #fff;
      }
    }

    .ant-col-item {
      margin-bottom: 6px;

      &:nth-child(odd) {
        .ant-form-item {
          background-color: rgba(0, 0, 0, 0.02);
        }
      }

      &.stat-item {
        .ant-form-item {
          border-left: 3px solid #1890ff;
        }
      }

      .ant-form-item {
        margin-bottom: 0;
        padding: 12px 16px;
        border-radius: 6px;
        transition: all 0.3s ease;

        &:hover {
          background-color: rgba(0, 0, 0, 0.04);
        }
      }
    }

    .label-text {
      color: #666;
      font-weight: 500;
    }

    .detail-value {
      font-size: 14px;
      color: #262626;
      word-break: break-word;

      &.number-highlight {
        font-weight: 600;
        font-size: 15px;

        &.total-value {
          color: #52c41a;
        }

        &.selected-value {
          color: #1890ff;
        }

        &.remain-value {
          color: #fa8c16;
        }
      }

      .stat-icon {
        margin-right: 6px;
      }

      &.status-tag {
        display: inline-flex;
        align-items: center;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 13px;

        &.status-true {
          background-color: #e6f7ff;
          color: #1890ff;
        }

        &.status-false {
          background-color: #fff1f0;
          color: #ff4d4f;
        }

        .dot {
          display: inline-block;
          width: 6px;
          height: 6px;
          border-radius: 50%;
          margin-right: 6px;

          &.dot-true {
            background-color: #1890ff;
          }

          &.dot-false {
            background-color: #ff4d4f;
          }
        }
      }
    }
  }
</style>
