<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" width="800px" :minHeight="100" :showOkBtn="false">
    <template #insertFooter> </template>

    <div class="notice-detail-container">
      <!-- 标题区域 -->
      <div class="notice-title-section">
        <h2 class="notice-title">{{ dataForm.title }}</h2>
        <div class="notice-meta">
          <a-tag color="blue">{{ '项目：' + optionText(dataForm.semesterId, planNameList, { value: 'id', label: 'name' }) }}</a-tag>
          <span v-if="dataForm.createTime" class="notice-time"><CalendarOutlined /> {{ toDateString(dataForm.createTime) }}</span>
        </div>
      </div>

      <!-- 内容区域 -->
      <a-divider />
      <div class="notice-content-section">
        <div class="section-header">
          <NotificationFilled />
          <span>通知内容</span>
        </div>
        <div class="notice-content" v-html="dataForm.content"></div>
      </div>

      <!-- 附件区域 -->
      <a-divider v-if="dataForm.attachment" />
      <div class="notice-attachment-section" v-if="dataForm.attachment">
        <div class="section-header">
          <PaperClipOutlined />
          <span>附件信息</span>
        </div>
        <div class="notice-attachment">
          <FileOutlined />
          <span>{{ dataForm.attachment }}</span>
        </div>
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { getDetailInfo } from '@/views/plan/notice';
  import { reactive, toRefs, nextTick, ref } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { usePermission } from '@/hooks/web/usePermission';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { optionText } from '@/utils/xunda';
  import { toDateString } from '@/utils/xunda';
  import { getForSelect } from '../semester';
  import { CalendarOutlined, NotificationFilled, PaperClipOutlined, FileOutlined, InfoCircleFilled } from '@ant-design/icons-vue';
  interface State {
    dataForm: any;
    title: string;
    optionsObj: any;
  }

  defineOptions({ name: 'Detail' });
  const planNameList = ref<any>([]);
  const emit = defineEmits(['reload']);
  const { createMessage, createConfirm } = useMessage();
  const [registerModal, { openModal, setModalProps, closeModal }] = useModal();

  const { t } = useI18n();
  const detailRef = ref<any>(null);
  const visitRecordGridRef = ref<any>(null);
  const state = reactive<State>({
    dataForm: {},
    title: t('common.detailText', '通知详情'),
    optionsObj: {
      //选项配置
      defaultProps: { label: 'fullName', value: 'enCode' }, // 默认下拉选择键值
    },
  });

  const { title, dataForm, optionsObj } = toRefs(state);
  // 表单权限
  const { hasFormP } = usePermission();

  defineExpose({ init });

  function init(data) {
    getAllSelectOptions();
    state.dataForm.id = data.id;
    openModal();
    nextTick(() => {
      setTimeout(initData, 0);
    });
  }
  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      closeModal();
    }
  }
  function getData(id) {
    getDetailInfo(id).then(res => {
      state.dataForm = res.data || {};
      nextTick(() => {
        changeLoading(false);
      });
    });
  }

  function setFormProps(data) {
    setModalProps(data);
  }
  function changeLoading(loading) {
    setFormProps({ loading });
  }

  function getAllSelectOptions() {
    getForSelect({
      dataType: 1,
    }).then(res => {
      planNameList.value = res.data.list;
    });
  }
</script>

<style lang="less" scoped>
  .notice-detail-container {
    padding: 16px;
    background-color: #fff;

    .notice-title-section {
      text-align: center;
      margin-bottom: 20px;

      .notice-title {
        font-size: 24px;
        font-weight: bold;
        color: #1890ff;
        margin-bottom: 12px;
      }

      .notice-meta {
        display: flex;
        justify-content: center;
        gap: 16px;
        color: #666;

        .notice-time {
          display: inline-flex;
          align-items: center;
          gap: 4px;
        }
      }
    }

    .section-header {
      font-size: 16px;
      font-weight: bold;
      color: #1890ff;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .notice-content-section {
      margin-bottom: 20px;

      .notice-content {
        padding: 16px;
        background-color: #f8f8f8;
        border-radius: 4px;
        line-height: 1.6;
      }
    }

    .notice-attachment-section {
      margin-bottom: 20px;

      .notice-attachment {
        display: flex;
        align-items: center;
        gap: 8px;
        background-color: #f0f7ff;
        padding: 12px;
        border-radius: 4px;
        border: 1px dashed #1890ff;
      }
    }

    .notice-info-section {
      margin-bottom: 20px;
    }
  }
</style>
