import { defHttp } from '@/utils/http/axios';
// 基础Api
export const semesterApi = '/api/plan/semester';
// 获取(转换数据)
export function getAdultHighEducationInfoAsync(id, type) {
  return defHttp.get({ url: semesterApi + `/getAdultHighEducationInfo/` + id + `/` + type });
}

export function setAdultHighEducationInfoAsync(data) {
  return defHttp.post({ url: semesterApi + `/setAdultHighEducationInfo`, data });
}
