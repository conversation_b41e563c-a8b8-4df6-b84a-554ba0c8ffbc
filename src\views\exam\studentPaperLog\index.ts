import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';

// 基础Api
export const studentPaperLogApi = '/api/exam/studentPaperLog';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: studentPaperLogApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: studentPaperLogApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: studentPaperLogApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: studentPaperLogApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: studentPaperLogApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: studentPaperLogApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: studentPaperLogApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: studentPaperLogApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: studentPaperLogApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: studentPaperLogApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: studentPaperLogApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: studentPaperLogApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: studentPaperLogApi + `/getForSelect`, data });
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('主键'),
    dataIndex: 'id',
    width: 120,
  },
  {
    title: t('试卷'),
    dataIndex: 'paperId',
    width: 120,
  },
  {
    title: t('事件'),
    dataIndex: 'title',
    width: 120,
  },
  {
    title: t('内容'),
    dataIndex: 'content',
    width: 120,
  },
  {
    title: t('状态'),
    dataIndex: 'examState',
    width: 120,
  },
  {
    title: t('附件'),
    dataIndex: 'attachment',
    width: 120,
  },
];
