<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    width="1000px"
    :minHeight="250"
    :cancelText="t('common.cancelText', '取消')"
    :okText="t('common.okText', '确定')"
    @ok="handleSubmit"
    :destroy-on-close="true"
    :closeFunc="onClose">
    <template #title>
      <div class="modal-header">
        <span class="title">{{ title }}</span>
        <a-space-compact size="small" block v-if="dataForm.id">
          <a-tooltip :title="t('common.prevRecord')">
            <a-button size="small" :disabled="getPrevDisabled" @click="handlePrev">
              <i class="icon-ym icon-ym-caret-left text-10px"></i>
            </a-button>
          </a-tooltip>
          <a-tooltip :title="t('common.nextRecord')">
            <a-button size="small" :disabled="getNextDisabled" @click="handleNext">
              <i class="icon-ym icon-ym-caret-right text-10px"></i>
            </a-button>
          </a-tooltip>
        </a-space-compact>
      </div>
    </template>
    <template #insertFooter>
      <div class="float-left mt-5px">
        <XundaCheckboxSingle v-model:value="submitType" :label="continueText" />
      </div>
    </template>
    <a-tabs v-model:activeKey="activeKey" type="card" class="campus-form-tabs">
      <a-tab-pane key="basic" tab="基本信息">
        <div class="dynamic-form">
          <a-form
            :colon="false"
            size="middle"
            layout="horizontal"
            labelAlign="left"
            :labelCol="{ style: { width: '120px' } }"
            :model="dataForm"
            :rules="dataRule"
            ref="formRef">
            <div class="form-section">
              <div class="form-section-title" v-if="false">基础配置</div>
              <a-row :gutter="24">
                <a-col :span="12" class="ant-col-item" :hidden="true">
                  <a-form-item name="schoolId">
                    <template #label>学校</template>
                    <XundaSelect
                      v-model:value="dataForm.schoolId"
                      :disabled="false"
                      @change="changeData('schoolId', -1)"
                      placeholder="请选择学校"
                      :allowClear="true"
                      :style="{ width: '100%' }"
                      showSearch
                      :options="optionsObj.schoolIdOptions"
                      :fieldNames="optionsObj.schoolIdFieldNames">
                    </XundaSelect>
                  </a-form-item>
                </a-col>
                <a-col :span="12" class="ant-col-item" :hidden="true">
                  <a-form-item name="campusId">
                    <template #label>校区</template>
                    <XundaSelect
                      v-model:value="dataForm.campusId"
                      :disabled="false"
                      @change="changeData('campusId', -1)"
                      placeholder="请选择校区"
                      :allowClear="true"
                      :style="{ width: '100%' }"
                      showSearch
                      :options="optionsObj.campusIdOptions"
                      :fieldNames="optionsObj.campusIdFieldNames">
                    </XundaSelect>
                  </a-form-item>
                </a-col>
              </a-row>
            </div>

            <div class="form-section">
              <div class="form-section-title">课程信息</div>
              <a-row :gutter="24">
                <a-col :span="12" class="ant-col-item" :hidden="false">
                  <a-form-item name="courseType">
                    <template #label>课程类型</template>
                    <XundaInput
                      v-model:value="dataForm.courseType"
                      :disabled="false"
                      @change="changeData('courseType', -1)"
                      placeholder="请输入课程类型"
                      :allowClear="true"
                      :style="{ width: '100%' }">
                    </XundaInput>
                  </a-form-item>
                </a-col>
                <a-col :span="12" class="ant-col-item" :hidden="false">
                  <a-form-item name="no">
                    <template #label>课程号</template>
                    <XundaInput
                      v-model:value="dataForm.no"
                      :disabled="false"
                      @change="changeData('no', -1)"
                      placeholder="请输入课程号"
                      :allowClear="true"
                      :style="{ width: '100%' }">
                    </XundaInput>
                  </a-form-item>
                </a-col>
                <a-col :span="12" class="ant-col-item" :hidden="false">
                  <a-form-item name="name">
                    <template #label>课程名(中)</template>
                    <XundaInput
                      v-model:value="dataForm.name"
                      :disabled="false"
                      @change="changeData('name', -1)"
                      placeholder="请输入课程名称(中文)"
                      :allowClear="true"
                      :style="{ width: '100%' }">
                    </XundaInput>
                  </a-form-item>
                </a-col>
                <a-col :span="12" class="ant-col-item" :hidden="false">
                  <a-form-item name="englishName">
                    <template #label>课程名(英)</template>
                    <XundaInput
                      v-model:value="dataForm.englishName"
                      :disabled="false"
                      @change="changeData('englishName', -1)"
                      placeholder="请输入课程名称(英文)"
                      :allowClear="true"
                      :style="{ width: '100%' }">
                    </XundaInput>
                  </a-form-item>
                </a-col>
              </a-row>
            </div>

            <div class="form-section">
              <div class="form-section-title">课程配置</div>
              <a-row :gutter="24">
                <a-col :span="8" class="ant-col-item" :hidden="false">
                  <a-form-item name="credit">
                    <template #label>学分</template>
                    <XundaInputNumber
                      v-model:value="dataForm.credit"
                      :disabled="false"
                      @change="changeData('credit', -1)"
                      placeholder="请输入学分"
                      :allowClear="true"
                      :style="{ width: '100%' }"
                      :precision="2"
                      :controls="true">
                    </XundaInputNumber>
                  </a-form-item>
                </a-col>
                <a-col :span="8" class="ant-col-item" :hidden="false">
                  <a-form-item name="useCount">
                    <template #label>开课次数</template>
                    <XundaInputNumber
                      v-model:value="dataForm.useCount"
                      :disabled="false"
                      @change="changeData('useCount', -1)"
                      placeholder="请输入开课次数"
                      :allowClear="true"
                      :style="{ width: '100%' }"
                      :precision="0"
                      :controls="true">
                    </XundaInputNumber>
                  </a-form-item>
                </a-col>
                <a-col :span="8" class="ant-col-item" :hidden="false">
                  <a-form-item name="notInCount">
                    <template #label>不计课程数</template>
                    <XundaSwitch
                      v-model:value="dataForm.notInCount"
                      :disabled="false"
                      @change="changeData('notInCount', -1)"
                      placeholder="请选择是否不计课程数">
                    </XundaSwitch>
                  </a-form-item>
                </a-col>
                <a-col :span="24" class="ant-col-item" :hidden="false">
                  <a-form-item name="description">
                    <template #label>课程说明</template>
                    <XundaTextarea
                      v-model:value="dataForm.description"
                      :disabled="false"
                      @change="changeData('description', -1)"
                      placeholder="请输入课程说明"
                      :allowClear="true"
                      :autoSize="{ minRows: 3, maxRows: 5 }"
                      :style="{ width: '100%' }">
                    </XundaTextarea>
                  </a-form-item>
                </a-col>
              </a-row>
            </div>

            <div class="form-section">
              <div class="form-section-title">选课信息</div>
              <a-row :gutter="24">
                <a-col :span="8" class="ant-col-item" :hidden="false">
                  <a-form-item name="total">
                    <template #label>可选人数</template>
                    <XundaInputNumber
                      v-model:value="dataForm.total"
                      :disabled="false"
                      @change="changeData('total', -1)"
                      placeholder="请输入可选人数"
                      :allowClear="true"
                      :style="{ width: '100%' }"
                      :precision="0"
                      :controls="true">
                    </XundaInputNumber>
                  </a-form-item>
                </a-col>
                <a-col :span="8" class="ant-col-item" :hidden="true">
                  <a-form-item name="selected">
                    <template #label>已选人数</template>
                    <XundaInputNumber
                      v-model:value="dataForm.selected"
                      :disabled="false"
                      @change="changeData('selected', -1)"
                      placeholder="请输入已选人数"
                      :allowClear="true"
                      :style="{ width: '100%' }"
                      :precision="0"
                      :controls="true">
                    </XundaInputNumber>
                  </a-form-item>
                </a-col>
                <a-col :span="8" class="ant-col-item" :hidden="true">
                  <a-form-item name="remain">
                    <template #label>剩余空位</template>
                    <XundaInputNumber
                      v-model:value="dataForm.remain"
                      :disabled="false"
                      @change="changeData('remain', -1)"
                      placeholder="请输入剩余空位"
                      :allowClear="true"
                      :style="{ width: '100%' }"
                      :precision="0"
                      :controls="true">
                    </XundaInputNumber>
                  </a-form-item>
                </a-col>
                <a-col :span="24" class="ant-col-item" :hidden="true">
                  <a-form-item name="scoreConfig">
                    <template #label>成绩配置</template>
                    <XundaInput
                      v-model:value="dataForm.scoreConfig"
                      :disabled="false"
                      @change="changeData('scoreConfig', -1)"
                      placeholder="请输入成绩配置"
                      :allowClear="true"
                      :style="{ width: '100%' }">
                    </XundaInput>
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </a-form>
        </div>
      </a-tab-pane>
      <a-tab-pane key="prerequisite" tab="前置课程" v-if="false">
        <CourseTable ref="prerequisiteTableRef" :data-source="dataForm.prerequisiteCourses"> </CourseTable>
      </a-tab-pane>
      <a-tab-pane key="corequisite" tab="绑定课程" v-if="false">
        <CourseTable ref="corequisiteTableRef" :data-source="dataForm.corequisiteCourses"> </CourseTable>
      </a-tab-pane>
    </a-tabs>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { create, update, getInfo } from '@/views/ea/course';
  import { BasicTable } from '@/components/Table';
  import { reactive, toRefs, nextTick, ref, unref, computed, inject } from 'vue';
  import CourseSelectModal from './CourseSelectModal.vue';
  import CourseTable from './CourseTable.vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useUserStore } from '@/store/modules/user';
  import type { FormInstance } from 'ant-design-vue';
  import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
  // 表单权限
  import { usePermission } from '@/hooks/web/usePermission';
  import { XundaSelect } from '@/components/Xunda';
  import { getForSelect as getSchoolForSelect } from '@/views/ea/school';
  import { getForSelect as getCampusForSelect } from '@/views/ea/campus';
  import { getForSelect as getSemesterForSelect } from '@/views/plan/semester';
  interface State {
    dataForm: any;
    tableRows: any;
    dataRule: any;
    optionsObj: any;
    childIndex: any;
    isEdit: any;
    interfaceRes: any;
    //可选范围默认值
    ableAll: any;
    //掩码配置
    maskConfig: any;
    //定位属性
    locationScope: any;
    title: string;
    continueText: string;
    allList: any[];
    currIndex: number;
    isContinue: boolean;
    submitType: number;
    showContinueBtn: boolean;
    activeKey: 'basic'; // 新增：默认激活第一个tab
    prerequisiteCourses: any[];
    corequisiteCourses: any[];
  }

  const emit = defineEmits(['reload']);
  const getLeftTreeActiveInfo: (() => any) | null = inject('getLeftTreeActiveInfo', null);
  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const [registerModal, { openModal, setModalProps }] = useModal();
  const modalTitle = ref('');
  const selectedModalData = ref([]);
  const modalType = ref(''); // 'prerequisite' 或 'corequisite'
  const formRef = ref<FormInstance>();
  const prerequisiteTableRef = ref();
  const corequisiteTableRef = ref();
  const state = reactive<State>({
    prerequisiteCourses: [],
    corequisiteCourses: [],
    dataForm: {
      id: undefined,
      schoolId: '',
      campusId: undefined,
      semesterId: '',
      templateFlag: undefined,
      courseType: '',
      no: '',
      name: '',
      englishName: '',
      credit: undefined,
      useCount: undefined,
      notInCount: undefined,
      description: '',
      total: undefined,
      selected: undefined,
      remain: undefined,
      scoreConfig: '',
    },
    tableRows: {},
    dataRule: {
      id: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '主键不能为空'),
          trigger: 'blur',
        },
      ],
      schoolId: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '学校不能为空'),
          trigger: 'blur',
        },
      ],
      templateFlag: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '模板课程不能为空'),
          trigger: 'blur',
        },
      ],
      name: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '课程名(中)不能为空'),
          trigger: 'blur',
        },
      ],
      notInCount: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '不计课程数不能为空'),
          trigger: 'blur',
        },
      ],
    },
    optionsObj: {
      //选项配置
      defaultProps: { label: 'fullName', value: 'enCode' }, // 默认下拉选择键值
    },
    childIndex: -1,
    isEdit: false,
    interfaceRes: {
      id: [],
      schoolId: [],
      campusId: [],
      semesterId: [],
      templateFlag: [],
      courseType: [],
      no: [],
      name: [],
      englishName: [],
      credit: [],
      useCount: [],
      notInCount: [],
      description: [],
      total: [],
      selected: [],
      remain: [],
      scoreConfig: [],
    },
    //可选范围默认值
    ableAll: {},

    //掩码配置
    maskConfig: {},
    //定位属性
    locationScope: {
      faddressDetail: [],
    },
    title: '',
    continueText: '',
    allList: [],
    currIndex: 0,
    isContinue: false,
    submitType: 0,
    showContinueBtn: true,
    activeKey: 'basic', // 新增：默认激活第一个tab
  });
  const { title, continueText, showContinueBtn, dataRule, dataForm, optionsObj, ableAll, maskConfig, submitType, activeKey } = toRefs(state);

  const getPrevDisabled = computed(() => state.currIndex === 0);
  const getNextDisabled = computed(() => state.currIndex === state.allList.length - 1);
  // 表单权限
  const { hasFormP } = usePermission();

  defineExpose({ init });

  const semesterId = ref('');
  function init(data) {
    semesterId.value = data.semesterId;
    state.submitType = 0;
    state.isContinue = false;
    state.title = !data.id ? t('common.add2Text', '新增') : t('common.editText', '编辑');
    state.continueText = !data.id ? t('common.continueAndAddText', '确定并新增') : t('common.continueText', '确定并继续');
    setFormProps({ continueLoading: false });
    state.dataForm.id = data.id;
    openModal();
    state.allList = data.allList;
    state.currIndex = state.allList.length && data.id ? state.allList.findIndex(item => item.id === data.id) : 0;
    getAllSelectOptions();
    nextTick(() => {
      getForm().resetFields();
      setTimeout(initData, 0);
    });
  }
  function initData() {
    corequisiteTableRef.value?.init();
    prerequisiteTableRef.value?.init();
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      // 设置默认值
      state.dataForm = {
        id: undefined,
        schoolId: '1922122900279865345',
        campusId: undefined,
        semesterId: semesterId.value,
        templateFlag: false,
        courseType: '',
        no: '',
        name: '',
        englishName: '',
        credit: undefined,
        useCount: undefined,
        notInCount: undefined,
        description: '',
        total: undefined,
        selected: undefined,
        remain: undefined,
        scoreConfig: '',
      };

      if (getLeftTreeActiveInfo) state.dataForm = { ...state.dataForm, ...(getLeftTreeActiveInfo() || {}) };
      state.childIndex = -1;
      changeLoading(false);
    }
  }
  function getForm() {
    const form = unref(formRef);
    if (!form) {
      throw new Error('form is null!');
    }
    return form;
  }
  function getData(id) {
    getInfo(id).then(res => {
      state.dataForm = res.data || {};
      state.prerequisiteCourses = res.data?.prerequisiteCourses || [];
      state.corequisiteCourses = res.data?.corequisiteCourses || [];
      state.childIndex = -1;
      getCampusOptions({ schoolId: state.dataForm.schoolId });
      changeLoading(false);
    });
  }
  async function handleSubmit(type) {
    try {
      const values = await getForm()?.validate();
      if (!values) return;
      setFormProps({ confirmLoading: true });

      state.dataForm.prerequisiteCourses = prerequisiteTableRef.value?.getCourses();
      state.dataForm.corequisiteCourses = corequisiteTableRef.value?.getCourses();
      const formMethod = state.dataForm.id ? update : create;
      console.log('values', state.dataForm);
      formMethod(state.dataForm)
        .then(res => {
          createMessage.success(res.msg);
          setFormProps({ confirmLoading: false });
          if (state.submitType == 1) {
            initData();
            state.isContinue = true;
          } else {
            setFormProps({ open: false });
            emit('reload');
          }
        })
        .catch(() => {
          setFormProps({ confirmLoading: false });
        });
    } catch (_) {}
  }

  // 跳转上一个
  function handlePrev() {
    state.currIndex--;
    handleGetNewInfo();
  }

  // 跳转下一个
  function handleNext() {
    state.currIndex++;
    handleGetNewInfo();
  }

  // 重新获取编辑信息
  function handleGetNewInfo() {
    changeLoading(true);
    getForm().resetFields();
    const id = state.allList[state.currIndex].id;
    getData(id);
  }

  function setFormProps(data) {
    setModalProps(data);
  }

  //
  function changeLoading(loading) {
    setModalProps({ loading });
  }

  // 关闭弹窗
  async function onClose() {
    if (state.isContinue) emit('reload');
    return true;
  }

  function changeData(model, index) {
    state.isEdit = false;
    state.childIndex = index;
    if (model == 'schoolId') {
      state.dataForm.campusId = undefined;
      if (state.dataForm.schoolId) {
        campusIdDisabled.value = false;
        getCampusOptions({ schoolId: state.dataForm.schoolId });
      } else {
        campusIdDisabled.value = true;
      }
    }
    for (let key in state.interfaceRes) {
      if (key != model) {
        let faceReList = state.interfaceRes[key];
        for (let i = 0; i < faceReList.length; i++) {
          let relationField = faceReList[i].relationField;
          if (relationField) {
            let modelAll = relationField.split('-');
            let faceMode = '';
            let faceMode2 = modelAll.length == 2 ? modelAll[0].substring(0, modelAll[0].length - 4) + modelAll[1] : '';
            for (let i = 0; i < modelAll.length; i++) {
              faceMode += modelAll[i];
            }
            if (faceMode == model || faceMode2 == model) {
              let options = 'get' + key + 'Options';
              eval(options)(true);
              changeData(key, index);
            }
          }
        }
      }
    }
  }

  const campusIdDisabled = ref(true);
  function getCampusOptions(params) {
    campusIdDisabled.value = false;
    getCampusForSelect({
      dataType: 1,
      ...params,
    }).then(res => {
      state.optionsObj.campusIdOptions = res.data.list;
    });
  }

  function getAllSelectOptions() {
    state.optionsObj.schoolIdFieldNames = { label: 'name', value: 'id' };
    getSchoolForSelect({
      dataType: 1,
    }).then(res => {
      state.optionsObj.schoolIdOptions = res.data.list;
    });

    state.optionsObj.semesterIdFieldNames = { label: 'name', value: 'id' };
    getSemesterForSelect({
      dataType: 1,
    }).then(res => {
      state.optionsObj.semesterIdOptions = res.data.list;
    });
    state.optionsObj.campusIdFieldNames = { label: 'name', value: 'id' };
  }
</script>

<style lang="less" scoped>
  .campus-form-tabs {
    :deep(.ant-tabs-nav) {
      margin-bottom: 16px;
      background: #fafafa;
      padding: 8px 8px 0;
      border-radius: 4px;
    }
  }

  .dynamic-form {
    padding: 16px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);

    .ant-col-item {
      margin-bottom: 16px;
      transition: all 0.3s ease-in-out;

      &:hover {
        transform: translateY(-1px);
      }
    }

    :deep(.ant-form-item) {
      margin-bottom: 16px;

      .ant-form-item-label {
        font-weight: 500;
        color: #1f2937;
      }

      .ant-input,
      .ant-input-number,
      .ant-select-selector {
        border-radius: 4px;
        transition: all 0.3s;

        &:hover,
        &:focus {
          border-color: #4096ff;
          box-shadow: 0 0 0 2px rgba(64, 150, 255, 0.1);
        }
      }
    }
  }

  .text-16px {
    font-size: 16px !important;
  }

  .modal-header {
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;

    .title {
      font-size: 16px;
      font-weight: 500;
      color: #1f2937;
      flex: 1;
    }
  }

  .form-section {
    margin-bottom: 24px;

    &-title {
      font-size: 14px;
      font-weight: 500;
      color: #1f2937;
      margin-bottom: 16px;
      padding-left: 8px;
      border-left: 3px solid #4096ff;
    }
  }
</style>
