import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();

// 基础Api
export const studentApi = '/api/ea/student';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: studentApi + `/getList`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: studentApi + `/getForSelect`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: studentApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: studentApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: studentApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: studentApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: studentApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: studentApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: studentApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: studentApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: studentApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: studentApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: studentApi + `/exportExceptionData`, data });
}
/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'no', // 学号
    label: t('ea.student.no'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    dataIndex: 'no', // 学号
    title: t('ea.student.no'),
    width: 120,
  },
];
