<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" width="600px" :minHeight="100" :showOkBtn="false">
    <template #insertFooter> </template>
    <!-- 表单 -->
    <a-row class="dynamic-form">
      <a-form :colon="false" size="middle" layout="horizontal" labelAlign="right" :labelCol="{ style: { width: '100px' } }" :model="dataForm" ref="formRef">
        <a-row :gutter="15">
          <!-- 具体表单 -->
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="id">
              <template #label>主键 </template>
              <p>{{ dataForm.id }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="semesterId">
              <template #label>计划 </template>
              <p>{{ dataForm.semesterId }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="userId">
              <template #label>用户 </template>
              <p>{{ dataForm.userId }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="name">
              <template #label>姓名 </template>
              <p>{{ dataForm.name }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="firstName">
              <template #label>名 </template>
              <p>{{ dataForm.firstName }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="lastName">
              <template #label>姓 </template>
              <p>{{ dataForm.lastName }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="englishName">
              <template #label>英文名 </template>
              <p>{{ dataForm.englishName }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="studentType">
              <template #label>学员类型 </template>
              <p>{{ dataForm.studentType }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="sex">
              <template #label>性别 </template>
              <p>{{ dataForm.sex }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="birthday">
              <template #label>生日 </template>
              <p>{{ dataForm.birthday }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="nationality">
              <template #label>国籍 </template>
              <p>{{ dataForm.nationality }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="idCardType">
              <template #label>证件类型 </template>
              <p>{{ dataForm.idCardType }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="idNo">
              <template #label>证件号 </template>
              <p>{{ dataForm.idNo }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="passportNo">
              <template #label>护照号 </template>
              <p>{{ dataForm.passportNo }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="email">
              <template #label>邮箱 </template>
              <p>{{ dataForm.email }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="phone1">
              <template #label>电话 </template>
              <p>{{ dataForm.phone1 }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="phone2">
              <template #label>备用电话 </template>
              <p>{{ dataForm.phone2 }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="emergencyPhone">
              <template #label>紧急联系电话 </template>
              <p>{{ dataForm.emergencyPhone }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="school">
              <template #label>就读/毕业学校 </template>
              <p>{{ dataForm.school }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="major">
              <template #label>专业 </template>
              <p>{{ dataForm.major }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="note">
              <template #label>备注 </template>
              <p>{{ dataForm.note }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="flowId">
              <template #label>流程 </template>
              <p>{{ dataForm.flowId }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="flowTaskId">
              <template #label>流程实例 </template>
              <p>{{ dataForm.flowTaskId }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="attachment">
              <template #label>附件 </template>
              <p>{{ dataForm.attachment }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="planTemplateId">
              <template #label>附加信息模板 </template>
              <p>{{ dataForm.planTemplateId }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="applyFormValue">
              <template #label>扩展信息 </template>
              <p>{{ dataForm.applyFormValue }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="photo">
              <template #label>照片 </template>
              <p>{{ dataForm.photo }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="totalFee">
              <template #label>总费用 </template>
              <p>{{ dataForm.totalFee }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="policyDedude">
              <template #label>政策优惠 </template>
              <p>{{ dataForm.policyDedude }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="policyDeduceDetial">
              <template #label>政策优惠明细 </template>
              <p>{{ dataForm.policyDeduceDetial }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="shouldPay">
              <template #label>应缴 </template>
              <p>{{ dataForm.shouldPay }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="payIn">
              <template #label>实缴 </template>
              <p>{{ dataForm.payIn }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="payRetrun">
              <template #label>退费 </template>
              <p>{{ dataForm.payRetrun }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="payDiff">
              <template #label>费用差额 </template>
              <p>{{ dataForm.payDiff }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="payState">
              <template #label>支付状态 </template>
              <p>{{ dataForm.payState }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="sign">
              <template #label>签名图片 </template>
              <p>{{ dataForm.sign }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="version">
              <template #label>报名模板版本 </template>
              <p>{{ dataForm.version }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="state">
              <template #label>学生状态 </template>
              <p>{{ dataForm.state }} </p>
            </a-form-item>
          </a-col>
          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { getDetailInfo } from '@/views/study/registration';
  import { reactive, toRefs, nextTick, ref } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { usePermission } from '@/hooks/web/usePermission';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { optionText } from '@/utils/xunda';
  import { toDateString, getDictionaryFullName, toFixedPercent } from '@/utils/myUtil';
  import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
  interface State {
    dataForm: any;
    title: string;
    optionsObj: any;
  }

  defineOptions({ name: 'Detail' });
  const emit = defineEmits(['reload']);
  const { createMessage, createConfirm } = useMessage();
  const [registerModal, { openModal, setModalProps, closeModal }] = useModal();

  const { t } = useI18n();
  const detailRef = ref<any>(null);
  const visitRecordGridRef = ref<any>(null);
  const state = reactive<State>({
    dataForm: {},
    title: t('common.detailText', '详情'),
    optionsObj: {
      //选项配置
      defaultProps: { label: 'fullName', value: 'enCode' }, // 默认下拉选择键值
    },
  });

  const { title, dataForm, optionsObj } = toRefs(state);
  // 表单权限
  const { hasFormP } = usePermission();

  defineExpose({ init });

  function init(data) {
    getAllSelectOptions();
    state.dataForm.id = data.id;
    openModal();
    nextTick(() => {
      setTimeout(initData, 0);
    });
  }
  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      closeModal();
    }
  }
  function getData(id) {
    getDetailInfo(id).then(res => {
      state.dataForm = res.data || {};
      nextTick(() => {
        changeLoading(false);
      });
    });
  }

  function setFormProps(data) {
    setModalProps(data);
  }
  function changeLoading(loading) {
    setFormProps({ loading });
  }

  function getAllSelectOptions() {}
</script>
