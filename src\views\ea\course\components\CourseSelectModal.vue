<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" :width="900" @ok="handleSubmit" :destroy-on-close="true">
    <div class="search-area mb-4">
      <a-form layout="inline">
        <a-form-item label="课程编号">
          <a-input v-model:value="searchForm.no" placeholder="请输入课程编号" />
        </a-form-item>
        <a-form-item label="课程名称">
          <a-input v-model:value="searchForm.name" placeholder="请输入课程名称" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleSearch">搜索</a-button>
          <a-button class="ml-2" @click="handleReset">重置</a-button>
        </a-form-item>
      </a-form>
    </div>
    <BasicTable @register="registerTable" :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" @change="handleTableChange">
    </BasicTable>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import { courseColumns } from './components';
  import { BasicModal, useModal } from '@/components/Modal';
  import { BasicTable, useTable } from '@/components/Table';
  import { useMessage } from '@/hooks/web/useMessage';

  const props = defineProps({
    title: {
      type: String,
      default: '选择课程',
    },
    showSelected: {
      type: String,
      default: false,
    },
  });

  const emit = defineEmits(['register', 'success']);

  const { createMessage } = useMessage();
  const [registerModal, { openModal, closeModal }] = useModal();

  // 搜索表单
  const searchForm = reactive({
    no: '',
    name: '',
  });

  // 表格数据
  const tableData = ref([]);
  const selectedRowKeys = ref([]);
  const selectedCourses = ref([]);

  const [registerTable, { reload, setLoading, getFetchParams, redoHeight, getSelectRowKeys, getSelectRows, clearSelectedRowKeys }] = useTable({
    columns: courseColumns,
    dataSource: tableData,
    clickToRowSelect: true,
    rowSelection: {
      type: 'checkbox',
    },
    canResize: true,
    resizeHeightOffset: 150,
  });

  // 初始化数据
  function init(data) {
    openModal();
    selectedCourses.value = data.selectedData || [];
    loadTableData();
  }

  // 加载表格数据
  function loadTableData() {
    // TODO: 调用API获取数据
    // tableData.value = [
    //   // 测试数据
    //   { id: 1, no: 'C001', name: '高等数学', credit: 4 },
    //   { id: 2, no: 'C002', name: '大学物理', credit: 3 },
    //   { id: 3, no: 'C003', name: '计算机基础', credit: 2 },
    //   { id: 4, no: 'C004', name: '线性代数', credit: 3 },
    //   { id: 5, no: 'C005', name: '大学英语', credit: 2 },
    //   { id: 6, no: 'C006', name: '数据结构', credit: 3 },
    //   { id: 7, no: 'C007', name: '计算机网络', credit: 3 },
    //   { id: 8, no: 'C008', name: '数据库原理', credit: 3 },
    //   { id: 9, no: 'C009', name: '操作系统', credit: 3 },
    //   { id: 10, no: 'C010', name: 'Java编程', credit: 3 },
    //   { id: 11, no: 'C011', name: '算法设计', credit: 3 },
    //   { id: 12, no: 'C012', name: '软件工程', credit: 3 },
    //   { id: 13, no: 'C013', name: '人工智能', credit: 3 },
    //   { id: 14, no: 'C014', name: '机器学习', credit: 3 },
    //   { id: 15, no: 'C015', name: 'Web开发', credit: 3 },
    //   { id: 16, no: 'C016', name: '移动开发', credit: 3 },
    //   { id: 17, no: 'C017', name: '网络安全', credit: 3 },
    //   { id: 18, no: 'C018', name: '计算机图形学', credit: 3 },
    //   { id: 19, no: 'C019', name: '游戏开发', credit: 3 },
    //   { id: 20, no: 'C020', name: '计算机应用', credit: 3 },
    //   { id: 21, no: 'C021', name: '计算机科学', credit: 3 },
    //   { id: 22, no: 'C022', name: '计算机工程', credit: 3 },
    //   { id: 23, no: 'C023', name: '计算机科学与技术', credit: 3 },
    //   { id: 24, no: 'C024', name: '计算机科学与工程', credit: 3 },
    //   { id: 25, no: 'C025', name: '计算机科学与技术', credit: 3 },
    //   { id: 26, no: 'C026', name: '计算机科学与技术', credit: 3 },
    //   { id: 27, no: 'C027', name: '计算机科学与技术', credit: 3 },
    //   { id: 28, no: 'C028', name: '计算机科学与技术', credit: 3 },
    // ];
  }

  // 搜索
  function handleSearch() {
    loadTableData();
  }

  // 重置搜索
  function handleReset() {
    searchForm.no = '';
    searchForm.name = '';
    handleSearch();
  }

  // 表格变化事件
  function handleTableChange(pag) {
    loadTableData();
  }

  // 选择行变化
  function onSelectChange(keys) {
    selectedRowKeys.value = keys;
  }

  // 提交选择
  function handleSubmit() {
    selectedCourses.value = getSelectRows();
    emit('success', selectedCourses.value);
    closeModal();
  }

  defineExpose({
    init,
  });
</script>

<style lang="less" scoped>
  .search-area {
    background-color: #fafafa;
    padding: 16px;
    border-radius: 4px;
  }

  .selected-courses {
    background-color: #fafafa;
    padding: 16px;
    border-radius: 4px;
  }
</style>
