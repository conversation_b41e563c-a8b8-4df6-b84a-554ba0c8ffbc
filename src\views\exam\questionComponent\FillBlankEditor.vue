<template>
    <div>
        <!-- 题干输入 -->
        <a-col :span="24" class="ant-col-item">
            <div class="form-item-wrapper">
                <div class="form-label">题干</div>
                <XundaEditor v-model:value="formData.content" :disabled="false" :allowClear="false" :height="300"
                    :style="{ width: '100%' }" />
            </div>
        </a-col>

        <!-- 填空项 -->
        <a-col :span="24" class="ant-col-item" :hidden="false">
            <div class="form-item-wrapper">
                <div class="form-label">填空答案</div>
                <div v-for="(blank, index) in localBlanks" :key="index" class="blank-item">
                    <span>第{{ index + 1 }}空</span>
                    <a-input v-model:value="blank.answer" placeholder="输入答案，多个用英文分号 ; 分隔，数字区间用 -"
                        style="width: 60%; margin: 0 8px;" />
                    <a-button type="link" danger @click="removeBlank(index)" v-if="localBlanks.length > 1">
                        删除
                    </a-button>
                </div>
                <a-button type="dashed" @click="addBlank" style="margin-top: 8px;">+ 添加更多</a-button>
            </div>
        </a-col>

        <div style="color: #888; font-size: 12px; padding-top: 10px;">
            <div>1. 一个空有多种答案时请用英文分号";"隔开，如：答案一;答案二</div>
            <div>2. 若试题答案是数字，可设定范围，如：1-9 表示 1 到 9 之间均正确（含边界）</div>
        </div>
    </div>
</template>

<script setup>
import { reactive, defineExpose, computed, ref, watch } from 'vue'
import { message } from 'ant-design-vue'

// 定义props，接收父组件的dataForm
const props = defineProps({
  formData: {
    type: Object,
    required: true
  }
})

// 直接使用父组件的formData
const formData = props.formData

// 本地填空数据
const localBlanks = ref([{ answer: '' }])

// 初始化填空数据
const initBlanks = () => {
  // 如果数据为空或无效，重置为默认状态
  if (!formData.answer || formData.answer === '') {
    localBlanks.value = [{ answer: '' }]
    return
  }
  
  try {
    const parsedBlanks = typeof formData.answer === 'string' ? JSON.parse(formData.answer) : formData.answer
    if (Array.isArray(parsedBlanks) && parsedBlanks.length > 0) {
      localBlanks.value = parsedBlanks.map(blank => ({
        answer: blank.answer || ''
      }))
    } else {
      // 如果解析结果为空数组，也重置为默认状态
      localBlanks.value = [{ answer: '' }]
    }
  } catch (e) {
    console.warn('Failed to parse blanks:', e)
    localBlanks.value = [{ answer: '' }]
  }
}

// 监听父组件数据变化，初始化填空
watch(() => [props.formData.answer, props.formData.content], () => {
  initBlanks()
}, { immediate: true, deep: true })

// 监听本地填空变化，同步到父组件
watch(localBlanks, (newBlanks) => {
  const blanksData = newBlanks.map((b, i) => ({
    index: i + 1,
    answer: b.answer.trim(),
  }))
  formData.answer = JSON.stringify(blanksData)
}, { deep: true })

// 添加/删除空
const addBlank = () => {
  localBlanks.value.push({ answer: '' })
}

const removeBlank = (index) => {
  localBlanks.value.splice(index, 1)
}

// 获取选项数据
const getData = () => {
    return {
        content: formData.content,
        blanks: localBlanks.value.map((b, i) => ({
            index: i + 1,
            answer: b.answer.trim(),
        }))
    }
}

// 校验
const validate = () => {
    if (!formData.content || !formData.content.trim()) {
        message.error('题干不能为空')
        return false
    }
    const hasEmpty = localBlanks.value.some(b => !b.answer.trim())
    if (hasEmpty) {
        message.error('每个空的答案不能为空')
        return false
    }
    return true
}

defineExpose({ validate, getData })
</script>

<style scoped>
.blank-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.form-item-wrapper {
  margin-bottom: 16px;
}

.form-label {
  font-weight: 500;
  margin-bottom: 8px;
  color: rgba(0, 0, 0, 0.85);
}
</style>
