import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';

// 基础Api
export const paperApi = '/api/exam/paper';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: paperApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: paperApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: paperApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: paperApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: paperApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: paperApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: paperApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: paperApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: paperApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: paperApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: paperApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: paperApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: paperApi + `/getForSelect`, data });
}

// 发布试卷
export function publishPaper(id) {
  return defHttp.put({ url: paperApi + `/publish/` + id });
}

// 取消发布试卷
export function unpublishPaper(id) {
  return defHttp.put({ url: paperApi + `/unpublish/` + id });
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'name',
    label: t('试卷名称'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('主键'),
    dataIndex: 'id',
    width: 120,
  },
  {
    title: t('计划'),
    dataIndex: 'semesterName',
    width: 120,
  },
  {
    title: t('课程'),
    dataIndex: 'courseName',
    width: 120,
  },
  {
    title: t('试卷名称'),
    dataIndex: 'name',
    width: 120,
  },
  {
    title: t('总分'),
    dataIndex: 'totalScore',
    width: 120,
  },
  {
    title: t('题目数'),
    dataIndex: 'questionCount',
    width: 120,
  },
  {
    title: t('试卷配置'),
    dataIndex: 'paperConfig',
    width: 120,
  },
  {
    title: t('试卷模式（随机，固定）'),
    dataIndex: 'paperMode',
    width: 120,
  },
  {
    title: t('试卷状态'),
    dataIndex: 'paperState',
    width: 120,
  },
  {
    title: t('考试须知'),
    dataIndex: 'regular',
    width: 120,
  },
];
