import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';

// 基础Api
export const serviceRegistrationApi = '/api/study/serviceRegistration';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: serviceRegistrationApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: serviceRegistrationApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: serviceRegistrationApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: serviceRegistrationApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: serviceRegistrationApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: serviceRegistrationApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: serviceRegistrationApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: serviceRegistrationApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: serviceRegistrationApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: serviceRegistrationApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: serviceRegistrationApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: serviceRegistrationApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: serviceRegistrationApi + `/getForSelect`, data });
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('主键'),
    dataIndex: 'id',
    width: 120,
  },
  {
    title: t('学员'),
    dataIndex: 'registrationId',
    width: 120,
  },
  {
    title: t('其他服务'),
    dataIndex: 'otherServiceId',
    width: 120,
  },
  {
    title: t('支付记录'),
    dataIndex: 'paymentId',
    width: 120,
  },
  {
    title: t('原价'),
    dataIndex: 'originalPrice',
    width: 120,
  },
  {
    title: t('实价'),
    dataIndex: 'realPrice',
    width: 120,
  },
  {
    title: t('支付状态'),
    dataIndex: 'payState',
    width: 120,
  },
  {
    title: t('币种'),
    dataIndex: 'currency',
    width: 120,
  },
  {
    title: t('服务要求'),
    dataIndex: 'requirement',
    width: 120,
  },
  {
    title: t('费用调整流程实例'),
    dataIndex: 'flowTaskId',
    width: 120,
  },
  {
    title: t('费用调整流程'),
    dataIndex: 'flowId',
    width: 120,
  },
];
