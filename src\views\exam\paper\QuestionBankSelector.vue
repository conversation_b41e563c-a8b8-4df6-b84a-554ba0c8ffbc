<template>
  <a-modal
    :visible="visible"
    title="选择题目"
    @ok="handleOk"
    @cancel="emitClose"
    width="1200px"
    @update:visible="emitVisible"
    destroyOnClose
    class="question-bank-modal"
  >
    <div class="selector-filters">
      <a-form layout="inline">
        <a-form-item label="课程">
          <a-select
            v-model:value="selectedCourseId"
            :options="courseOptions"
            placeholder="请选择课程"
            style="width: 200px"
            allowClear
            showSearch
            optionFilterProp="label"
          />
        </a-form-item>
        <a-form-item label="题型">
          <a-select
            v-model:value="selectedQuestionType"
            :options="questionTypeOptions"
            placeholder="请选择题型"
            style="width: 160px"
            allowClear
          />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleFilter">查询</a-button>
        </a-form-item>
      </a-form>
    </div>
    <a-table
      :rowSelection="{ type: 'checkbox', selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      :dataSource="questionBank"
      :columns="columns"
      rowKey="id"
      size="small"
      bordered
      :pagination="{ current: pagination.current, pageSize: pagination.pageSize, total: pagination.total, showSizeChanger: false }"
      :loading="loading"
      @change="handleTableChange"
      :scroll="{ y: 'calc(100vh - 400px)' }"
      class="question-table"
    />
    <a-modal
      v-model:visible="scoreModalVisible"
      title="请输入分值"
      @ok="handleScoreOk"
      @cancel="handleScoreCancel"
      :maskClosable="false"
      :closable="false"
      width="340px"
      destroyOnClose
      class="score-modal"
    >
      <div class="score-modal-content">
        <a-form layout="inline">
          <a-form-item
            label="分值"
            :validate-status="scoreError ? 'error' : ''"
            :help="scoreError ? '分值为必填项' : ''"
            required
            class="score-form-item"
          >
            <XundaInput v-model:value="score" placeholder="请输入分值" style="width: 200px; border-radius: 6px;" />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </a-modal>
</template>
<script setup lang="ts">
import { ref, onMounted, h, watch } from 'vue';
import { getList } from '@/views/exam/question';
import { getForSelect as getCourseForSelect } from '@/views/ea/course';
import { parseOptions, parseFillBlankAnswer } from '../utils';
import { XundaInput } from '@/components/Xunda/Input';

const props = defineProps<{
  visible: boolean;
  queryParams: any;
}>();
const emit = defineEmits(['select', 'update:visible']);
const questionBank = ref<any[]>([]);
const selectedRowKeys = ref<any[]>([]);
const pagination = ref({ current: 1, pageSize: 10, total: 0 });
const loading = ref(false);
const score = ref();
const scoreModalVisible = ref(false);
const scoreError = ref(false);

// 课程下拉
const courseOptions = ref<any[]>([]);
const selectedCourseId = ref<string | undefined>(props.queryParams?.courseId || undefined);
// 题型下拉
const questionTypeOptions = [
  { label: '单选题', value: 'single' },
  { label: '多选题', value: 'multi' },
  { label: '判断题', value: 'truefalse' },
  { label: '填空题', value: 'fillblank' },
  { label: '简答题', value: 'shortanswer' },
];
const selectedQuestionType = ref<string | undefined>();

function fetchCourses() {
  getCourseForSelect({ dataType: 1 }).then(res => {
    courseOptions.value = (res.data?.list || []).map(item => ({ label: item.name, value: item.id }));
  });
}

function fetchQuestions(page = 1) {
  loading.value = true;
  const params: any = {
    ...props.queryParams,
    currentPage: page,
    pageSize: pagination.value.pageSize,
  };
  if (selectedCourseId.value) params.courseId = selectedCourseId.value;
  if (selectedQuestionType.value) params.questionTypeId = selectedQuestionType.value;
  getList(params).then(res => {
    let list = [];
    let total = 0;
    if (res && res.data && Array.isArray(res.data.list)) {
      list = res.data.list;
      total = res.data.pagination?.total || list.length;
    }
    questionBank.value = list;
    pagination.value.total = total;
    loading.value = false;
    Object.assign(state.dataForm, res.data || {});
  }).catch(() => { loading.value = false; });
}

function onSelectChange(keys: any[], rows: any[]) { selectedRowKeys.value = keys; }
function handleOk() {
  // 先弹出分值填写弹窗
  scoreError.value = false;
  score.value = undefined;
  scoreModalVisible.value = true;
}
function handleScoreOk() {
  if (!score.value && score.value !== 0) {
    scoreError.value = true;
    return;
  }
  scoreError.value = false;
  scoreModalVisible.value = false;
  emit('select', { ids: selectedRowKeys.value, score: score.value });
  emit('update:visible', false);
}
function handleScoreCancel() {
  scoreModalVisible.value = false;
}
function emitClose() { emit('update:visible', false); }
function emitVisible(val) { emit('update:visible', val); }
function handleFilter() {
  fetchQuestions(1);
}

const columns = [
  { title: '题序', dataIndex: 'displayOrder', width: 60 },
  { title: '课程', dataIndex: 'courseName', width: 120 },
  { title: '题型', dataIndex: 'questionTypeName', width: 80 },
  {
    title: '题干',
    dataIndex: 'content',
    width: 200,
    customRender: ({ text }) => h('div', { innerHTML: text }),
  },
  {
    title: '选项',
    dataIndex: 'option',
    width: 200,
    customRender: ({ text }) => h('div', { style: 'white-space: pre-wrap;' }, parseOptions(text || '')),
  },
  {
    title: '答案',
    dataIndex: 'answer',
    width: 120,
    customRender: ({ text, record }) => {
      if (record.questionTypeName === '填空题') {
        return parseFillBlankAnswer(text || '');
      }
      return text;
    },
  },
  { title: '难度系数', dataIndex: 'difficulty', width: 120 },
];

onMounted(() => {
  fetchCourses();
  fetchQuestions(1);
  // 默认筛选
  if (props.queryParams?.courseId) {
    selectedCourseId.value = props.queryParams.courseId;
  }
});

function handleTableChange(paginationObj) {
  pagination.value.current = paginationObj.current;
  fetchQuestions(paginationObj.current);
}

watch(() => props.queryParams?.courseId, (val) => {
  if (val) selectedCourseId.value = val;
});
</script>
<style scoped>
.question-bank-modal .ant-modal-content {
  border-radius: 10px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.08);
}
.selector-filters {
  background: #f7f8fa;
  padding: 16px 24px 8px 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}
.question-table {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  margin-bottom: 16px;
}
.score-input-bar {
  margin-top: 24px;
  text-align: right;
  background: #f7f8fa;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
}
.score-modal .ant-modal-content {
  border-radius: 10px;
  padding: 32px 24px 24px 24px;
}
.score-modal-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px 0 8px 0;
}
.score-form-item {
  margin-bottom: 0;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.score-form-item .ant-form-item-label {
  margin-bottom: 0;
  margin-right: 8px;
}
.score-form-item .ant-form-item-control {
  width: auto;
}
</style> 