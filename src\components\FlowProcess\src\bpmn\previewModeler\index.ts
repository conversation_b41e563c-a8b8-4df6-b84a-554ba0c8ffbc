import Modeler from 'bpmn-js/lib/Modeler';
import xundaRenderer from './renderer';
import xundaElementFactory from '../factory';
import xundaOutline from '../outline';
import xundaBusinessData from '../business';
let flowInfo: any;
const modeler: any = options => [
  {
    __init__: ['bpmnRenderer', 'elementFactory', 'xundaData', 'outlineProvider'],
    bpmnRenderer: ['type', xundaRenderer, { options }], // 画布渲染
    elementFactory: ['type', xundaElementFactory], // 元素工厂
    xundaData: ['type', xundaBusinessData], // 用于放置业务数据
    outlineProvider: ['type', xundaOutline, { options }], // 元素的外边框(用于修改边框颜色，注：线条颜色有svg获取标签再去修改颜色及箭头）
  },
];

class bpmnModeler extends Modeler {
  constructor(options: any) {
    flowInfo = options.flowInfo;
    super(options);
  }
}

bpmnModeler.prototype['_modules'] = [].concat(bpmnModeler.prototype['_modules'], modeler(flowInfo));

export default bpmnModeler;
