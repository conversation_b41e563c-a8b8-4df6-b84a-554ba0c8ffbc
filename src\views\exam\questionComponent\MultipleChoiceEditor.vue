<template>
  <div>

    <!-- 题干输入 -->
    <a-col :span="24" class="ant-col-item" :hidden="false">
      <div class="form-item-wrapper">
        <div class="form-label">题干</div>
        <XundaEditor v-model:value="formData.content" :disabled="false" :allowClear="false" :height="300"
          :style="{ width: '100%' }">
        </XundaEditor>
      </div>
    </a-col>

    <!-- 选项 -->
    <a-col :span="24" class="ant-col-item" :hidden="false">
      <div class="form-item-wrapper">
        <div class="form-label">选项</div>
        <div v-for="(option, index) in localOptions" :key="option.key" class="option-item">
          <a-checkbox :checked="answerArray.includes(option.key)" @change="toggleAnswer(option.key)">
            {{ getOptionLabel(index) }}
          </a-checkbox>
          <a-input v-model:value="option.text" placeholder="请输入选项内容" style="width: 60%; margin: 0 8px;" />
          <a-button type="link" danger @click="removeOption(index)" v-if="localOptions.length > 2">
            删除
          </a-button>
        </div>
        <a-button type="dashed" @click="addOption" style="margin-top: 8px;">+ 添加选项</a-button>
      </div>
    </a-col>

  </div>
</template>

<script setup>
import { ref, reactive, defineExpose, computed, watch } from 'vue'
import { message } from 'ant-design-vue'

const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'

// 定义props，接收父组件的dataForm
const props = defineProps({
  formData: {
    type: Object,
    required: true
  }
})

// 直接使用父组件的formData
const formData = props.formData

// 本地选项数据
const localOptions = ref([
  { key: 'A', text: '' },
  { key: 'B', text: '' },
  { key: 'C', text: '' },
  { key: 'D', text: '' },
])

// 初始化选项数据
const initOptions = () => {
  // 如果数据为空或无效，重置为默认状态
  if (!formData.option || formData.option === '') {
    localOptions.value = [
      { key: 'A', text: '' },
      { key: 'B', text: '' },
      { key: 'C', text: '' },
      { key: 'D', text: '' },
    ]
    return
  }
  
  try {
    const parsedOptions = typeof formData.option === 'string' ? JSON.parse(formData.option) : formData.option
    if (Array.isArray(parsedOptions) && parsedOptions.length > 0) {
      localOptions.value = parsedOptions.map((opt, index) => ({
        key: opt.key || letters[index] || `X${index}`,
        text: opt.text || ''
      }))
    } else {
      // 如果解析结果为空数组，也重置为默认状态
      localOptions.value = [
        { key: 'A', text: '' },
        { key: 'B', text: '' },
        { key: 'C', text: '' },
        { key: 'D', text: '' },
      ]
    }
  } catch (e) {
    console.warn('Failed to parse options:', e)
    localOptions.value = [
      { key: 'A', text: '' },
      { key: 'B', text: '' },
      { key: 'C', text: '' },
      { key: 'D', text: '' },
    ]
  }
}

// 监听父组件数据变化，初始化选项
watch(() => [props.formData.option, props.formData.content, props.formData.answer], () => {
  initOptions()
}, { immediate: true, deep: true })

// 监听本地选项变化，同步到父组件
watch(localOptions, (newOptions) => {
  formData.option = JSON.stringify(newOptions)
}, { deep: true })

// 计算属性：处理答案数组
const answerArray = computed({
  get() {
    if (!formData.answer) return []
    if (typeof formData.answer === 'string') {
      return formData.answer.split(',').filter(a => a.trim())
    }
    if (Array.isArray(formData.answer)) {
      return [...formData.answer]
    }
    return []
  }
})

const addOption = () => {
  const nextKey = letters[localOptions.value.length] || `X${localOptions.value.length}`
  localOptions.value.push({ key: nextKey, text: '' })
}

const removeOption = (index) => {
  const keyToRemove = localOptions.value[index].key
  const newAnswerArray = answerArray.value.filter(k => k !== keyToRemove)
  formData.answer = newAnswerArray.join(',')
  localOptions.value.splice(index, 1)
}

const toggleAnswer = (key) => {
  const currentAnswers = [...answerArray.value]
  if (currentAnswers.includes(key)) {
    const newAnswers = currentAnswers.filter(k => k !== key)
    formData.answer = newAnswers.join(',')
  } else {
    currentAnswers.push(key)
    formData.answer = currentAnswers.join(',')
  }
}

const getOptionLabel = (index) => letters[index] || `X${index}`

const getData = () => {
  return {
    content: formData.content,
    option: formData.option,
    answer: formData.answer,
  }
}

const validate = () => {
  if (!formData.content || !formData.content.trim()) {
    message.error('题干不能为空')
    return false
  }
  
  if (localOptions.value.length < 2) {
    message.error('至少需要两个选项')
    return false
  }
  
  const emptyOption = localOptions.value.find(o => !o.text.trim())
  if (emptyOption) {
    message.error('选项内容不能为空')
    return false
  }
  
  if (answerArray.value.length === 0) {
    message.error('请至少选择一个正确答案')
    return false
  }
  
  return true
}

defineExpose({ validate, getData })
</script>

<style scoped>
.option-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.form-item-wrapper {
  margin-bottom: 16px;
}

.form-label {
  font-weight: 500;
  margin-bottom: 8px;
  color: rgba(0, 0, 0, 0.85);
}
</style>