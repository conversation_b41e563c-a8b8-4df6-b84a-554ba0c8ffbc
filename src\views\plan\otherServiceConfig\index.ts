import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';
import { getForSelect as getPlanNameForSelect } from '../semester';
import { ref } from 'vue';

// 基础Api
export const planOtherServiceConfigApi = '/api/plan/otherServiceConfig';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: planOtherServiceConfigApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: planOtherServiceConfigApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: planOtherServiceConfigApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: planOtherServiceConfigApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: planOtherServiceConfigApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: planOtherServiceConfigApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: planOtherServiceConfigApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: planOtherServiceConfigApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: planOtherServiceConfigApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: planOtherServiceConfigApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: planOtherServiceConfigApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: planOtherServiceConfigApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: planOtherServiceConfigApi + `/getForSelect`, data });
}

const planOptionsObj = ref([]);

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'name',
    label: t('名称'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
  {
    field: 'semesterName',
    label: t('计划名称'),
    component: 'Select',
    componentProps: {
      submitOnPressEnter: true,
      showSearch: true,
      options: planOptionsObj.value,
      fieldNames: {
        label: 'name',
        value: 'id',
      },
    },
  },
];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('计划名称'),
    dataIndex: 'semesterName',
    width: 120,
  },
  {
    title: t('名称'),
    dataIndex: 'name',
    sorter: true,
    width: 120,
  },
  {
    title: t('人数限制'),
    dataIndex: 'limitCount',
    sorter: true,
    width: 120,
  },
  {
    title: t('已选数'),
    dataIndex: 'selectedCount',
    sorter: true,
    width: 120,
  },
  {
    title: t('剩余数'),
    dataIndex: 'remain',
    sorter: true,
    width: 120,
  },
  {
    title: t('需求模板'),
    dataIndex: 'requirementTemplate',
    width: 120,
  },
  {
    title: t('允许填写需求'),
    dataIndex: 'allowRequirement',
    width: 120,
    customRender: xundaUtils.customRenderBoolean,
  },
  {
    title: t('说明'),
    dataIndex: 'description',
    width: 120,
  },
  {
    title: t('图片'),
    dataIndex: 'picture',
    width: 120,
  },
];

export async function getPlanName(updateSchema) {
  getPlanNameForSelect({
    dataType: 1,
  }).then(res => {
    planOptionsObj.value = res.data.list;
    updateSchema({
      field: 'semesterName',
      label: t('计划名称'),
      component: 'Select',
      componentProps: {
        submitOnPressEnter: true,
        showSearch: true,
        options: planOptionsObj.value,
        fieldNames: {
          label: 'name',
          value: 'id',
        },
      },
    });
  });
}
