<template>
  <div class="form-table">
    <div class="form-header">
      <h2 class="form-title">贵州省职业技能等级认定个人申请表</h2>
    </div>
    <table class="info-table">
      <tbody>
        <tr>
          <td width="15%" class="label-field">申报人姓名</td>
          <td width="25%"
            ><span class="form-data-field">{{ formData.name || 'XXX' }}</span></td
          >
          <td width="15%" class="label-field">性别</td>
          <td width="25%"
            ><span class="form-data-field">{{ formData.sex || 'X' }}</span></td
          >
          <td rowspan="3" width="220px" class="photo-cell">
            <a-image
              v-if="formData.userImg"
              :src="apiUrl + formData.userImg.url"
              :preview="{
                src: apiUrl + formData.userImg.url,
                mask: '点击预览',
              }" />
            <div v-else class="empty-placeholder">
              <p>暂无盖底1寸证件照</p>
            </div>
          </td>
        </tr>
        <tr>
          <td class="label-field">出生日期</td>
          <td
            ><span class="form-data-field">{{ formData.birthday || 'XXXX.XX' }}</span></td
          >
          <td class="label-field">学历及专业名称</td>
          <td
            ><span class="form-data-field">{{ formData.major || 'XX     XXXXXXX' }}</span></td
          >
        </tr>
        <tr>
          <td class="label-field">证件类型</td>
          <td>
            <div class="checkbox-group">
              <span class="checkbox">☒ 居民身份证</span>
              <span class="checkbox">☐ 护照</span>
            </div>
          </td>
          <td class="label-field">证件号码</td>
          <td
            ><span class="form-data-field">{{ formData.idNo || 'XXXXX' }}</span></td
          >
        </tr>
        <tr>
          <td class="label-field">手机号码</td>
          <td
            ><span class="form-data-field">{{ formData.phone || 'XXXXXX' }}</span></td
          >
          <td class="label-field">政治面貌</td>
          <td colspan="2"
            ><span class="form-data-field">{{ formData.politicalStatus || 'XXX' }}</span></td
          >
        </tr>
        <tr>
          <td class="label-field">户籍地址</td>
          <td colspan="2"
            ><span class="form-data-field">{{ formData.permanentAddress || 'XXX' }}</span></td
          >
          <td class="label-field">常住地址</td>
          <td>
            <span class="form-data-field">{{ formData.residentialAddress || 'XXX' }}</span></td
          >
        </tr>
        <tr>
          <td class="label-field">现持有证书信息</td>
          <td colspan="4">
            <div class="cert-section">
              <!-- 职业资格证书 -->
              <div class="cert-item" v-for="(cert, index) in certificates.filter(c => c.type === '职业资格证书')" :key="cert.id">
                <div>☑ 职业资格证书 ☐ 职业资格证书</div>
                <div class="cert-detail">
                  <div>职业（工种）：{{ cert.name || '_______________' }} 等级：{{ cert.level || '_______________' }}</div>
                  <div>证书编号：{{ cert.no || '_______________' }} 发证日期：{{ cert.date ? formatDate(cert.date) : '___ 年 ___ 月 ___ 日' }}</div>
                  <div>发证机构：{{ cert.organization || '_______________' }}</div>
                </div>
              </div>

              <!-- 技能等级证书 -->
              <div class="cert-item" v-for="(cert, index) in certificates.filter(c => c.type === '技能等级证书')" :key="cert.id">
                <div>☐ 职业资格证书 ☑ 技能等级证书</div>
                <div class="cert-detail">
                  <div>职业（工种）：{{ cert.name || '_______________' }} 等级：{{ cert.level || '_______________' }}</div>
                  <div>证书编号：{{ cert.no || '_______________' }} 发证日期：{{ cert.date ? formatDate(cert.date) : '___ 年 ___ 月 ___ 日' }}</div>
                  <div>发证机构：{{ cert.organization || '_______________' }}</div>
                </div>
              </div>

              <div class="cert-item" v-if="!certificates.some(c => c.type === '职业资格证书') && !certificates.some(c => c.type === '技能等级证书')">
                <div>☐ 职业资格证书 ☐ 职业资格证书</div>
                <div class="cert-detail">
                  <div>职业（工种）：_______________ 等级：_______________</div>
                  <div>证书编号：_______________ 发证日期：___ 年 ___ 月 ___ 日</div>
                  <div>发证机构：_______________</div>
                </div>
              </div>

              <!-- 专业技术职称证书 -->
              <div class="cert-item" v-for="(cert, index) in certificates.filter(c => c.type === '专业技术职称证书')" :key="cert.id">
                <div>☑ 专业技术职称证书</div>
                <div class="cert-detail">
                  <div>职称证书名称：{{ cert.name || '_______________' }} 等级：{{ cert.level || '_______________' }}</div>
                  <div>证书编号：{{ cert.no || '_______________' }} 发证日期：{{ cert.date ? formatDate(cert.date) : '___ 年 ___ 月 ___ 日' }}</div>
                  <div>发证机构：{{ cert.organization || '_______________' }}</div>
                </div>
              </div>
              <div class="cert-item" v-if="!certificates.some(c => c.type === '专业技术职称证书')">
                <div>☐ 专业技术职称证书</div>
                <div class="cert-detail">
                  <div>职称证书名称：_______________ 等级：_______________</div>
                  <div>证书编号：_______________ 发证日期：___ 年 ___ 月 ___ 日</div>
                  <div>发证机构：_______________</div>
                </div>
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td class="label-field">申报信息</td>
          <td colspan="4">
            申报职业（工种）：<span class="form-data-field">{{ formData.workType || 'XX' }}</span> 申报等级：<span class="form-data-field">{{
              formData.determinedLevel || 'X'
            }}</span>
          </td>
        </tr>
      </tbody>
    </table>

    <div class="work-commitment">
      <div class="section-title">工作年限与个人承诺</div>
      <div class="work-exp">
        一、工作年限：<br />
        <p class="indent"
          >本人从事
          <span
            ><span class="form-data-field">{{ formData.post || 'XX' }}</span></span
          >
          岗位已累计工作
          <span>
            <span class="form-data-field">{{ formData.duration || 'X' }}</span>
          </span>
          年，现根据《国家职业技能标准》（评价规范）申报
          <span>
            <span class="form-data-field">{{ formData.workType || 'XX' }}</span>
          </span>
          （职业工种）
          <span>
            <span class="form-data-field">{{ formData.determinedLevel || 'X' }}</span>
          </span>
          等级认定。</p
        >
      </div>

      <div class="commitment">
        二、个人承诺<br />
        <p class="indent">我申报参加技能等级认定，为了维护认定的严肃性、权威性和公平性，现郑重承诺如下：</p>
        <ol>
          <li>1.本人已仔细阅读并理解《国家职业技能标准》（评价规范）等相关政策及报名须知内容，完全了解并符合所报考职业等级的条件要求。</li>
          <li>2.本人报名填写提交的身份证件、学历、现持有证书、专业工作年限、业绩材料等信息及认定期间提供的证件资料准确、真实、有效，不存在虚假。</li>
          <li>3.本人知道应在规定时间内完成报名、交费和打印准考证事宜，并清楚应按时参加认定，逾期本人将自动放弃认定。</li>
          <li>
            4.本人认真履行报考人员的各项义务，遵守认定纪律和考场规则，遵从认定组织部门的安排，服从监考人员的检查、监督和管理，维护评价机构和他人的合法权益，不做扰乱报名和认定秩序的行为，不参与任何形式的认定舞弊。
          </li>
          <li>5.如有违纪违规及违反上述承诺的行为，本人自愿接受有关法律法规处罚，并承担相应的责任和由此造成的一切后果。</li>
        </ol>
      </div>

      <div class="signature-section">
        <div class="signature-line">
          <span>申报人签字：</span>
          <span class="signature">手签盖手印</span>
          <span class="date date-right">___ 年 ___ 月 ___ 日</span>
        </div>
      </div>

      <div class="review-section">
        <table class="review-table">
          <tbody>
            <tr>
              <td width="50%" class="review-title">评价机构审核人意见</td>
              <td width="50%" class="review-content">
                <div class="checkbox-group">
                  <span class="checkbox">☐ 通过</span>
                  <span class="checkbox">☐ 不通过</span>
                </div>
                <div class="signature-line">
                  <span>（签字）：</span>
                  <span class="date date-right">___ 年 ___ 月 ___ 日</span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue';

  const props = defineProps({
    formData: {
      type: Object,
      default: () => ({}),
    },
    apiUrl: {
      type: String,
      default: '',
    },
  });

  const certificates = computed(() => {
    var holdCertificates = [];
    if (props.formData.holdCertificates && Array.isArray(props.formData.holdCertificates)) {
      holdCertificates = props.formData.holdCertificates;
    } else {
      // 示例数据
      holdCertificates = [];
    }
    return holdCertificates;
  });

  const emit = defineEmits(['download']);

  // 格式化日期的函数
  const formatDate = dateString => {
    if (!dateString) return '___ 年 ___ 月 ___ 日';
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${year} 年 ${month} 月 ${day} 日`;
  };
</script>

<style lang="less" scoped>
  .form-table {
    width: 100%;
    font-size: 14px;

    .form-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 24px;
      position: relative;

      .download-link {
        position: absolute;
        right: 0;
        top: 0;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 8px;

        a {
          color: #1890ff;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }
      }

      .form-title {
        font-size: 20px;
        font-weight: bold;
        margin-top: 16px;
        margin-bottom: 16px;
      }
    }

    .info-table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 24px;

      td {
        border: 1px solid #000;
        padding: 8px;
        font-size: 14px;
      }

      .label-field {
        font-weight: bold;
        text-align: center;
        color: #333;
      }

      .checkbox-group {
        display: flex;
        gap: 16px;

        .checkbox {
          font-size: 14px;
        }
      }

      .photo-cell {
        height: 160px;
        text-align: center;
        vertical-align: middle;
      }

      .empty-placeholder {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px dashed #d9d9d9;
        border-radius: 4px;
        color: #999;
      }

      .cert-section {
        .cert-item {
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          .cert-detail {
            margin-left: 24px;
            margin-top: 4px;
            font-size: 14px;
          }
        }
      }
    }

    .work-commitment {
      border: 1px solid #000;
      border-top: none;
      padding: 16px;

      .section-title {
        font-weight: bold;
        margin-bottom: 16px;
        font-size: 14px;
      }

      .work-exp,
      .commitment {
        margin-bottom: 24px;
      }

      .indent {
        margin-left: 24px;
        margin-top: 8px;
        line-height: 1.6;
        font-size: 14px;
      }

      ol {
        margin-left: 48px;

        li {
          margin-bottom: 12px;
          line-height: 1.6;
          font-size: 14px;
        }
      }

      .form-data-field {
        font-weight: bold;
        border-bottom: 1px solid;
      }

      .signature-section {
        margin: 24px 0;

        .signature-line {
          display: flex;
          align-items: center;
          gap: 16px;
          position: relative;
          font-size: 14px;

          .signature {
            color: #f00;
            font-weight: bold;
            font-size: 14px;
          }

          .date-right {
            margin-left: auto;
            font-size: 14px;
          }
        }
      }

      .review-section {
        .review-table {
          width: 100%;
          border-collapse: collapse;
          border: 1px solid #000;

          td {
            border: 1px solid #000;
            padding: 16px;
            font-size: 14px;
          }

          .label-field {
            font-weight: bold;
            background-color: #f0f8ff;
            color: #333;
            border-left: 3px solid #1890ff;
          }

          .review-title {
            font-weight: bold;
            font-size: 14px;
          }

          .review-content {
            .checkbox-group {
              margin-bottom: 16px;
            }

            .signature-line {
              display: flex;
              align-items: center;
              gap: 16px;

              .date-right {
                margin-left: auto;
              }
            }
          }
        }
      }
    }

    @media print {
      /* 打印样式保持不变 */
      /* 隐藏不需要打印的元素 */
      body * {
        visibility: hidden;
      }

      /* 只显示表单内容 */
      .form-table,
      .form-table * {
        visibility: visible;
      }

      .form-table {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        padding: 20px;
        background: white;
      }

      /* 隐藏下载链接 */
      .download-link {
        display: none;
      }

      /* 确保表格边框显示 */
      .info-table {
        border-collapse: collapse;

        td {
          border: 1px solid #000;
          padding: 8px;
        }
      }

      /* 确保签名和审核部分不会被分页 */
      .signature-section,
      .review-section {
        page-break-inside: avoid;
      }
    }
  }
</style>
