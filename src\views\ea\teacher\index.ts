import { FormSchema } from "@/components/Form";
import { useI18n } from "@/hooks/web/useI18n";
import { BasicColumn } from "@/components/Table/src/types/table";
import { getDictionaryDataSelector } from "@/api/systemData/dictionary";
import { getDictionaryFullName, toDateString, toFixedPercent } from "@/utils/myUtil";
import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();

// 基础Api
export const teacherApi = '/api/ea/teacher';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: teacherApi + `/getList`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: teacherApi + `/getForSelect`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: teacherApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: teacherApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: teacherApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: teacherApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: teacherApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: teacherApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: teacherApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: teacherApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: teacherApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: teacherApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: teacherApi + `/exportExceptionData`, data });
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'name',    // 名称
    label: t('ea.teacher.name'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
  {
    field: 'no',    // 工号
    label: t('ea.teacher.no'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
];


/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    dataIndex: 'name',    // 名称
    title: t('ea.teacher.name'),
    width: 120,
  },
  {
    dataIndex: 'nickname',    // 昵称
    title: t('ea.teacher.nickname'),
    width: 120,
  },
  {
    dataIndex: 'no',    // 工号
    title: t('ea.teacher.no'),
    width: 120,
  },
  {
    dataIndex: 'graduatedAt',    // 毕业学校
    title: t('ea.teacher.graduatedAt'),
    width: 120,
  },
  {
    dataIndex: 'idCardType',    // 证件类型
    title: t('ea.teacher.idCardType'),
    width: 120,
  },
  {
    dataIndex: 'idCardNo',    // 证件号
    title: t('ea.teacher.idCardNo'),
    width: 120,
  },
  {
    dataIndex: 'workAt',    // 就职单位
    title: t('ea.teacher.workAt'),
    width: 120,
  },
  {
    dataIndex: 'highestEducation',    // 最高学历
    title: t('ea.teacher.highestEducation'),
    width: 120,
  },
  {
    dataIndex: 'profession',    // 专业
    title: t('ea.teacher.profession'),
    width: 120,
  },
  {
    dataIndex: 'jobTitle',    // 职称
    title: t('ea.teacher.jobTitle'),
    width: 120,
  },
  {
    dataIndex: 'note',    // 备注
    title: t('ea.teacher.note'),
    width: 120,
  },
  {
    dataIndex: 'extraInfo',    // 扩展信息
    title: t('ea.teacher.extraInfo'),
    width: 120,
  },
];