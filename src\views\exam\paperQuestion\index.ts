import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';

// 基础Api
export const paperQuestionApi = '/api/exam/paperQuestion';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: paperQuestionApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: paperQuestionApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: paperQuestionApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: paperQuestionApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: paperQuestionApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: paperQuestionApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: paperQuestionApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: paperQuestionApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: paperQuestionApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: paperQuestionApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: paperQuestionApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: paperQuestionApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: paperQuestionApi + `/getForSelect`, data });
}

export function batchAddFromQuestions(data: { paperId: string; questionIds: string[]; score?: number }) {
  return defHttp.post({ url: paperQuestionApi + '/BatchAddFromQuestions', data });
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'no',
    label: t('序号'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('主键'),
    dataIndex: 'id',
    width: 120,
  },
  {
    title: t('父题目'),
    dataIndex: 'parentId',
    width: 120,
  },
  {
    title: t('试卷'),
    dataIndex: 'paperId',
    width: 120,
  },
  {
    title: t('题库题目标识'),
    dataIndex: 'questionId',
    width: 120,
  },
  {
    title: t('题型'),
    dataIndex: 'questionTypeId',
    width: 120,
  },
  {
    title: t('序号'),
    dataIndex: 'no',
    width: 120,
  },
  {
    title: t('题序'),
    dataIndex: 'displayOrder',
    width: 120,
  },
  {
    title: t('题干'),
    dataIndex: 'content',
    width: 120,
  },
  {
    title: t('选项'),
    dataIndex: 'option',
    width: 120,
  },
  {
    title: t('答案'),
    dataIndex: 'answer',
    width: 120,
  },
  {
    title: t('分值'),
    dataIndex: 'score',
    width: 120,
  },
  {
    title: t('解析'),
    dataIndex: 'analysis',
    width: 120,
  },
  {
    title: t('难度系数'),
    dataIndex: 'difficulty',
    width: 120,
  },
  {
    title: t('知识点'),
    dataIndex: 'knowledgeTag',
    width: 120,
  },
];
