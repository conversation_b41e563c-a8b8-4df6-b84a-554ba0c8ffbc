import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { defHttp } from '@/utils/http/axios';
import { xundaUtils } from '@/utils/xunda';

const { t } = useI18n();

// 基础Api
export const courseTeacherApi = '/api/ea/courseTeacher';

/**
 * 获取列表
 * @param data
 * @returns
 */
export function getUserList(data) {
  return defHttp.post({ url: courseTeacherApi + `/getList`, data });
}

export function batchDelete(data) {
  return defHttp.delete({ url: courseTeacherApi + `/batchRemove`, data });
}

/**
 * 搜索表单配置
 */
export const userSearchSchemas: FormSchema[] = [
  {
    field: 'name',
    label: t('姓名'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
];

/**
 * 表格列配置
 */
export const userColumns: BasicColumn[] = [
  {
    title: t('姓名'),
    dataIndex: 'teacherName',
    width: 120,
  },
  {
    title: t('角色'),
    dataIndex: 'role',
    width: 120,
  },
  {
    title: t('是否主要负责'),
    dataIndex: 'primaryFlag',
    width: 120,
    customRender: xundaUtils.customRenderBoolean,
  },
];
