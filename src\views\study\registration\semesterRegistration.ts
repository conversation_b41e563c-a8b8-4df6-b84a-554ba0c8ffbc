import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';

// 基础Api
export const registrationApi = '/api/study/registration';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: registrationApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: registrationApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: registrationApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: registrationApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: registrationApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: registrationApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: registrationApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: registrationApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: registrationApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: registrationApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: registrationApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: registrationApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: registrationApi + `/getForSelect`, data });
}

// 审核
export function auditStudent(data) {
  return defHttp.post({ url: registrationApi + `/auditStudent`, data });
}

// 审核
export function createRMessage(data) {
  return defHttp.post({ url: registrationApi + `/createMessage`, data });
}

// 审核
export function sendMessage(data) {
  return defHttp.post({ url: registrationApi + `/sendMessage`, data });
}

// 审核
export function getSemesterTypeAsync(id) {
  return defHttp.post({ url: registrationApi + `/getSemesterType/` + id });
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'auditFlag',
    label: t('审核状态'),
    component: 'Select',
    componentProps: {
      submitOnPressEnter: true,
      options: [
        {
          fullName: t('未审核'),
          id: false,
        },
        {
          fullName: t('已审核'),
          id: true,
        },
      ],
    },

    colProps: {
      span: 6,
    },
  },
  {
    field: 'name',
    label: t('姓名'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
  {
    field: 'sex',
    label: t('性别'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },

  {
    field: 'nationality',
    label: t('民族'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
  {
    field: 'name',
    label: t('是否参加培训'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },

  {
    field: 'name',
    label: t('身份'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('计划'),
    dataIndex: 'semesterName',
    width: 120,
    fixed: 'left',
  },
  {
    title: t('姓名'),
    dataIndex: 'name',
    width: 120,
    fixed: 'left',
  },
  // {
  //   title: t('英文名'),
  //   dataIndex: 'englishName',
  //   width: 120,
  // },
  // {
  //   title: t('学员类型'),
  //   dataIndex: 'studentType',
  //   width: 120,
  // },
  // {
  //   title: t('性别'),
  //   dataIndex: 'sex',
  //   width: 120,
  // },
  // {
  //   title: t('生日'),
  //   dataIndex: 'birthday',
  //   width: 120,
  //   customRender: ({ record }) => {
  //     return xundaUtils.toDateString(record.birthday);
  //   },
  // },
  // {
  //   title: t('国籍'),
  //   dataIndex: 'nationality',
  //   width: 120,
  // },
  // {
  //   title: t('证件类型'),
  //   dataIndex: 'idCardType',
  //   width: 120,
  // },
  // {
  //   title: t('证件号'),
  //   dataIndex: 'idNo',
  //   width: 200,
  // },
  // {
  //   title: t('护照号'),
  //   dataIndex: 'passportNo',
  //   width: 120,
  // },
  // {
  //   title: t('邮箱'),
  //   dataIndex: 'email',
  //   width: 120,
  // },
  // {
  //   title: t('电话'),
  //   dataIndex: 'phone1',
  //   width: 120,
  // },
  // {
  //   title: t('备用电话'),
  //   dataIndex: 'phone2',
  //   width: 120,
  // },
  // {
  //   title: t('紧急联系电话'),
  //   dataIndex: 'emergencyPhone',
  //   width: 120,
  // },
  // {
  //   title: t('就读/毕业学校'),
  //   dataIndex: 'school',
  //   width: 120,
  // },
  // {
  //   title: t('专业'),
  //   dataIndex: 'major',
  //   width: 120,
  // },
  // {
  //   title: t('备注'),
  //   dataIndex: 'note',
  //   width: 120,
  // },
  // {
  //   title: t('照片'),
  //   dataIndex: 'photo',
  //   width: 120,
  // },
  // {
  //   title: t('总费用'),
  //   dataIndex: 'totalFee',
  //   width: 120,
  // },
  // {
  //   title: t('政策优惠'),
  //   dataIndex: 'policyDedude',
  //   width: 120,
  // },
  // {
  //   title: t('政策优惠明细'),
  //   dataIndex: 'policyDeduceDetial',
  //   width: 120,
  // },
  // {
  //   title: t('应缴'),
  //   dataIndex: 'shouldPay',
  //   width: 120,
  // },
  // {
  //   title: t('实缴'),
  //   dataIndex: 'payIn',
  //   width: 120,
  // },
  // {
  //   title: t('退费'),
  //   dataIndex: 'payRetrun',
  //   width: 120,
  // },
  // {
  //   title: t('费用差额'),
  //   dataIndex: 'payDiff',
  //   width: 120,
  // },
  {
    title: t('支付状态'),
    dataIndex: 'payState',
    width: 120,
  },
  {
    title: t('学生状态'),
    dataIndex: 'state',
    width: 120,
    fixed: 'right',
  },
];

export const semesterApi = '/api/plan/semester';
// 获取列表
export function getSemesterList(data) {
  return defHttp.post({ url: semesterApi + `/getList`, data });
}
/**
 * 表格列配置
 */
export const semesterColumns: BasicColumn[] = [
  {
    title: t('计划名称'),
    dataIndex: 'name',
    width: 120,
  },
];

/**
 * 搜索表单配置
 */
export const semesterSearchSchemas: FormSchema[] = [
  {
    field: 'name',
    label: t('姓名'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
];
