<template>
  <a-form
    :colon="false"
    size="middle"
    layout="horizontal"
    labelAlign="left"
    :labelCol="{ style: { width: '100px' } }"
    :model="modelValue"
    :rules="rules"
    ref="formRef">
    <a-row :gutter="15">
      <a-col v-if="false" :span="24" class="ant-col-item">
        <a-form-item name="paperId">
          <template #label>试卷</template>
          <XundaInput v-model:value="modelValue.paperId" placeholder="请输入试卷" :allowClear="true" :style="{ width: '100%' }" />
        </a-form-item>
      </a-col>
      <a-col v-if="showCourseId" :span="24" class="ant-col-item">
        <a-form-item name="courseId">
          <template #label>课程</template>
          <XundaSelect v-model:value="modelValue.courseId" placeholder="请选择课程" :allowClear="true" :style="{ width: '100%' }" :options="courseOptions" />
        </a-form-item>
      </a-col>
      <a-col v-if="false" :span="24" class="ant-col-item">
        <a-form-item name="questionId">
          <template #label>题库题目标识</template>
          <XundaInput v-model:value="modelValue.questionId" placeholder="题库题目标识" :allowClear="true" :style="{ width: '100%' }" />
        </a-form-item>
      </a-col>
      <a-col :span="24" class="ant-col-item">
        <a-space style="margin-bottom: 16px;">
          <a-button v-for="item in questionTypes" :key="item.type" :type="activeType === item.type ? 'primary' : 'default'" @click="handleTypeChange(item.type)">
            {{ item.label }}
          </a-button>
        </a-space>
      </a-col>
      <component :is="activeComponent" ref="currentEditor" :formData="modelValue" />
      <a-divider />
      <a-col v-if="showScore" :span="24" class="ant-col-item">
        <a-form-item name="score">
          <template #label>分值</template>
          <XundaInput v-model:value="modelValue.score" placeholder="请输入分值" :allowClear="true" :style="{ width: '100%' }" />
        </a-form-item>
      </a-col>
      <a-col :span="24" class="ant-col-item">
        <a-form-item name="analysis">
          <template #label>解析</template>
          <XundaInput v-model:value="modelValue.analysis" placeholder="请输入解析" :allowClear="true" :style="{ width: '100%' }" />
        </a-form-item>
      </a-col>
      <a-col :span="24" class="ant-col-item">
        <a-form-item name="difficulty">
          <template #label>难度系数</template>
          <XundaRate v-model:value="modelValue.difficulty" placeholder="难度系数" :allowClear="true" :style="{ width: '100%' }" :count="5" allow-half />
        </a-form-item>
      </a-col>
      <a-col :span="24" class="ant-col-item">
        <a-form-item name="knowledgeTag">
          <template #label>知识点</template>
          <XundaInput v-model:value="modelValue.knowledgeTag" placeholder="请输入知识点" :allowClear="true" :style="{ width: '100%' }" />
        </a-form-item>
      </a-col>
    </a-row>
  </a-form>
</template>
<script lang="ts" setup>
import { ref, computed, watch, defineProps, defineEmits } from 'vue';
import SingleChoiceEditor from './questionComponent/SingleChoiceEditor.vue';
import MultipleChoiceEditor from './questionComponent/MultipleChoiceEditor.vue';
import TrueFalseEditor from './questionComponent/TrueFalseEditor.vue';
import FillBlankEditor from './questionComponent/FillBlankEditor.vue';
import ShortAnswerEditor from './questionComponent/ShortAnswerEditor.vue';

const props = defineProps({
  modelValue: { type: Object, required: true },
  rules: { type: Object, default: () => ({}) },
  showPaperId: { type: Boolean, default: false },
  showScore: { type: Boolean, default: false },
  showCourseId: { type: Boolean, default: false },
  showQuestionId: { type: Boolean, default: true },
  courseOptions: { type: Array, default: () => [] },
});
const emit = defineEmits(['update:modelValue', 'typeChange']);

const questionTypes = [
  { type: 'single', label: '单选题', component: SingleChoiceEditor },
  { type: 'multi', label: '多选题', component: MultipleChoiceEditor },
  { type: 'truefalse', label: '判断题', component: TrueFalseEditor },
  { type: 'fillblank', label: '填空', component: FillBlankEditor },
  { type: 'shortanswer', label: '简答', component: ShortAnswerEditor },
];
const activeType = ref(props.modelValue.questionTypeId || 'single');
const currentEditor = ref();
const activeComponent = computed(() => {
  const found = questionTypes.find(q => q.type === activeType.value);
  console.log('QuestionFormBase activeComponent computed - activeType:', activeType.value, 'found:', found);
  return found ? found.component : null;
});
function handleTypeChange(type) {
  activeType.value = type;
  emit('typeChange', type);
  props.modelValue.questionTypeId = type;
}
watch(() => props.modelValue.questionTypeId, (val) => {
  console.log('QuestionFormBase watch questionTypeId:', val);
  if (val) activeType.value = val;
}, { immediate: true });

// 添加对整个modelValue对象的监听，确保对象重新创建时能正确更新activeType
watch(() => props.modelValue, (newValue) => {
  console.log('QuestionFormBase watch modelValue:', newValue);
  if (newValue && newValue.questionTypeId) {
    console.log('QuestionFormBase setting activeType to:', newValue.questionTypeId);
    activeType.value = newValue.questionTypeId;
  }
}, { immediate: true, deep: true });

const formRef = ref();

defineExpose({
  resetFields: () => formRef.value?.resetFields(),
  validate: (...args) => formRef.value?.validate(...args),
  currentEditor: currentEditor,
});
</script> 