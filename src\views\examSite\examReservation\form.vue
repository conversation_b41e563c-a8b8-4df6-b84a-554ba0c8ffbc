<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    width="800px"
    :minHeight="100"
    :cancelText="t('common.cancelText', '取消')"
    :okText="t('common.okText', '确定')"
    @ok="handleSubmit"
    :closeFunc="onClose">
    <template #title>
      <a-space :size="10">
        <div class="text-16px font-medium">{{ title }}</div>
        <a-space-compact size="small" block v-if="dataForm.id">
          <a-tooltip :title="t('common.prevRecord')">
            <a-button size="small" :disabled="getPrevDisabled" @click="handlePrev">
              <i class="icon-ym icon-ym-caret-left text-10px"></i>
            </a-button>
          </a-tooltip>
          <a-tooltip :title="t('common.nextRecord')">
            <a-button size="small" :disabled="getNextDisabled" @click="handleNext">
              <i class="icon-ym icon-ym-caret-right text-10px"></i>
            </a-button>
          </a-tooltip>
        </a-space-compact>
      </a-space>
    </template>
    <template #insertFooter>
      <div class="loat-left mt-5px">
        <XundaCheckboxSingle v-model:value="submitType" :label="continueText" />
      </div>
    </template>

    <!-- 表单开始 -->
    <a-row class="dynamic-form">
      <a-form
        :colon="false"
        size="middle"
        layout="horizontal"
        labelAlign="left"
        :labelCol="{ style: { width: '100px' } }"
        :model="dataForm"
        :rules="dataRule"
        ref="formRef">
        <a-row :gutter="15">
          <!-- 单位ID -->
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="unitId">
              <template #label>单位ID </template>
              <XundaInput v-model:value="dataForm.unitId" placeholder="请输入单位ID" :allowClear="true" :style="{ width: '100%' }"> </XundaInput>
            </a-form-item>
          </a-col>

          <!-- 单位名称 -->
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="unitName">
              <template #label>单位名称 </template>
              <XundaInput v-model:value="dataForm.unitName" placeholder="请输入单位名称" :allowClear="true" :style="{ width: '100%' }"> </XundaInput>
            </a-form-item>
          </a-col>

          <!-- 联系人 -->
          <a-col :span="12" class="ant-col-item">
            <a-form-item name="contactPerson">
              <template #label>联系人 </template>
              <XundaInput v-model:value="dataForm.contactPerson" placeholder="请输入联系人" :allowClear="true" :style="{ width: '100%' }"> </XundaInput>
            </a-form-item>
          </a-col>

          <!-- 联系电话 -->
          <a-col :span="12" class="ant-col-item">
            <a-form-item name="contactPhone">
              <template #label>联系电话 </template>
              <XundaInput v-model:value="dataForm.contactPhone" placeholder="请输入联系电话" :allowClear="true" :style="{ width: '100%' }"> </XundaInput>
            </a-form-item>
          </a-col>

          <!-- 考试方式 -->
          <a-col :span="12" class="ant-col-item">
            <a-form-item name="examMode">
              <template #label>考试方式 </template>
              <XundaSelect
                v-model:value="dataForm.examMode"
                :options="optionsObj.examModeOptions"
                placeholder="请选择" :allowClear="true"
                :style="{ width: '100%' }">
              </XundaSelect>
            </a-form-item>
          </a-col>

          <!-- 考点ID -->
          <a-col :span="12" class="ant-col-item">
            <a-form-item name="placeId">
              <template #label>考点ID </template>
              <XundaInput v-model:value="dataForm.placeId" placeholder="请输入考点ID" :allowClear="true" :style="{ width: '100%' }"> </XundaInput>
            </a-form-item>
          </a-col>

          <!-- 考点名称 -->
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="placeName">
              <template #label>考点名称 </template>
              <XundaInput v-model:value="dataForm.placeName" placeholder="请输入考点名称" :allowClear="true" :style="{ width: '100%' }"> </XundaInput>
            </a-form-item>
          </a-col>

          <!-- 考试日期 -->
          <a-col :span="12" class="ant-col-item">
            <a-form-item name="examDate">
              <template #label>考试日期 </template>
              <XundaDatePicker v-model:value="dataForm.examDate" placeholder="请选择" :style="{ width: '100%' }"> </XundaDatePicker>
            </a-form-item>
          </a-col>

          <!-- 开始时间 -->
          <a-col :span="12" class="ant-col-item">
            <a-form-item name="startTime">
              <template #label>开始时间 </template>
              <XundaTimePicker v-model:value="dataForm.startTime" placeholder="请选择" :style="{ width: '100%' }"> </XundaTimePicker>
            </a-form-item>
          </a-col>

          <!-- 持续时长 -->
          <a-col :span="12" class="ant-col-item">
            <a-form-item name="durationHours">
              <template #label>持续时长(小时) </template>
              <XundaInput v-model:value="dataForm.durationHours" type="number" placeholder="请输入" :allowClear="true" :style="{ width: '100%' }"> </XundaInput>
            </a-form-item>
          </a-col>

          <!-- 预计人数 -->
          <a-col :span="12" class="ant-col-item">
            <a-form-item name="estimatedCount">
              <template #label>预计人数 </template>
              <XundaInput v-model:value="dataForm.estimatedCount" type="number" placeholder="请输入" :allowClear="true" :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>

          <!-- 备注 -->
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="note">
              <template #label>备注 </template>
              <XundaTextarea
                v-model:value="dataForm.note"
                placeholder="请输入备注"
                :allowClear="true"
                :style="{ width: '100%' }"
                :autoSize="{ minRows: 4, maxRows: 4 }"
                :showCount="true">
              </XundaTextarea>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { create, update, getInfo } from './index';
  import { reactive, toRefs, nextTick, ref, unref, computed, inject } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useUserStore } from '@/store/modules/user';
  import type { FormInstance } from 'ant-design-vue';
  // 表单权限
  import { usePermission } from '@/hooks/web/usePermission';
  import { XundaSelect } from '@/components/Xunda';
  import { XundaDatePicker } from '@/components/Xunda';
  import { XundaTimePicker } from '@/components/Xunda';

  interface State {
    dataForm: any;
    tableRows: any;
    dataRule: any;
    optionsObj: any;
    childIndex: any;
    isEdit: any;
    interfaceRes: any;
    //可选范围默认值
    ableAll: any;
    //掩码配置
    maskConfig: any;
    //定位属性
    locationScope: any;
    title: string;
    continueText: string;
    allList: any[];
    currIndex: number;
    isContinue: boolean;
    submitType: number;
    showContinueBtn: boolean;
  }

  const emit = defineEmits(['reload']);
  const getLeftTreeActiveInfo: (() => any) | null = inject('getLeftTreeActiveInfo', null);
  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const [registerModal, { openModal, setModalProps }] = useModal();
  const formRef = ref<FormInstance>();
  const state = reactive<State>({
    dataForm: {
      id: '',
      unitId: '',
      unitName: '',
      contactPerson: '',
      contactPhone: '',
      examMode: 0, // 默认线上
      placeId: '',
      placeName: '',
      examDate: '',
      startTime: '',
      durationHours: '',
      estimatedCount: '',
      note: '',
      status: 0, // 默认待审核
      auditRemark: '',
      auditUserId: '',
      auditTime: '',
    },
    tableRows: {},
    dataRule: {
      unitName: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '单位名称不能为空'),
          trigger: 'blur',
        },
      ],
      contactPerson: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '联系人不能为空'),
          trigger: 'blur',
        },
      ],
      contactPhone: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '联系电话不能为空'),
          trigger: 'blur',
        },
      ],
      examMode: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '考试方式不能为空'),
          trigger: 'change',
        },
      ],
      placeName: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '考点名称不能为空'),
          trigger: 'blur',
        },
      ],
      examDate: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '考试日期不能为空'),
          trigger: 'change',
        },
      ],
      startTime: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '开始时间不能为空'),
          trigger: 'change',
        },
      ],
      durationHours: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '持续时长不能为空'),
          trigger: 'blur',
        },
      ],
      estimatedCount: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '预计人数不能为空'),
          trigger: 'blur',
        },
      ],
    },
    optionsObj: {
      examModeOptions: [
        { label: t('线上'), value: 0 },
        { label: t('线下'), value: 1 },
      ],
    },
    childIndex: -1,
    isEdit: false,
    interfaceRes: {},
    //可选范围默认值
    ableAll: {},

    //掩码配置
    maskConfig: {},
    //定位属性
    locationScope: {
      faddressDetail: [],
    },
    title: '',
    continueText: '',
    allList: [],
    currIndex: 0,
    isContinue: false,
    submitType: 0,
    showContinueBtn: true,
  });
  const { title, continueText, showContinueBtn, dataRule, dataForm, optionsObj, ableAll, maskConfig, submitType } = toRefs(state);

  const getPrevDisabled = computed(() => state.currIndex === 0);
  const getNextDisabled = computed(() => state.currIndex === state.allList.length - 1);
  // 表单权限
  const { hasFormP } = usePermission();

  defineExpose({ init });

  function init(data) {
    state.submitType = 0;
    state.isContinue = false;
    state.title = !data.id ? t('common.add2Text', '新增') : t('common.editText', '编辑');
    state.continueText = !data.id ? t('common.continueAndAddText', '确定并新增') : t('common.continueText', '确定并继续');
    setFormProps({ continueLoading: false });
    state.dataForm.id = data.id;
    openModal();
    state.allList = data.allList;
    state.currIndex = state.allList.length && data.id ? state.allList.findIndex(item => item.id === data.id) : 0;
    getAllSelectOptions();
    nextTick(() => {
      getForm().resetFields();
      setTimeout(initData, 0);
    });
  }
  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      // 设置默认值
      state.dataForm = {
        id: '',
        unitId: '',
        unitName: '',
        contactPerson: '',
        contactPhone: '',
        examMode: 0,
        placeId: '',
        placeName: '',
        examDate: '',
        startTime: '',
        durationHours: '',
        estimatedCount: '',
        note: '',
        status: 0,
        auditRemark: '',
        auditUserId: '',
        auditTime: '',
      };

      if (getLeftTreeActiveInfo) state.dataForm = { ...state.dataForm, ...(getLeftTreeActiveInfo() || {}) };
      state.childIndex = -1;
      changeLoading(false);
    }
  }
  function getForm() {
    const form = unref(formRef);
    if (!form) {
      throw new Error('form is null!');
    }
    return form;
  }
  function getData(id) {
    getInfo(id).then(res => {
      state.dataForm = res.data || {};
      state.childIndex = -1;
      changeLoading(false);
    });
  }
  async function handleSubmit(type) {
    try {
      const values = await getForm()?.validate();
      if (!values) return;
      setFormProps({ confirmLoading: true });
      const formMethod = state.dataForm.id ? update : create;
      formMethod(state.dataForm)
        .then(res => {
          createMessage.success(res.msg);
          setFormProps({ confirmLoading: false });
          if (state.submitType == 1) {
            initData();
            state.isContinue = true;
          } else {
            setFormProps({ open: false });
            emit('reload');
          }
        })
        .catch(() => {
          setFormProps({ confirmLoading: false });
        });
    } catch (_) {}
  }

  // 跳转上一个
  function handlePrev() {
    state.currIndex--;
    handleGetNewInfo();
  }

  // 跳转下一个
  function handleNext() {
    state.currIndex++;
    handleGetNewInfo();
  }

  // 重新获取编辑信息
  function handleGetNewInfo() {
    changeLoading(true);
    getForm().resetFields();
    const id = state.allList[state.currIndex].id;
    getData(id);
  }

  function setFormProps(data) {
    setModalProps(data);
  }

  //
  function changeLoading(loading) {
    setModalProps({ loading });
  }

  // 关闭弹窗
  async function onClose() {
    if (state.isContinue) emit('reload');
    return true;
  }

  function changeData(model, index) {
    state.isEdit = false;
    state.childIndex = index;
    for (let key in state.interfaceRes) {
      if (key != model) {
        let faceReList = state.interfaceRes[key];
        for (let i = 0; i < faceReList.length; i++) {
          let relationField = faceReList[i].relationField;
          if (relationField) {
            let modelAll = relationField.split('-');
            let faceMode = '';
            let faceMode2 = modelAll.length == 2 ? modelAll[0].substring(0, modelAll[0].length - 4) + modelAll[1] : '';
            for (let i = 0; i < modelAll.length; i++) {
              faceMode += modelAll[i];
            }
            if (faceMode == model || faceMode2 == model) {
              let options = 'get' + key + 'Options';
              eval(options)(true);
              changeData(key, index);
            }
          }
        }
      }
    }
  }
  function getAllSelectOptions() {}
</script>
