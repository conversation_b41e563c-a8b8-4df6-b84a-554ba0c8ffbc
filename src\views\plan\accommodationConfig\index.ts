import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
import { getForSelect as getPlanNameForSelect } from '../semester';
import { getForSelect as getForRoomSelect } from '../room';

const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';
import { onMounted, ref } from 'vue';

// 基础Api
export const planAccommodationConfigApi = '/api/plan/accommodationConfig';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: planAccommodationConfigApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: planAccommodationConfigApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: planAccommodationConfigApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: planAccommodationConfigApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: planAccommodationConfigApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: planAccommodationConfigApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: planAccommodationConfigApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: planAccommodationConfigApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: planAccommodationConfigApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: planAccommodationConfigApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: planAccommodationConfigApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: planAccommodationConfigApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: planAccommodationConfigApi + `/getForSelect`, data });
}

const roomTypeOptionsObj = ref([]);
const planOptionsObj = ref([]);

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'semesterName',
    label: t('计划名称'),
    component: 'Select',
    componentProps: {
      submitOnPressEnter: true,
      showSearch: true,
      options: planOptionsObj.value,
      fieldNames: {
        label: 'name',
        value: 'id',
      },
    },
  },
  {
    field: 'name',
    label: t('住宿名称'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
  {
    field: 'roomName',
    label: t('房型'),
    component: 'Select',
    componentProps: {
      submitOnPressEnter: true,
      options: roomTypeOptionsObj.value,
      fieldNames: {
        label: 'name',
        value: 'id',
      },
    },
  },
  {
    field: 'startDate',
    label: t('入住日期'),
    component: 'DateRange',
    componentProps: {
      submitOnPressEnter: true,
      format: 'YYYY-MM-DD',
    },
  },
  {
    field: 'endDate',
    label: t('退房日期'),
    component: 'DateRange',
    componentProps: {
      submitOnPressEnter: true,
      format: 'YYYY-MM-DD',
    },
  },
];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('计划名称'),
    dataIndex: 'semesterName',
    sorter: true,
    width: 120,
  },
  {
    title: t('住宿名称'),
    dataIndex: 'name',
    sorter: true,
    width: 120,
  },
  {
    title: t('房型'),
    dataIndex: 'roomName',
    sorter: true,
    width: 120,
  },
  {
    title: t('人数限制'),
    dataIndex: 'limitCount',
    sorter: true,
    width: 120,
  },
  {
    title: t('已选数'),
    dataIndex: 'selectedCount',
    sorter: true,
    width: 120,
  },
  {
    title: t('剩余数'),
    dataIndex: 'remain',
    sorter: true,
    width: 120,
  },
  {
    title: t('说明'),
    dataIndex: 'description',
    width: 120,
  },
  {
    title: t('入住日期'),
    dataIndex: 'startDate',
    sorter: true,
    width: 120,
    format: 'date|YYYY-MM-DD',
  },
  {
    title: t('退房日期'),
    dataIndex: 'endDate',
    sorter: true,
    width: 120,
    format: 'date|YYYY-MM-DD',
  },
  {
    title: t('入住时长'),
    dataIndex: 'days',
    sorter: true,
    width: 120,
  },
  {
    title: t('价格'),
    dataIndex: 'price',
    sorter: true,
    width: 120,
  },
];

export async function getAllOption(updateSchema) {
  getPlanNameForSelect({
    dataType: 1,
  }).then(res => {
    planOptionsObj.value = res.data.list;
    updateSchema({
      field: 'semesterName',
      label: t('计划名称'),
      component: 'Select',
      componentProps: {
        submitOnPressEnter: true,
        showSearch: true,
        options: planOptionsObj.value,
        fieldNames: {
          label: 'name',
          value: 'id',
        },
      },
    });
  });
  getForRoomSelect({
    dataType: 1,
  }).then(res => {
    roomTypeOptionsObj.value = res.data.list;
    updateSchema({
      field: 'roomName',
      label: t('房型'),
      component: 'Select',
      componentProps: {
        submitOnPressEnter: true,
        options: roomTypeOptionsObj.value,
        fieldNames: {
          label: 'name',
          value: 'id',
        },
      },
    });
  });
}
