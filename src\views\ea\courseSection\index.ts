import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';

// 基础Api
export const courseSectionApi = '/api/ea/courseSection';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: courseSectionApi + `/getList`, data });
}
// 获取树形列表
export function getTree(data) {
  return defHttp.post({ url: courseSectionApi + `/getTree`, data });
}

// 新建
export function create(data) {
  return defHttp.post({ url: courseSectionApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: courseSectionApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: courseSectionApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: courseSectionApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: courseSectionApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: courseSectionApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: courseSectionApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: courseSectionApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: courseSectionApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: courseSectionApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: courseSectionApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: courseSectionApi + `/getForSelect`, data });
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'no',
    label: t('编号'),
    component: 'Input',
    colProps: { span: 24 },
    componentProps: {
      submitOnPressEnter: true,
    },
  },
];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  // {
  //   title: t('主键'),
  //   dataIndex: 'id',
  //   width: 120,
  // },
  // {
  //   title: t('父节'),
  //   dataIndex: 'parentId',
  //   width: 120,
  // },
  {
    title: t('课程'),
    dataIndex: 'courseName',
    width: 120,
  },
  {
    title: t('编号'),
    dataIndex: 'no',
    width: 120,
  },
  {
    title: t('排序'),
    dataIndex: 'displayOrder',
    width: 120,
  },
  {
    title: t('标题名称'),
    dataIndex: 'name',
    width: 120,
  },
  {
    title: t('内容'),
    dataIndex: 'description',
    width: 120,
  },
  {
    title: t('叶子节点'),
    dataIndex: 'leafFlag',
    width: 120,
    customRender: xundaUtils.customRenderBoolean,
  },
  {
    title: t('历史版本'),
    dataIndex: 'version',
    width: 120,
  },
  {
    title: t('章节模式'),
    dataIndex: 'mode',
    width: 120,
  },
  {
    title: t('章节资源'),
    dataIndex: 'resource',
    width: 120,
  },
  {
    title: t('章节配置'),
    dataIndex: 'extraConfig',
    width: 120,
  },
];
