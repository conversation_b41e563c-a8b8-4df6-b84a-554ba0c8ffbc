<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    width="600px"
    :minHeight="100"
    :cancelText="t('common.cancelText', '取消')"
    :okText="t('common.okText', '确定')"
    @ok="handleSubmit"
    :closeFunc="onClose">
    <template #title>
      <a-space :size="10">
        <div class="text-16px font-medium">{{ title }}</div>
        <a-space-compact size="small" block v-if="dataForm.id">
          <a-tooltip :title="t('common.prevRecord')">
            <a-button size="small" :disabled="getPrevDisabled" @click="handlePrev">
              <i class="icon-ym icon-ym-caret-left text-10px"></i>
            </a-button>
          </a-tooltip>
          <a-tooltip :title="t('common.nextRecord')">
            <a-button size="small" :disabled="getNextDisabled" @click="handleNext">
              <i class="icon-ym icon-ym-caret-right text-10px"></i>
            </a-button>
          </a-tooltip>
        </a-space-compact>
      </a-space>
    </template>
    <template #insertFooter>
      <div class="loat-left mt-5px">
        <XundaCheckboxSingle v-model:value="submitType" :label="continueText" />
      </div>
    </template>
    <a-row class="dynamic-form">
      <a-form
        :colon="false"
        size="middle"
        layout="horizontal"
        labelAlign="left"
        :labelCol="{ style: { width: '100px' } }"
        :model="dataForm"
        :rules="dataRule"
        ref="formRef">
        <a-row :gutter="15">
          <!-- 具体表单 -->
          <a-col :span="12" class="ant-col-item" :hidden="false">
            <a-form-item name="name">
              <template #label>费用名 </template>
              <XundaSelect
                v-if="isCreate"
                v-model:value="dataForm.name"
                :disabled="false"
                @change="changeData('name', -1)"
                placeholder="币种"
                :allowClear="true"
                :style="{ width: '100%' }"
                showSearch
                :options="optionsObj.nameOptions"
                :fieldNames="optionsObj.nameFieldNames">
              </XundaSelect>
              <p v-else>{{ xundaUtils.optionText(dataForm.name, optionsObj.nameOptions, optionsObj.nameFieldNames) }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="12" class="ant-col-item" :hidden="false">
            <a-form-item name="currency">
              <template #label>币种 </template>
              <XundaSelect
                v-if="isCreate"
                v-model:value="dataForm.currency"
                :disabled="false"
                @change="changeData('currency', -1)"
                placeholder="币种"
                :allowClear="true"
                :style="{ width: '100%' }"
                showSearch
                :options="optionsObj.currencyOptions"
                :fieldNames="optionsObj.currencyFieldNames">
              </XundaSelect>
              <p v-else>{{ xundaUtils.optionText(dataForm.currency, optionsObj.currencyOptions, optionsObj.currencyFieldNames) }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="12" class="ant-col-item" :hidden="false">
            <a-form-item name="originalPrice">
              <template #label>原价 </template>
              <XundaInputNumber
                v-if="isCreate"
                v-model:value="dataForm.originalPrice"
                :disabled="false"
                @change="changeData('originalPrice', -1)"
                placeholder="请输入原价"
                :allowClear="true"
                :style="{ width: '100%' }"
                :precision="2"
                :controls="true"
                :addonAfter="getCurrencySymbol">
              </XundaInputNumber>
              <p v-else>{{ xundaUtils.thousandsFormat(dataForm.originalPrice) }} {{ getCurrencySymbol }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="12" class="ant-col-item" :hidden="isCreate">
            <a-form-item name="systemDiscount">
              <template #label>系统优惠 </template>
              <p>{{ xundaUtils.thousandsFormat(dataForm.systemDiscount) }} {{ getCurrencySymbol }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="12" class="ant-col-item" :hidden="false">
            <a-form-item name="manualAdjustment">
              <template #label>优惠费用 </template>
              <XundaInputNumber
                v-if="isCreate || isAdjust"
                v-model:value="dataForm.manualAdjustment"
                :disabled="false"
                @change="changeData('manualAdjustment', -1)"
                :allowClear="true"
                :style="{ width: '100%' }"
                :precision="2"
                :controls="true"
                :max="maxPrice"
                :addonAfter="getCurrencySymbol">
              </XundaInputNumber>
              <p v-else>{{ xundaUtils.thousandsFormat(dataForm.manualAdjustment) }} {{ getCurrencySymbol }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="12" class="ant-col-item" :hidden="isCreate">
            <a-form-item name="realPrice">
              <template #label>实价 </template>
              <p>{{ xundaUtils.thousandsFormat(dataForm.realPrice) }} {{ getCurrencySymbol }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="discountDescription">
              <template #label>优惠说明 </template>
              <XundaTextarea
                v-if="isCreate || isAdjust"
                v-model:value="dataForm.discountDescription"
                :disabled="false"
                @change="changeData('discountDescription', -1)"
                :allowClear="true"
                :style="{ width: '100%' }"
                :precision="2"
                :controls="true">
              </XundaTextarea>
              <p v-else>{{ xundaUtils.thousandsFormat(dataForm.realPrice) }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="true">
            <a-form-item name="payState">
              <template #label>支付状态 </template>
              <XundaSelect
                v-model:value="dataForm.payState"
                :disabled="false"
                @change="changeData('payState', -1)"
                placeholder="请选择支付状态"
                :allowClear="true"
                :style="{ width: '100%' }"
                showSearch
                :options="optionsObj.payStateOptions"
                :fieldNames="optionsObj.payStateFieldNames">
              </XundaSelect>
            </a-form-item>
          </a-col>
          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { create, update, getInfo } from '@/views/study/tuitionRecord';
  import { reactive, toRefs, nextTick, ref, unref, computed, inject } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useUserStore } from '@/store/modules/user';
  import type { FormInstance } from 'ant-design-vue';
  import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
  // 表单权限
  import { usePermission } from '@/hooks/web/usePermission';
  import { xundaUtils } from '@/utils/xunda';
  interface State {
    dataForm: any;
    tableRows: any;
    dataRule: any;
    optionsObj: any;
    childIndex: any;
    isEdit: any;
    interfaceRes: any;
    //可选范围默认值
    ableAll: any;
    //掩码配置
    maskConfig: any;
    //定位属性
    locationScope: any;
    title: string;
    continueText: string;
    allList: any[];
    currIndex: number;
    isContinue: boolean;
    submitType: number;
    showContinueBtn: boolean;
  }

  const emit = defineEmits(['reload']);
  const getLeftTreeActiveInfo: (() => any) | null = inject('getLeftTreeActiveInfo', null);
  const userStore = useUserStore();
  const { createMessage } = useMessage();
  const { t } = useI18n();
  const [registerModal, { openModal, setModalProps }] = useModal();
  const formRef = ref<FormInstance>();

  const isCreate = computed(() => {
    return state.dataForm.id === '' || state.dataForm.id === undefined;
  });

  const isAdjust = ref(false);

  const state = reactive<State>({
    dataForm: {
      id: '',
      registrationId: '',
      name: '',
      currency: '',
      originalPrice: undefined,
      realPrice: 0,
      payState: '',
      flowTaskId: '',
      flowId: '',
      paymentId: '',
    },
    tableRows: {},
    dataRule: {
      id: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '主键不能为空'),
          trigger: 'blur',
        },
      ],
      registrationId: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '学员不能为空'),
          trigger: 'blur',
        },
      ],
      name: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '费用名不能为空'),
          trigger: 'blur',
        },
      ],
      originalPrice: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '原价不能为空'),
          trigger: 'blur',
        },
      ],
      realPrice: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '实价不能为空'),
          trigger: 'blur',
        },
      ],
      payState: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '支付状态不能为空'),
          trigger: 'blur',
        },
      ],
    },
    optionsObj: {
      //选项配置
      defaultProps: { label: 'fullName', value: 'enCode' }, // 默认下拉选择键值
    },
    childIndex: -1,
    isEdit: false,
    interfaceRes: {},
    //可选范围默认值
    ableAll: {},

    //掩码配置
    maskConfig: {},
    //定位属性
    locationScope: {
      faddressDetail: [],
    },
    title: '',
    continueText: '',
    allList: [],
    currIndex: 0,
    isContinue: false,
    submitType: 0,
    showContinueBtn: true,
  });
  const { title, continueText, dataRule, dataForm, optionsObj, submitType } = toRefs(state);

  const getPrevDisabled = computed(() => state.currIndex === 0);
  const getNextDisabled = computed(() => state.currIndex === state.allList.length - 1);

  // 获取当前选择的货币符号
  const getCurrencySymbol = computed(() => {
    // 根据货币代码获取对应符号，这里以常见货币为例
    const currencyMap = {
      '001': '元', // 人民币
      '002': '$', // 美元
      '003': '€', // 欧元
      '004': '£', // 英镑
    };
    return currencyMap[state.dataForm.currency] || '元';
  });
  // 表单权限
  const { hasFormP } = usePermission();

  defineExpose({ init });

  function init(data) {
    isAdjust.value = data.isAdjust ? true : false;
    state.submitType = 0;
    state.isContinue = false;
    state.title = !data.id ? t('common.add2Text', '新增') : t('common.editText', '编辑');
    state.continueText = !data.id ? t('common.continueAndAddText', '确定并新增') : t('common.continueText', '确定并继续');
    setFormProps({ continueLoading: false });
    state.dataForm.id = data.id;
    openModal();
    state.allList = data.allList;
    state.currIndex = state.allList.length && data.id ? state.allList.findIndex(item => item.id === data.id) : 0;
    getAllSelectOptions();
    nextTick(() => {
      getForm().resetFields();
      setTimeout(initData, 0);
    });
  }
  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      // 设置默认值
      state.dataForm = {
        id: '',
        registrationId: '',
        name: '',
        currency: '001',
        originalPrice: undefined,
        systemDiscount: 0,
        manualAdjustment: 0,
        realPrice: 0,
        payState: '',
        flowTaskId: '',
        flowId: '',
        paymentId: '',
      };

      if (getLeftTreeActiveInfo) state.dataForm = { ...state.dataForm, ...(getLeftTreeActiveInfo() || {}) };
      state.childIndex = -1;
      changeLoading(false);
    }
  }
  function getForm() {
    const form = unref(formRef);
    if (!form) {
      throw new Error('form is null!');
    }
    return form;
  }
  function getData(id) {
    getInfo(id).then(res => {
      state.dataForm = res.data || {};
      state.childIndex = -1;
      changeLoading(false);
    });
  }
  async function handleSubmit(type) {
    try {
      const values = await getForm()?.validate();
      if (!values) return;
      setFormProps({ confirmLoading: true });
      const formMethod = state.dataForm.id ? update : create;
      console.log('values', state.dataForm);
      formMethod(state.dataForm)
        .then(res => {
          createMessage.success(res.msg);
          setFormProps({ confirmLoading: false });
          if (state.submitType == 1) {
            initData();
            state.isContinue = true;
          } else {
            setFormProps({ open: false });
            emit('reload');
          }
        })
        .catch(() => {
          setFormProps({ confirmLoading: false });
        });
    } catch (_) {}
  }

  // 跳转上一个
  function handlePrev() {
    state.currIndex--;
    handleGetNewInfo();
  }

  // 跳转下一个
  function handleNext() {
    state.currIndex++;
    handleGetNewInfo();
  }

  // 重新获取编辑信息
  function handleGetNewInfo() {
    changeLoading(true);
    getForm().resetFields();
    const id = state.allList[state.currIndex].id;
    getData(id);
  }

  function setFormProps(data) {
    setModalProps(data);
  }

  //
  function changeLoading(loading) {
    setModalProps({ loading });
  }

  // 关闭弹窗
  async function onClose() {
    if (state.isContinue) emit('reload');
    return true;
  }

  function changeData(model, index) {
    state.isEdit = false;
    state.childIndex = index;

    // 当原价或优惠费用发生变化时，更新实价
    if (model === 'originalPrice' || model === 'manualAdjustment' || model === 'systemDiscount') {
      updateRealPrice();
    }

    for (let key in state.interfaceRes) {
      if (key != model) {
        let faceReList = state.interfaceRes[key];
        for (let i = 0; i < faceReList.length; i++) {
          let relationField = faceReList[i].relationField;
          if (relationField) {
            let modelAll = relationField.split('-');
            let faceMode = '';
            let faceMode2 = modelAll.length == 2 ? modelAll[0].substring(0, modelAll[0].length - 4) + modelAll[1] : '';
            for (let i = 0; i < modelAll.length; i++) {
              faceMode += modelAll[i];
            }
            if (faceMode == model || faceMode2 == model) {
              let options = 'get' + key + 'Options';
              eval(options)(true);
              changeData(key, index);
            }
          }
        }
      }
    }
  }
  function getAllSelectOptions() {
    optionsObj.value.nameOptions = [];
    optionsObj.value.nameFieldNames = { label: 'fullName', value: 'enCode' };
    optionsObj.value.currencyOptions = [];
    optionsObj.value.currencyFieldNames = { label: 'fullName', value: 'enCode' };
    getDictionaryDataSelector('currencyType').then(res => {
      optionsObj.value.currencyOptions = res.data.list;
    });
    getDictionaryDataSelector('feeType').then(res => {
      optionsObj.value.nameOptions = res.data.list;
    });
  }

  // 计算并更新实价
  function updateRealPrice() {
    const originalPrice = state.dataForm.originalPrice || 0;
    const manualAdjustment = state.dataForm.manualAdjustment || 0;
    const systemDiscount = state.dataForm.systemDiscount || 0;

    // 实价 = 原价 - 系统优惠 - 优惠费用
    state.dataForm.realPrice = parseFloat((originalPrice - systemDiscount - manualAdjustment).toFixed(2));

    // 确保实价不小于0
    if (state.dataForm.realPrice < 0) {
      state.dataForm.realPrice = 0;
    }
  }

  const maxPrice = computed(() => {
    return state.dataForm.originalPrice - state.dataForm.systemDiscount;
  });

  const minPrice = computed(() => {
    return state.dataForm.originalPrice - state.dataForm.systemDiscount;
  });

  const isDisabled = computed(() => {
    return state.dataForm.originalPrice === 0;
  });
</script>
