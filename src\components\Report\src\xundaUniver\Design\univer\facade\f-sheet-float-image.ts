import { XundaSheetsFloatImageService } from '../services/sheet-float-image.service';

export class XundaFacadeSheetsFloatImage {
  constructor(private readonly _xundaSheetsFloatImageService: XundaSheetsFloatImageService) {}

  savePiniaStoreId(value: any) {
    this._xundaSheetsFloatImageService.savePiniaStoreId(value);
  }

  clearFocusDrawingId() {
    this._xundaSheetsFloatImageService.clearFocusDrawingId();
  }
}
