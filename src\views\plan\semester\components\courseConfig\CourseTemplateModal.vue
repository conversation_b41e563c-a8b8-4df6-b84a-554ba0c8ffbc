<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="课程模板导入" @ok="handleSubmit" width="1000px" :minHeight="500" :destroyOnClose="true">
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <a-input-search v-model:value="searchValue" placeholder="请输入课程名称搜索" style="width: 200px" @search="handleSearch" />
      </template>
    </BasicTable>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { nextTick, ref } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { BasicTable, useTable } from '@/components/Table';
  import { useMessage } from '@/hooks/web/useMessage';

  import { getCourseTemplateList, importCourseTemplate } from '.';
  const emit = defineEmits(['register', 'reload']);
  const props = defineProps({
    semesterId: {
      type: String,
      required: true,
    },
  });
  const searchValue = ref('');
  const { createMessage } = useMessage();

  const [registerModal, { closeModal, openModal }] = useModal();

  // 定义表格列
  const columns = [
    {
      title: '课程名称',
      dataIndex: 'name',
      width: 200,
    },
    {
      title: '课程代码',
      dataIndex: 'no',
      width: 120,
    },
    {
      title: '学分',
      dataIndex: 'credit',
      width: 80,
    },
    {
      title: '课时',
      dataIndex: 'hours',
      width: 80,
    },
    {
      title: '课程类型',
      dataIndex: 'courseType',
      width: 120,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 160,
    },
  ];

  // 注册表格
  const [registerTable, { reload, getSelectRowKeys }] = useTable({
    columns,
    rowKey: 'id',
    searchInfo: {
      templateFlag: true,
      dataType: 0,
    },
    api: getCourseTemplateList, // 这里需要添加获取课程模板列表的API
    rowSelection: { type: 'checkbox' },
    pagination: true,
    striped: false,
    useSearchForm: false,
    showTableSetting: true,
    bordered: true,
    showIndexColumn: true,
    canResize: false,
    immediate: false,
  });

  // 搜索处理
  function handleSearch(value: string) {
    reload({
      searchInfo: {
        keyword: value,
      },
    });
  }

  // 提交处理
  async function handleSubmit() {
    const selectedKeys = getSelectRowKeys();
    if (!selectedKeys.length) {
      createMessage.warning('请选择要导入的课程模板');
      return;
    }
    try {
      // 这里需要添加导入课程模板的API
      importCourseTemplate({ id: props.semesterId, courseIds: selectedKeys }).then(res => {
        createMessage.success('导入成功');
        closeModal();
        emit('reload');
      });
    } catch (error) {
      createMessage.error('导入失败');
    }
  }
  function init(data) {
    openModal();
    nextTick(() => {
      reload();
    });
  }

  defineExpose({
    init,
  });
</script>

<style lang="less" scoped>
  .basic-table-wrapper {
    background: #fff;
    border-radius: 4px;

    :deep(.ant-table) {
      .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 500;
      }

      .ant-table-row {
        &:hover {
          td {
            background: #e6f4ff;
          }
        }
      }
    }
  }
</style>
