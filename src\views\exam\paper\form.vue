<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    width="800px"
    :minHeight="100"
    :cancelText="t('common.cancelText', '取消')"
    :okText="currentStep === 0 ? '下一步' : '保存'"
    @ok="handleSubmit"
    :closeFunc="onClose">
    <template #title>
      <a-space :size="10">
        <div class="text-16px font-medium">{{ title }}</div>
        <a-space-compact size="small" block v-if="dataForm.id">
          <a-tooltip :title="t('common.prevRecord')">
            <a-button size="small" :disabled="getPrevDisabled" @click="handlePrev">
              <i class="icon-ym icon-ym-caret-left text-10px"></i>
            </a-button>
          </a-tooltip>
          <a-tooltip :title="t('common.nextRecord')">
            <a-button size="small" :disabled="getNextDisabled" @click="handleNext">
              <i class="icon-ym icon-ym-caret-right text-10px"></i>
            </a-button>
          </a-tooltip>
        </a-space-compact>
      </a-space>
    </template>
    <template #insertFooter>
      <div class="float-left mt-5px">
        <XundaCheckboxSingle v-model:value="submitType" :label="continueText" />
      </div>
    </template>
    
    <!-- 步骤导航 -->
    <div class="steps-container mb-20px">
      <a-steps v-model:current="currentStep" type="navigation" size="small">
        <a-step title="基本信息" />
        <a-step title="题目配置" />
      </a-steps>
    </div>

    <!-- 第一步：基本信息 -->
    <div v-show="currentStep === 0" class="step-content">
      <a-row class="dynamic-form">
        <a-form
          :colon="false"
          size="middle"
          layout="horizontal"
          labelAlign="left"
          :labelCol="{ style: { width: '100px' } }"
          :model="dataForm"
          :rules="basicDataRule"
          ref="basicFormRef">
          <a-row :gutter="15">
            <a-col :span="12" class="ant-col-item" :hidden="false">
              <a-form-item name="semesterId">
                <template #label>计划 </template>
                <XundaSelect
                  v-model:value="dataForm.semesterId"
                  :disabled="false"
                  @change="changeData('semesterId', -1)"
                  placeholder="请选择计划"
                  :allowClear="true"
                  :style="{ width: '100%' }"
                  showSearch
                  :options="optionsObj.semesterIdOptions"
                  :fieldNames="optionsObj.semesterIdFieldNames">
                </XundaSelect>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item" :hidden="false">
              <a-form-item name="courseId">
                <template #label>课程 </template>
                <XundaSelect
                  v-model:value="dataForm.courseId"
                  :disabled="false"
                  @change="changeData('courseId', -1)"
                  placeholder="请选择课程"
                  :allowClear="true"
                  :style="{ width: '100%' }"
                  showSearch
                  :options="optionsObj.courseIdOptions"
                  :fieldNames="optionsObj.courseIdFieldNames">
                </XundaSelect>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item">
              <a-form-item name="name">
                <template #label>试卷名称</template>
                <XundaInput
                  v-model:value="dataForm.name"
                  :disabled="false"
                  @change="changeData('name', -1)"
                  placeholder="请输入试卷名称"
                  :allowClear="true"
                  :style="{ width: '100%' }">
                </XundaInput>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="ant-col-item">
              <a-form-item name="paperMode">
                <template #label>试卷模式</template>
                <a-radio-group v-model:value="dataForm.paperMode" @change="changeData('paperMode', -1)">
                  <a-radio value="随机">随机</a-radio>
                  <a-radio value="固定">固定</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-row>
    </div>

    <!-- 第二步：题目配置 -->
    <div v-show="currentStep === 1" class="step-content">
      <a-row class="dynamic-form">
        <a-form
          :colon="false"
          size="middle"
          layout="vertical"
          labelAlign="left"
          :model="questionConfigForm"
          ref="questionFormRef">
          
          <!-- 随机组卷配置 -->
          <div v-if="dataForm.paperMode === '随机'" class="config-section">
            <h4 class="section-title">随机组卷配置</h4>
            <div class="question-config-table">
              <a-table
                :columns="questionColumns"
                :dataSource="questionConfigForm.questionTypes"
                :pagination="false"
                :bordered="true"
                size="small">
                <template #bodyCell="{ column, record, index }">
                  <template v-if="column.key === 'questionCount'">
                    <a-input-number
                      v-model:value="record.questionCount"
                      :min="0"
                      :precision="0"
                      style="width: 80px"
                      @change="calculateTotalScore"
                      placeholder="题目数" />
                  </template>
                  <template v-else-if="column.key === 'scorePerQuestion'">
                    <a-input-number
                      v-model:value="record.scorePerQuestion"
                      :min="0"
                      :precision="1"
                      style="width: 80px"
                      @change="calculateTotalScore"
                      placeholder="分值" />
                  </template>
                  <template v-else-if="column.key === 'totalScore'">
                    <span class="total-score">{{ record.totalScore || 0 }}</span>
                  </template>
                </template>
              </a-table>
              <div class="total-summary">
                <span class="summary-label">题型总分合计：</span>
                <span class="summary-value">{{ questionConfigForm.totalQuestionScore }}</span>
                <span class="summary-unit">分</span>
              </div>
            </div>
          </div>

          <!-- 固定题目配置 -->
          <div v-if="dataForm.paperMode === '固定'" class="config-section">
            <h4 class="section-title">固定题目配置</h4>
            <PaperQuestionList :paperId="computedPaperId" />
          </div>
        </a-form>
      </a-row>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { create, update, getInfo } from '@/views/exam/paper';
  import { reactive, toRefs, nextTick, ref, unref, computed, inject } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useUserStore } from '@/store/modules/user';
  import type { FormInstance } from 'ant-design-vue';
  import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
  // 表单权限
  import { usePermission } from '@/hooks/web/usePermission';
  import { XundaSelect, XundaInput, XundaInputNumber, XundaTextarea, XundaDatePicker, XundaCheckboxSingle } from '@/components/Xunda';
  import { Radio, Checkbox, Select, InputGroup, Divider } from 'ant-design-vue';
  // 导入API
  import { getForSelect as getSemesterForSelect } from '@/views/plan/semester';
  import { getForSelect as getCourseForSelect } from '@/views/ea/course';
  import PaperQuestionList from '@/views/exam/paperQuestion/index.vue'
  
  interface State {
    dataForm: any;
    tableRows: any;
    basicDataRule: any;
    optionsObj: any;
    childIndex: any;
    isEdit: any;
    interfaceRes: any;
    ableAll: any;
    maskConfig: any;
    locationScope: any;
    title: string;
    continueText: string;
    allList: any[];
    currIndex: number;
    isContinue: boolean;
    submitType: number;
    showContinueBtn: boolean;
    currentStep: number;
    saveLoading: boolean;
    questionConfigForm: any;
    questionColumns: any[];
  }

  const emit = defineEmits(['reload']);
  const getLeftTreeActiveInfo: (() => any) | null = inject('getLeftTreeActiveInfo', null);
  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const [registerModal, { openModal, setModalProps }] = useModal();
  const basicFormRef = ref<FormInstance>();
  const questionFormRef = ref<FormInstance>();
  
  const state = reactive<State>({
    dataForm: {
      id: '',
      semesterId: '',
      courseId: '',
      name: '',
      questionConfig: '',
      paperMode: '',
      paperState: ''
    },
    tableRows: {},
    basicDataRule: {
      semesterId: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '计划不能为空'),
          trigger: 'change',
        },
      ],
      courseId: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '课程不能为空'),
          trigger: 'change',
        },
      ],
      name: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '试卷名称不能为空'),
          trigger: 'blur',
        },
      ],
      paperMode: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '试卷模式不能为空'),
          trigger: 'change',
        },
      ],
    },
    optionsObj: {
      defaultProps: { label: 'fullName', value: 'enCode' },
    },
    childIndex: -1,
    isEdit: false,
    interfaceRes: {},
    ableAll: {},
    maskConfig: {},
    locationScope: {
      faddressDetail: [],
    },
    title: '',
    continueText: '',
    allList: [],
    currIndex: 0,
    isContinue: false,
    submitType: 0,
    showContinueBtn: true,
    currentStep: 0,
    saveLoading: false,
    questionConfigForm: {
      questionTypes: [
          { name: '单选', questionType: 'single', questionCount: 0, scorePerQuestion: 0, totalScore: 0 },
          { name: '多选',questionType: 'multi', questionCount: 0, scorePerQuestion: 0, totalScore: 0 },
          { name: '判断', questionType: 'truefalse', questionCount: 0, scorePerQuestion: 0, totalScore: 0 },
          { name: '填空',questionType: 'fillblank', questionCount: 0, scorePerQuestion: 0, totalScore: 0 },
          { name: '简答',questionType: 'shortanswer', questionCount: 0, scorePerQuestion: 0, totalScore: 0 },
      ],
      totalQuestionScore: 0,
      fixedTotalScore: 0,
      selectedQuestions: [],
    },
    questionColumns: [
      {
        title: '题型',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '题目数',
        dataIndex: 'questionCount',
        key: 'questionCount',
      },
      {
        title: '分值',
        dataIndex: 'scorePerQuestion',
        key: 'scorePerQuestion',
      },
      {
        title: '总分',
        dataIndex: 'totalScore',
        key: 'totalScore',
      },
    ],
  });
  
  const { 
    title, 
    continueText, 
    showContinueBtn, 
    basicDataRule, 
    dataForm, 
    optionsObj, 
    ableAll, 
    maskConfig, 
    submitType,
    currentStep,
    saveLoading,
    questionConfigForm,
    questionColumns
  } = toRefs(state);

  const getPrevDisabled = computed(() => state.currIndex === 0);
  const getNextDisabled = computed(() => state.currIndex === state.allList.length - 1);
  
  // 表单权限
  const { hasFormP } = usePermission();

  const computedPaperId = computed(() => state.dataForm.id)

  defineExpose({ init });

  function init(data) {
    state.submitType = 0;
    state.isContinue = false;
    state.currentStep = 0;
    state.title = !data.id ? t('common.add2Text', '新增') : t('common.editText', '编辑');
    state.continueText = !data.id ? t('common.continueAndAddText', '确定并新增') : t('common.continueText', '确定并继续');
    setFormProps({ continueLoading: false });
    state.dataForm.id = data.id;
    openModal();
    state.allList = data.allList;
    state.currIndex = state.allList.length && data.id ? state.allList.findIndex(item => item.id === data.id) : 0;
    getAllSelectOptions();
    nextTick(() => {
      getBasicForm().resetFields();
      getQuestionForm().resetFields && getQuestionForm().resetFields();
      setTimeout(initData, 0);
    });
  }
  
  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      // 设置默认值
      state.dataForm = {
        id: '',
        semesterId: '',
        courseId: '',
        name: '',
        questionConfig: '',
        paperMode: '',
        paperState: ''
      };
      state.questionConfigForm = {
        questionTypes: [
          { name: '单选', questionType: 'single', questionCount: 0, scorePerQuestion: 0, totalScore: 0 },
          { name: '多选',questionType: 'multi', questionCount: 0, scorePerQuestion: 0, totalScore: 0 },
          { name: '判断', questionType: 'truefalse', questionCount: 0, scorePerQuestion: 0, totalScore: 0 },
          { name: '填空',questionType: 'fillblank', questionCount: 0, scorePerQuestion: 0, totalScore: 0 },
          { name: '简答',questionType: 'shortanswer', questionCount: 0, scorePerQuestion: 0, totalScore: 0 },
        ],
        totalQuestionScore: 0,
        fixedTotalScore: 0,
        selectedQuestions: [],
      };
      state.childIndex = -1;
      changeLoading(false);
    }
  }
  
  function getBasicForm() {
    const form = unref(basicFormRef);
    if (!form) {
      throw new Error('basicForm is null!');
    }
    return form;
  }
  
  function getQuestionForm() {
    const form = unref(questionFormRef);
    if (!form) {
      throw new Error('questionForm is null!');
    }
    return form;
  }
  
  function getData(id) {
    getInfo(id).then(res => {
      state.dataForm = res.data || {};
      state.childIndex = -1;
      changeLoading(false);
    });
  }
  
  // 步骤控制
  function handleStepNext() {
    if (state.currentStep < 2) {
      // 验证当前步骤表单
      let currentForm;
      if (state.currentStep === 0) {
        currentForm = getBasicForm();
      } else if (state.currentStep === 1) {
        currentForm = getQuestionForm();
      }
      
      currentForm.validate().then(async () => {
        // 第一步额外验证：确保试卷模式已选择
        if (state.currentStep === 0 && !state.dataForm.paperMode) {
          createMessage.error('请选择试卷模式');
          return;
        }
        
        // 验证通过后，先保存当前步骤数据
        try {
          state.saveLoading = true;
          
          // 准备保存数据
          const formData = {
            ...state.dataForm,
            questionConfig: JSON.stringify(getQuestionConfigForSubmit())
          };
          
          // 确保必填字段存在
          if (!formData.semesterId) {
            createMessage.error('请选择计划');
            state.saveLoading = false;
            return;
          }
          if (!formData.courseId) {
            createMessage.error('请选择课程');
            state.saveLoading = false;
            return;
          }
          if (!formData.name) {
            createMessage.error('请输入试卷名称');
            state.saveLoading = false;
            return;
          }
          if (!formData.paperMode) {
            createMessage.error('请选择试卷模式');
            state.saveLoading = false;
            return;
          }
          
          console.log('准备保存的数据:', formData);
          
          const formMethod = state.dataForm.id ? update : create;
          const res = await formMethod(formData);
          console.log('保存响应:', res);
          if (res && res.data) {
            if (typeof res.data === 'string' || typeof res.data === 'number') {
              state.dataForm.id = res.data;
            } else if (res.data.id) {
              state.dataForm.id = res.data.id;
            }
          }
          console.log('保存后id:', state.dataForm.id);
          
          // 保存成功后进入下一步
          state.currentStep++;
          state.saveLoading = false;
          createMessage.success('保存成功');
          
          // 刷新列表
          emit('reload');
        } catch (error: any) {
          console.error('保存失败:', error);
          state.saveLoading = false;
          if (error.response && error.response.data && error.response.data.message) {
            createMessage.error(`保存失败: ${error.response.data.message}`);
          } else {
            createMessage.error('保存失败，请重试');
          }
        }
      }).catch((error: any) => {
        console.error('表单验证失败:', error);
        // 验证失败，不进入下一步
      });
    }
  }
  
  // 保存
  async function handleSave() {
    try {
      // 验证所有表单
      const validationPromises = [
        getBasicForm().validate()
      ];

      console.log(state.dataForm.id)
      
      await Promise.all(validationPromises);
      
      state.saveLoading = true;
      const formData = {
        ...state.dataForm,
        questionConfig: JSON.stringify(getQuestionConfigForSubmit())
      };
      
      const formMethod = state.dataForm.id ? update : create;
      const res = await formMethod(formData);
      
      // 兼容后端data为字符串id的情况
      if (res && res.data) {
        if (typeof res.data === 'string' || typeof res.data === 'number') {
          state.dataForm.id = res.data;
        } else if (res.data.id) {
          state.dataForm.id = res.data.id;
        }
      }
      
      createMessage.success('保存成功');
      state.saveLoading = false;
      
      if (!state.dataForm.id && state.submitType == 1) {
        // 只有新增时才继续新增
        initData();
        state.isContinue = true;
      } else {
        setFormProps({ open: false });
        emit('reload');
      }
    } catch (error) {
      state.saveLoading = false;
      createMessage.error('保存失败，请重试');
    }
  }
  
  async function handleSubmit(type) {
    // 根据当前步骤执行不同操作
    if (state.currentStep === 0) {
      // 第一步：点击确定时执行下一步
      await handleStepNext();
    } else if (state.currentStep === 1) {
      // 第二步：点击确定时执行保存
      await handleSave();
    }
  }

  // 跳转上一个
  function handlePrev() {
    state.currIndex--;
    handleGetNewInfo();
  }

  // 跳转下一个
  function handleNext() {
    state.currIndex++;
    handleGetNewInfo();
  }

  // 重新获取编辑信息
  function handleGetNewInfo() {
    changeLoading(true);
    getBasicForm().resetFields();
    getQuestionForm().resetFields && getQuestionForm().resetFields();
    const id = state.allList[state.currIndex].id;
    getData(id);
  }

  function setFormProps(data) {
    setModalProps(data);
  }

  //
  function changeLoading(loading) {
    setModalProps({ loading });
  }

  // 关闭弹窗
  async function onClose() {
    if (state.isContinue) emit('reload');
    return true;
  }

  function changeData(model, index) {
    state.isEdit = false;
    state.childIndex = index;
    if (model === 'paperMode') {
      if (state.dataForm.paperMode === '随机') {
        state.questionConfigForm.selectedQuestions = [];
      } else if (state.dataForm.paperMode === '固定') {
        state.questionConfigForm.questionTypes = [
         { name: '单选', questionType: 'single', questionCount: 0, scorePerQuestion: 0, totalScore: 0 },
          { name: '多选',questionType: 'multi', questionCount: 0, scorePerQuestion: 0, totalScore: 0 },
          { name: '判断', questionType: 'truefalse', questionCount: 0, scorePerQuestion: 0, totalScore: 0 },
          { name: '填空',questionType: 'fillblank', questionCount: 0, scorePerQuestion: 0, totalScore: 0 },
          { name: '简答',questionType: 'shortanswer', questionCount: 0, scorePerQuestion: 0, totalScore: 0 },
        ];
        state.questionConfigForm.totalQuestionScore = 0;
      }
      calculateTotalScore();
    }
    for (let key in state.interfaceRes) {
      if (key != model) {
        let faceReList = state.interfaceRes[key];
        for (let i = 0; i < faceReList.length; i++) {
          let relationField = faceReList[i].relationField;
          if (relationField) {
            let modelAll = relationField.split('-');
            let faceMode = '';
            let faceMode2 = modelAll.length == 2 ? modelAll[0].substring(0, modelAll[0].length - 4) + modelAll[1] : '';
            for (let i = 0; i < modelAll.length; i++) {
              faceMode += modelAll[i];
            }
            if (faceMode == model || faceMode2 == model) {
              let options = 'get' + key + 'Options';
              eval(options)(true);
              changeData(key, index);
            }
          }
        }
      }
    }
  }
  
  function getAllSelectOptions() {
    // 获取计划下拉选项
    state.optionsObj.semesterIdFieldNames = { label: 'name', value: 'id' };
    getSemesterForSelect({
      dataType: 1,
    }).then(res => {
      state.optionsObj.semesterIdOptions = res.data.list;
    });

    // 获取课程下拉选项
    state.optionsObj.courseIdFieldNames = { label: 'name', value: 'id' };
    getCourseForSelect({
      dataType: 1,
    }).then(res => {
      state.optionsObj.courseIdOptions = res.data.list;
    });
  }

  function handlePaperTypeChange(value) {
    if (value === 'random') {
      state.questionConfigForm.questionTypes = [];
      state.questionConfigForm.totalQuestionScore = 0;
      state.questionConfigForm.fixedTotalScore = 0;
      state.questionConfigForm.selectedQuestions = [];
    } else if (value === 'fixed') {
      state.questionConfigForm.questionTypes = [];
      state.questionConfigForm.totalQuestionScore = 0;
      state.questionConfigForm.fixedTotalScore = 0;
      state.questionConfigForm.selectedQuestions = [];
    }
  }

  function calculateTotalScore() {
    if (state.dataForm.paperMode === '随机') {
      let totalScore = 0;
      state.questionConfigForm.questionTypes.forEach(type => {
        type.totalScore = (type.questionCount || 0) * (type.scorePerQuestion || 0);
        totalScore += type.totalScore;
      });
      state.questionConfigForm.totalQuestionScore = totalScore;
    } else if (state.dataForm.paperMode === '固定') {
      let totalScore = 0;
      state.questionConfigForm.selectedQuestions.forEach(question => {
        totalScore += question.score || 0;
      });
      state.questionConfigForm.fixedTotalScore = totalScore;
    }
  }

  function openQuestionSelector() {
    // 实现打开题目选择器的逻辑
  }

  function getQuestionConfigForSubmit() {
    // 只合并题目配置，不再有paperType等多余字段
    if (state.dataForm.paperMode === '随机') {
      return { questionTypes: state.questionConfigForm.questionTypes };
    } else if (state.dataForm.paperMode === '固定') {
      return { selectedQuestions: state.questionConfigForm.selectedQuestions };
    }
    return {};
  }
</script>

<style lang="less" scoped>
.steps-container {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 16px;
}

.step-content {
  min-height: 300px;
}

.publish-actions {
  margin-top: 20px;
  text-align: right;
}
.form-item-help {
  color: #999;
  font-size: 12px;
  line-height: 1.5;
  margin-top: 2px;
}

.input-with-unit {
  position: relative;
}

.unit-text {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  font-size: 12px;
}

.config-section {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background-color: #fafafa;
}

.section-title {
  margin-bottom: 16px;
  font-weight: 600;
  color: #262626;
  font-size: 14px;
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 8px;
}

.submit-limit-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.limit-text {
  margin-left: 8px;
  color: #666;
}

.checkbox-item {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.checkbox-item:last-child {
  border-bottom: none;
}

.leave-page-control {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  padding: 12px 0;
}

.control-label {
  font-weight: 500;
  color: #262626;
}

.control-text {
  color: #666;
}

.question-config-table {
  margin-top: 16px;
}

.question-config-table .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
  text-align: center;
}

.question-config-table .ant-table-tbody > tr > td {
  text-align: center;
}

.total-score {
  font-weight: 600;
  color: #1890ff;
}

.total-summary {
  margin-top: 16px;
  text-align: right;
  padding: 12px;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
}

.summary-label {
  font-weight: 500;
  color: #262626;
}

.summary-value {
  margin-left: 8px;
  margin-right: 8px;
  color: #52c41a;
  font-weight: 600;
  font-size: 16px;
}

.summary-unit {
  color: #666;
}

.fixed-question-config {
  margin-top: 16px;
}

.selected-questions-summary {
  margin-top: 16px;
  display: flex;
  gap: 24px;
}

.summary-item {
  display: flex;
  align-items: center;
}

.item-label {
  font-weight: 500;
  color: #262626;
}

.item-value {
  margin-left: 8px;
  margin-right: 8px;
  color: #1890ff;
  font-weight: 600;
}

.item-unit {
  color: #666;
}
</style>
