<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    width="600px"
    :minHeight="100"
    :cancelText="t('common.cancelText', '取消')"
    :okText="t('common.okText', '确定')"
    @ok="handleSubmit"
    :closeFunc="onClose">
    <template #title>
      <a-space :size="10">
        <div class="text-16px font-medium">{{ title }}</div>
        <a-space-compact size="small" block v-if="dataForm.id">
          <a-tooltip :title="t('common.prevRecord')">
            <a-button size="small" :disabled="getPrevDisabled" @click="handlePrev">
              <i class="icon-ym icon-ym-caret-left text-10px"></i>
            </a-button>
          </a-tooltip>
          <a-tooltip :title="t('common.nextRecord')">
            <a-button size="small" :disabled="getNextDisabled" @click="handleNext">
              <i class="icon-ym icon-ym-caret-right text-10px"></i>
            </a-button>
          </a-tooltip>
        </a-space-compact>
      </a-space>
    </template>
    <template #insertFooter>
      <div class="loat-left mt-5px">
        <XundaCheckboxSingle v-model:value="submitType" :label="continueText" />
      </div>
    </template>
    <a-row class="dynamic-form">
      <a-form
        :colon="false"
        size="middle"
        layout="horizontal"
        labelAlign="left"
        :labelCol="{ style: { width: '100px' } }"
        :model="dataForm"
        :rules="dataRule"
        ref="formRef">
        <a-row :gutter="15">
          <!-- 具体表单 -->
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="registrationId">
              <template #label>学员 </template>
              <XundaSelect
                v-model:value="dataForm.registrationId"
                :disabled="false"
                @change="changeData('registrationId', -1)"
                placeholder="请选择学员"
                :allowClear="true"
                :style="{ width: '100%' }"
                showSearch
                :options="optionsObj.registrationIdOptions"
                :fieldNames="optionsObj.registrationIdFieldNames">
              </XundaSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="name">
              <template #label>费用名 </template>
              <XundaInput
                v-model:value="dataForm.name"
                :disabled="false"
                @change="changeData('name', -1)"
                placeholder="请输入费用名"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="currency">
              <template #label>实收币种 </template>
              <XundaInput
                v-model:value="dataForm.currency"
                :disabled="false"
                @change="changeData('currency', -1)"
                placeholder="请输入实收币种"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="originalPrice">
              <template #label>原价 </template>
              <XundaInputNumber
                v-model:value="dataForm.originalPrice"
                :disabled="false"
                @change="changeData('originalPrice', -1)"
                placeholder="请输入原价"
                :allowClear="true"
                :style="{ width: '100%' }"
                :precision="2"
                :controls="true">
              </XundaInputNumber>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="realPrice">
              <template #label>实价 </template>
              <XundaInputNumber
                v-model:value="dataForm.realPrice"
                :disabled="false"
                @change="changeData('realPrice', -1)"
                placeholder="请输入实价"
                :allowClear="true"
                :style="{ width: '100%' }"
                :precision="2"
                :controls="true">
              </XundaInputNumber>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="homeCurrencyPrice">
              <template #label>本币金额 </template>
              <XundaInputNumber
                v-model:value="dataForm.homeCurrencyPrice"
                :disabled="false"
                @change="changeData('homeCurrencyPrice', -1)"
                placeholder="本币金额"
                :allowClear="true"
                :style="{ width: '100%' }"
                :precision="2"
                :controls="true">
              </XundaInputNumber>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="homeCurrency">
              <template #label>本币 </template>
              <XundaInput
                v-model:value="dataForm.homeCurrency"
                :disabled="false"
                @change="changeData('homeCurrency', -1)"
                placeholder="本币"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="rate">
              <template #label>汇率 </template>
              <XundaInputNumber
                v-model:value="dataForm.rate"
                :disabled="false"
                @change="changeData('rate', -1)"
                placeholder="请输入汇率"
                :allowClear="true"
                :style="{ width: '100%' }"
                :precision="2"
                :controls="true">
              </XundaInputNumber>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="payState">
              <template #label>支付状态 </template>
              <XundaSelect
                v-model:value="dataForm.payState"
                :disabled="false"
                @change="changeData('payState', -1)"
                placeholder="请选择支付状态"
                :allowClear="true"
                :style="{ width: '100%' }"
                showSearch
                :options="optionsObj.payStateOptions"
                :fieldNames="optionsObj.payStateFieldNames">
              </XundaSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="payTime">
              <template #label>支付时间 </template>
              <XundaDatePicker
                v-model:value="dataForm.payTime"
                :disabled="false"
                @change="changeData('payTime', -1)"
                placeholder="请选择支付时间"
                :allowClear="true"
                :style="{ width: '100%' }"
                format="yyyy-MM-dd HH:mm:ss">
              </XundaDatePicker>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="payType">
              <template #label>支付类型 </template>
              <XundaSelect
                v-model:value="dataForm.payType"
                :disabled="false"
                @change="changeData('payType', -1)"
                placeholder="请选择支付类型"
                :allowClear="true"
                :style="{ width: '100%' }"
                showSearch
                :options="optionsObj.payTypeOptions"
                :fieldNames="optionsObj.payTypeFieldNames">
              </XundaSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="transcationId">
              <template #label>交易记录 </template>
              <XundaInput
                v-model:value="dataForm.transcationId"
                :disabled="false"
                @change="changeData('transcationId', -1)"
                placeholder="请输入交易记录"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="description">
              <template #label>说明 </template>
              <XundaInput
                v-model:value="dataForm.description"
                :disabled="false"
                @change="changeData('description', -1)"
                placeholder="说明"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="note">
              <template #label>备注 </template>
              <XundaInput
                v-model:value="dataForm.note"
                :disabled="false"
                @change="changeData('note', -1)"
                placeholder="备注"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="attachment">
              <template #label>附件 </template>
              <XundaInput
                v-model:value="dataForm.attachment"
                :disabled="false"
                @change="changeData('attachment', -1)"
                placeholder="附件"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="promotionCodeCurrency">
              <template #label>优惠码币种 </template>
              <XundaSelect
                v-model:value="dataForm.promotionCodeCurrency"
                :disabled="false"
                @change="changeData('promotionCodeCurrency', -1)"
                placeholder="请选择优惠码币种"
                :allowClear="true"
                :style="{ width: '100%' }"
                showSearch
                :options="optionsObj.promotionCodeCurrencyOptions"
                :fieldNames="optionsObj.promotionCodeCurrencyFieldNames">
              </XundaSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="promotionCode">
              <template #label>优惠码 </template>
              <XundaInput
                v-model:value="dataForm.promotionCode"
                :disabled="false"
                @change="changeData('promotionCode', -1)"
                placeholder="请输入优惠码"
                :allowClear="true"
                :style="{ width: '100%' }">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" :hidden="false">
            <a-form-item name="promotionMoney">
              <template #label>优惠金额 </template>
              <XundaInputNumber
                v-model:value="dataForm.promotionMoney"
                :disabled="false"
                @change="changeData('promotionMoney', -1)"
                placeholder="优惠金额"
                :allowClear="true"
                :style="{ width: '100%' }"
                :precision="2"
                :controls="true">
              </XundaInputNumber>
            </a-form-item>
          </a-col>
          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { create, update, getInfo } from '@/views/study/paymentRecord';
  import { reactive, toRefs, nextTick, ref, unref, computed, inject } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useUserStore } from '@/store/modules/user';
  import type { FormInstance } from 'ant-design-vue';
  import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
  // 表单权限
  import { usePermission } from '@/hooks/web/usePermission';
  interface State {
    dataForm: any;
    tableRows: any;
    dataRule: any;
    optionsObj: any;
    childIndex: any;
    isEdit: any;
    interfaceRes: any;
    //可选范围默认值
    ableAll: any;
    //掩码配置
    maskConfig: any;
    //定位属性
    locationScope: any;
    title: string;
    continueText: string;
    allList: any[];
    currIndex: number;
    isContinue: boolean;
    submitType: number;
    showContinueBtn: boolean;
  }

  const emit = defineEmits(['reload']);
  const getLeftTreeActiveInfo: (() => any) | null = inject('getLeftTreeActiveInfo', null);
  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const [registerModal, { openModal, setModalProps }] = useModal();
  const formRef = ref<FormInstance>();
  const state = reactive<State>({
    dataForm: {
      id: '',
      registrationId: '',
      name: '',
      currency: '',
      originalPrice: undefined,
      realPrice: undefined,
      homeCurrencyPrice: undefined,
      homeCurrency: '',
      rate: undefined,
      payState: '',
      payTime: undefined,
      payType: '',
      transcationId: '',
      description: '',
      note: '',
      attachment: '',
      promotionCodeCurrency: '',
      promotionCode: '',
      promotionMoney: undefined,
    },
    tableRows: {},
    dataRule: {
      id: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '优惠不能为空'),
          trigger: 'blur',
        },
      ],
      registrationId: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '学员不能为空'),
          trigger: 'blur',
        },
      ],
      name: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '费用名不能为空'),
          trigger: 'blur',
        },
      ],
      currency: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '实收币种不能为空'),
          trigger: 'blur',
        },
      ],
      originalPrice: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '原价不能为空'),
          trigger: 'blur',
        },
      ],
      realPrice: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '实价不能为空'),
          trigger: 'blur',
        },
      ],
      rate: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '汇率不能为空'),
          trigger: 'blur',
        },
      ],
      payState: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '支付状态不能为空'),
          trigger: 'blur',
        },
      ],
      payTime: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '支付时间不能为空'),
          trigger: 'blur',
        },
      ],
      payType: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '支付类型不能为空'),
          trigger: 'blur',
        },
      ],
      transcationId: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '交易记录不能为空'),
          trigger: 'blur',
        },
      ],
      promotionCodeCurrency: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '优惠码币种不能为空'),
          trigger: 'blur',
        },
      ],
      promotionCode: [
        {
          required: 'true',
          message: t('sys.validate.textRequiredSuffix', '优惠码不能为空'),
          trigger: 'blur',
        },
      ],
    },
    optionsObj: {
      //选项配置
      defaultProps: { label: 'fullName', value: 'enCode' }, // 默认下拉选择键值
    },
    childIndex: -1,
    isEdit: false,
    interfaceRes: {},
    //可选范围默认值
    ableAll: {},

    //掩码配置
    maskConfig: {},
    //定位属性
    locationScope: {
      faddressDetail: [],
    },
    title: '',
    continueText: '',
    allList: [],
    currIndex: 0,
    isContinue: false,
    submitType: 0,
    showContinueBtn: true,
  });
  const { title, continueText, showContinueBtn, dataRule, dataForm, optionsObj, ableAll, maskConfig, submitType } = toRefs(state);

  const getPrevDisabled = computed(() => state.currIndex === 0);
  const getNextDisabled = computed(() => state.currIndex === state.allList.length - 1);
  // 表单权限
  const { hasFormP } = usePermission();

  defineExpose({ init });

  function init(data) {
    state.submitType = 0;
    state.isContinue = false;
    state.title = !data.id ? t('common.add2Text', '新增') : t('common.editText', '编辑');
    state.continueText = !data.id ? t('common.continueAndAddText', '确定并新增') : t('common.continueText', '确定并继续');
    setFormProps({ continueLoading: false });
    state.dataForm.id = data.id;
    openModal();
    state.allList = data.allList;
    state.currIndex = state.allList.length && data.id ? state.allList.findIndex(item => item.id === data.id) : 0;
    getAllSelectOptions();
    nextTick(() => {
      getForm().resetFields();
      setTimeout(initData, 0);
    });
  }
  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      // 设置默认值
      state.dataForm = {
        id: '',
        registrationId: '',
        name: '',
        currency: '',
        originalPrice: undefined,
        realPrice: undefined,
        homeCurrencyPrice: undefined,
        homeCurrency: '',
        rate: undefined,
        payState: '',
        payTime: undefined,
        payType: '',
        transcationId: '',
        description: '',
        note: '',
        attachment: '',
        promotionCodeCurrency: '',
        promotionCode: '',
        promotionMoney: undefined,
      };

      if (getLeftTreeActiveInfo) state.dataForm = { ...state.dataForm, ...(getLeftTreeActiveInfo() || {}) };
      state.childIndex = -1;
      changeLoading(false);
    }
  }
  function getForm() {
    const form = unref(formRef);
    if (!form) {
      throw new Error('form is null!');
    }
    return form;
  }
  function getData(id) {
    getInfo(id).then(res => {
      state.dataForm = res.data || {};
      state.childIndex = -1;
      changeLoading(false);
    });
  }
  async function handleSubmit(type) {
    try {
      const values = await getForm()?.validate();
      if (!values) return;
      setFormProps({ confirmLoading: true });
      const formMethod = state.dataForm.id ? update : create;
      console.log('values', state.dataForm);
      formMethod(state.dataForm)
        .then(res => {
          createMessage.success(res.msg);
          setFormProps({ confirmLoading: false });
          if (state.submitType == 1) {
            initData();
            state.isContinue = true;
          } else {
            setFormProps({ open: false });
            emit('reload');
          }
        })
        .catch(() => {
          setFormProps({ confirmLoading: false });
        });
    } catch (_) {}
  }

  // 跳转上一个
  function handlePrev() {
    state.currIndex--;
    handleGetNewInfo();
  }

  // 跳转下一个
  function handleNext() {
    state.currIndex++;
    handleGetNewInfo();
  }

  // 重新获取编辑信息
  function handleGetNewInfo() {
    changeLoading(true);
    getForm().resetFields();
    const id = state.allList[state.currIndex].id;
    getData(id);
  }

  function setFormProps(data) {
    setModalProps(data);
  }

  //
  function changeLoading(loading) {
    setModalProps({ loading });
  }

  // 关闭弹窗
  async function onClose() {
    if (state.isContinue) emit('reload');
    return true;
  }

  function changeData(model, index) {
    state.isEdit = false;
    state.childIndex = index;
    for (let key in state.interfaceRes) {
      if (key != model) {
        let faceReList = state.interfaceRes[key];
        for (let i = 0; i < faceReList.length; i++) {
          let relationField = faceReList[i].relationField;
          if (relationField) {
            let modelAll = relationField.split('-');
            let faceMode = '';
            let faceMode2 = modelAll.length == 2 ? modelAll[0].substring(0, modelAll[0].length - 4) + modelAll[1] : '';
            for (let i = 0; i < modelAll.length; i++) {
              faceMode += modelAll[i];
            }
            if (faceMode == model || faceMode2 == model) {
              let options = 'get' + key + 'Options';
              eval(options)(true);
              changeData(key, index);
            }
          }
        }
      }
    }
  }
  function getAllSelectOptions() {}
</script>
