<template>
  <div class="course-container">
    <!-- 左侧目录 -->
    <div class="sidebar">
      <h3>课程目录</h3>
      <ul>
        <li v-for="(chapter, index) in chapters" :key="index">
          <strong>{{ chapter.title }}</strong>
          <ul>
            <li v-for="(section, idx) in chapter.sections" :key="idx">
              <a @click="playVideo(section.videoUrl)">{{ section.title }}</a>
            </li>
          </ul>
        </li>
      </ul>
    </div>

    <!-- 右侧内容区域 -->
    <div class="content">
      <h2>{{ currentSection.title }}</h2>
      <video controls :src="currentSection.videoUrl"></video>
      <div class="controls">
        <button @click="togglePlay">播放/暂停</button>
        <button @click="downloadVideo">下载视频</button>
        <button @click="insertObject">插入对象</button>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref } from 'vue';

  // 课程章节数据
  const chapters = [
    {
      title: '01 初识Java',
      sections: [
        { title: 'Java的发展', videoUrl: 'path/to/video1.mp4' },
        { title: 'Java的特点', videoUrl: 'path/to/video2.mp4' },
        // 更多章节...
      ],
    },
    {
      title: '02 Java语言基础',
      sections: [
        { title: '符号集', videoUrl: 'path/to/video3.mp4' },
        { title: 'Java程序结构', videoUrl: 'path/to/video4.mp4' },
        // 更多章节...
      ],
    },
    // 更多章节...
  ];

  // 当前播放的章节
  const currentSection = ref(chapters[0].sections[0]);

  // 播放视频
  const playVideo = url => {
    currentSection.value = chapters
      .find(chapter => chapter.sections.some(section => section.videoUrl === url))
      .sections.find(section => section.videoUrl === url);
  };

  // 控制按钮功能
  const togglePlay = () => {
    const video = document.querySelector('video');
    if (video.paused) {
      video.play();
    } else {
      video.pause();
    }
  };

  const downloadVideo = () => {
    // 下载视频逻辑
  };

  const insertObject = () => {
    // 插入对象逻辑
  };
</script>

<style scoped>
  .course-container {
    display: flex;
  }

  .sidebar {
    width: 250px;
    padding: 10px;
    border-right: 1px solid #ccc;
  }

  .content {
    flex: 1;
    padding: 20px;
  }

  video {
    width: 100%;
    height: auto;
  }

  .controls {
    margin-top: 10px;
  }
</style>
