import { IMenuButtonItem, IMenuSelectorItem, MenuItemType } from '@univerjs/ui';
import {
  XundaSheetsInsertFloatBarEchartOperation,
  XundaSheetsInsertFloatLineEchartOperation,
  XundaSheetsInsertFloatPieEchartOperation,
  XundaSheetsInsertFloatRadarEchartOperation,
} from '../../commands/operations/sheet-float-echart.operation';
import { XundaCommandIds } from '../../utils/define';

export const XundaSheetsFloatEchartMenuFactory = (): IMenuSelectorItem => {
  return {
    id: XundaCommandIds.floatEchartOperations,
    type: MenuItemType.SUBITEMS,
    icon: 'SystemSingle',
    tooltip: 'xundaSheetFloatEchartMenu.tooltip',
  };
};

export const XundaSheetsInsertFloatBarEchartMenuFactory = (): IMenuButtonItem => {
  return {
    id: XundaSheetsInsertFloatBarEchartOperation.id,
    type: MenuItemType.BUTTON,
    icon: 'ChartSingle',
    tooltip: 'xundaSheetInsertFloatBarEchartMenu.tooltip',
    title: 'xundaSheetInsertFloatBarEchartMenu.title',
  };
};

export const XundaSheetsInsertFloatLineEchartMenuFactory = (): IMenuButtonItem => {
  return {
    id: XundaSheetsInsertFloatLineEchartOperation.id,
    type: MenuItemType.BUTTON,
    icon: 'LineChartSingle',
    tooltip: 'xundaSheetInsertFloatLineEchartMenu.tooltip',
    title: 'xundaSheetInsertFloatLineEchartMenu.title',
  };
};

export const XundaSheetsInsertFloatPieEchartMenuFactory = (): IMenuButtonItem => {
  return {
    id: XundaSheetsInsertFloatPieEchartOperation.id,
    type: MenuItemType.BUTTON,
    icon: 'PieChartSingle',
    tooltip: 'xundaSheetInsertFloatPieEchartMenu.tooltip',
    title: 'xundaSheetInsertFloatPieEchartMenu.title',
  };
};

export const XundaSheetsInsertFloatRadarEchartMenuFactory = (): IMenuButtonItem => {
  return {
    id: XundaSheetsInsertFloatRadarEchartOperation.id,
    type: MenuItemType.BUTTON,
    icon: 'RadarChartSingle',
    tooltip: 'xundaSheetInsertFloatRadarEchartMenu.tooltip',
    title: 'xundaSheetInsertFloatRadarEchartMenu.title',
  };
};
