<template>
  <div class="course-config-container">
    <div class="course-config-content">
      <a-card class="table-card" :bordered="false" :bodyStyle="{ padding: '20px' }">
        <BasicTable @register="registerTable" ref="tableRef">
          <template #tableTitle>
            <a-button type="primary" ghost @click="handleTemplateImport" :disabled="disabled"> 从课程模板导入 </a-button>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="HandleAdd()" v-if="!disabled"> {{ t('common.add2Text', '新增') }}</a-button>
            <a-button type="link" preIcon="icon-ym icon-ym-btn-clearn" @click="handelBatchRemove()" v-if="!disabled">
              {{ t('common.batchDelText', '批量删除') }}</a-button
            >
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'feeType'">
              <xunda-input v-model:value="record['feeType']" :useMask="column.useMask" :maskConfig="column.maskConfig" :showOverflow="true" detailed />
            </template>
            <template v-if="column.dataIndex === 'currency'">
              <xunda-input v-model:value="record['currency']" :useMask="column.useMask" :maskConfig="column.maskConfig" :showOverflow="true" detailed />
            </template>
            <template v-if="column.key === 'action'">
              <TableAction
                :actions="[
                  {
                    label: t('common.editText', '编辑'),
                    onClick: handleEdit.bind(null, record),
                    ifShow: !props.disabled,
                  },
                  {
                    label: t('common.delText', '删除'),
                    color: 'error',
                    ifShow: !props.disabled,
                    modelConfirm: {
                      title: '确认删除',
                      content: '删除后数据将无法恢复，是否继续？',
                      onOk: handleDelete.bind(null, record.id),
                    },
                  },
                  {
                    label: t('common.detailText', '详情'),
                    onClick: HandleDetail.bind(null, record),
                  },
                ]" />
            </template>
          </template>
        </BasicTable>
      </a-card>
    </div>
    <Form ref="formRef" @reload="reload" />
    <Detail ref="detailRef" @reload="reload" />
    <ExportModal @register="registerExportModal" @download="handleExport" />
    <ImportModal @register="registerImportModal" @reload="reload" />
    <CourseTemplateModal ref="templateRef" @reload="reload" :semester-id="semesterId" />
  </div>
</template>

<script lang="ts" setup>
  import { getList, batchDelete, exportData, columns } from './index';
  import { ref, reactive, computed } from 'vue';
  import { useModal } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useForm } from '@/components/Form';
  import { BasicTable, useTable, TableAction, TableActionType } from '@/components/Table';
  import Form from './form.vue';
  import Detail from './detail.vue';
  import { useRoute } from 'vue-router';
  import { cloneDeep } from 'lodash-es';
  import { ExportModal } from '@/components/CommonModal';
  import { downloadByUrl } from '@/utils/file/download';
  import { ImportModal } from '@/components/CommonModal';
  import { useUserStore } from '@/store/modules/user';
  import { usePermission } from '@/hooks/web/usePermission';
  import CourseTemplateModal from './CourseTemplateModal.vue';
  defineOptions({ name: 'FeeConfig' });
  const emit = defineEmits(['handleStep']);
  const props = defineProps({
    semesterId: {
      type: String,
      required: true,
    },
    disabled: { type: Boolean, default: false },
  });

  const courseFile = ref<any>(null);

  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;
  const { hasFormP } = usePermission();
  const route = useRoute();
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const [registerExportModal, { openModal: openExportModal, closeModal: closeExportModal, setModalProps: setExportModalProps }] = useModal();
  const [registerImportModal, { openModal: openImportModal }] = useModal();
  const formRef = ref<any>(null);
  const tableRef = ref<Nullable<TableActionType>>(null);
  const detailRef = ref<any>(null);
  const templateRef = ref<any>(null);
  const cacheList = ref<any>([]);
  const defaultSearchInfo = {
    menuId: route.meta.modelId as string,
    moduleId: '***********', //模块ID
    superQueryJson: '',
    dataType: 0,
  };
  const exportColumnList = computed(() => {
    return columns.map(o => {
      return {
        id: o.dataIndex,
        key: o.dataIndex,
        value: o.dataIndex,
        fullName: o.title,
        __config__: {
          xundakey: 'text',
        },
      };
    });
  });
  const searchInfo = reactive({
    ...cloneDeep(defaultSearchInfo),
  });
  const [
    registerSearchForm,
    { submit, setFieldsValue, resetFields, getFieldsValue, clearValidate, updateSchema, removeSchemaByField, appendSchemaByField, validateFields },
  ] = useForm({
    baseColProps: { span: 6 },
    showActionButtonGroup: true,
    showAdvancedButton: true,
    compact: true,
  });
  const [registerTable, { reload, getDataSource, setLoading, getFetchParams, redoHeight, getSelectRowKeys, clearSelectedRowKeys }] = useTable({
    api: getList,
    columns: columns,
    immediate: false,
    searchInfo: searchInfo,
    beforeFetch: params => {
      params.semesterId = props.semesterId;
      return params;
    },
    clickToRowSelect: false,
    afterFetch: data => {
      const list = data.map(o => ({
        ...o,
      }));
      cacheList.value = cloneDeep(list);
      return list;
    },
    actionColumn: {
      width: 200,
      title: t('common.actionText', '操作'),
      dataIndex: 'action',
      fixed: 'right',
      ifShow: !props.disabled,
    },
    rowSelection: props.disabled
      ? undefined
      : {
          type: 'checkbox',
          getCheckboxProps: record => ({
            disabled: record.top,
          }),
        },
  });

  // 编辑
  function handleEdit(record) {
    // 不带工作流
    const data = {
      id: record.id,
      menuId: searchInfo.menuId,
      allList: cacheList.value,
    };
    formRef.value?.init(data);
  }

  // 删除
  function handleDelete(id) {
    const query = { ids: [id] };
    batchDelete(query).then(res => {
      createMessage.success(res.msg);
      clearSelectedRowKeys();
      reload();
    });
  }

  // 新增
  function HandleAdd() {
    // 不带流程新增
    const data = {
      id: '',
      menuId: searchInfo.menuId,
      allList: cacheList.value,
      semesterId: props.semesterId,
    };
    formRef.value?.init(data);
  }
  // 查看详情
  function HandleDetail(record) {
    // 不带流程
    const data = {
      id: record.id,
    };
    detailRef.value?.init(data);
  }
  // 导出
  function handleExport(data) {
    let query = { ...getFetchParams(), ...data };
    exportData(query)
      .then(res => {
        setExportModalProps({ confirmLoading: false });
        if (!res.data.url) return;
        downloadByUrl({ url: res.data.url });
        closeExportModal();
      })
      .catch(() => {
        setExportModalProps({ confirmLoading: false });
      });
  }

  // 批量删除
  function handelBatchRemove() {
    const ids = getSelectRowKeys();
    if (!ids.length) return createMessage.error('请选择一条数据');
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要删除这些数据吗, 是否继续?',
      onOk: () => {
        const query = { ids: ids };
        setLoading(true);
        batchDelete(query).then(res => {
          createMessage.success(res.msg);
          clearSelectedRowKeys();
          reload();
        });
      },
    });
  }

  // 打开模板导入弹窗
  function handleTemplateImport() {
    const data = {
      semesterId: props.semesterId,
    };
    templateRef.value?.init(data);
  }

  function handleSubmit(type) {
    if (type === 'next') {
      const dataSource = getDataSource();
      const list = dataSource.map(o => ({
        ...o,
      }));
      if (list.length === 0) {
        createMessage.error('请配置课程信息');
        return;
      }
    }
    emit('handleStep', type);
  }

  defineExpose({
    initData: () => reload(),
    handleSubmit,
  });
</script>

<style lang="less" scoped>
  .course-config-container {
    width: 100%;

    .course-config-content {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .upload-card {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

        .course-upload-item {
          margin-bottom: 0;

          .form-label {
            font-weight: 500;
            color: #262626;
          }

          .upload-wrapper {
            .button-group {
              display: flex;
              gap: 12px;
              align-items: center;
            }

            .upload-tip {
              margin-top: 8px;
              color: #8c8c8c;
              font-size: 12px;
            }
          }
        }
      }

      .table-card {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

        .table-operations {
          display: flex;
          gap: 12px;

          .action-button {
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            height: 34px;
            border-radius: 6px;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }

            &.add-button {
              background: var(--ant-primary-color);
              color: #fff;

              &:hover {
                background: var(--ant-primary-5);
              }
            }

            &.delete-button {
              &:hover {
                color: var(--ant-error-color);
                border-color: var(--ant-error-color);
              }
            }
          }
        }
      }
    }
  }
</style>
