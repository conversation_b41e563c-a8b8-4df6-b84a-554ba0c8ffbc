{"common": {"okText": "OK", "continueText": "OK & Continue", "continueAndAddText": "OK & Add", "closeText": "Close", "cancelText": "Cancel", "loadingText": "Loading...", "saveText": "Save", "delText": "Delete", "resetText": "Reset", "searchText": "Search", "queryText": "Search", "addText": "Add", "add1Text": "Add", "add2Text": "Add", "editText": "Edit", "detailText": "Detail", "moreText": "More", "exportText": "Export", "importText": "Import", "copyText": "Copy", "printText": "Print", "batchPrintText": "Batch Print", "batchDelText": "<PERSON><PERSON> Delete", "previewText": "Preview", "submitText": "Submit", "syncText": "Sync", "cleanText": "Clean Up", "closeList": "Close List", "inputText": "Please enter", "chooseText": "Please select", "inputTextPrefix": "Please enter ", "chooseTextPrefix": "Please select ", "redo": "Refresh", "back": "Back", "expandAll": "Expand All", "collapseAll": "Collapse All", "superQuery": "Super Query", "light": "Light", "dark": "Dark", "tipTitle": "Tips", "delTip": "This operation will permanently delete the data. Do you want to continue?", "batchDelTip": "Are you sure you want to delete these data? Do you want to continue?", "selectDataTip": "Please select a piece of data", "prev": "Prev", "next": "Next", "prevRecord": "Prev", "nextRecord": "Next", "keyword": "Keyword", "enterKeyword": "Please Enter", "leftTreeSearchText": "Enter Keyword", "drawerSearchText": "Please Enter Keyword", "inputPlaceholder": "Please enter", "selectPlaceholder": "Please select", "selectI18nCode": "Select translation markers", "undoText": "undo", "redoText": "redo", "noData": "No Data", "updateView": "Update View", "addView": "Add View"}, "component": {"app": {"searchNotData": "No search results yet", "toSearch": "to search", "toNavigate": "to navigate"}, "countdown": {"normalText": "Get SMS code", "sendText": "Reacquire in {0}s"}, "cropper": {"selectImage": "Select Image", "uploadSuccess": "Uploaded success!", "modalTitle": "Avatar upload", "okText": "Confirm and upload", "btn_reset": "Reset", "btn_rotate_left": "Counterclockwise rotation", "btn_rotate_right": "Clockwise rotation", "btn_scale_x": "Flip horizontal", "btn_scale_y": "Flip vertical", "btn_zoom_in": "Zoom in", "btn_zoom_out": "Zoom out", "preview": "<PERSON><PERSON><PERSON>"}, "drawer": {"loadingText": "Loading...", "cancelText": "Close", "okText": "Confirm"}, "excel": {"exportModalTitle": "Export data", "fileType": "File type", "fileName": "File name"}, "form": {"fold": "Fold", "unfold": "Unfold", "maxTip": "The number of characters should be less than {0}", "apiSelectNotFound": "Wait for data loading to complete..."}, "icon": {"placeholder": "Click the select icon", "search": "Search icon", "copy": "Copy icon successfully!"}, "menu": {"search": "Menu search"}, "modal": {"cancelText": "Close", "okText": "Confirm", "close": "Close", "maximize": "Maximize", "restore": "Rest<PERSON>"}, "table": {"settingDens": "Density", "settingDensDefault": "<PERSON><PERSON><PERSON>", "settingDensMiddle": "Middle", "settingDensSmall": "Compact", "viewList": "View", "viewSetting": "View settings", "settingColumn": "Column settings", "settingColumnShow": "Column display", "settingIndexColumnShow": "Index Column", "settingSelectColumnShow": "Selection Column", "settingFixedLeft": "Fixed Left", "settingFixedRight": "Fixed Right", "settingFullScreen": "Full Screen", "index": "No.", "status": "Status", "action": "Action", "summary": "Total", "total": "total of {total}"}, "time": {"before": " ago", "after": " after", "just": "just now", "seconds": " seconds", "minutes": " minutes", "hours": " hours", "days": " days"}, "tree": {"reload": "Reload", "selectAll": "Select All", "unSelectAll": "Cancel Select", "expandAll": "Expand All", "unExpandAll": "Collapse all", "checkStrictly": "Hierarchical association", "checkUnStrictly": "Hierarchical independence"}, "upload": {"save": "Save", "upload": "Upload", "buttonText": "Upload", "imgUpload": "ImageUpload", "imgDragger": "Click or drag image to this area to upload", "uploaded": "Uploaded", "operating": "Operating", "del": "Delete", "download": "Download", "downloadAll": "Download all", "saveWarn": "Please wait for the file to upload and save!", "saveError": "There is no file successfully uploaded and cannot be saved!", "preview": "Preview", "choose": "Select the file", "accept": "Support {0} format", "acceptUpload": "Only upload files in {0} format", "maxSize": "A single file does not exceed {0}MB ", "maxSizeMultiple": "Only upload files up to {0}MB!", "maxNumber": "Only upload up to {0} files", "legend": "Legend", "fileName": "File name", "fileSize": "File size", "fileStatue": "File status", "startUpload": "Start upload", "uploadSuccess": "Upload successfully", "uploadError": "Upload failed", "uploading": "Uploading", "paused": "Paused", "waiting": "Waiting", "checking": "Checking", "uploadWait": "Please wait for the file upload to finish", "reUploadFailed": "Re-upload failed files", "uploadImg": "Please upload Image", "viewImage": "View Image", "view": "View", "imageMaxNumber": "Up to {0} images can be uploaded", "imageMaxSize": "Image size exceeds {size}{unit}", "fileMaxNumber": "Up to {0} files can be uploaded", "fileMaxSize": "File size exceeds {size}{unit}", "fileTypeCheck": "Please select a file of {0} type", "fileReadError": "File {0} reading error, please check the file", "videoNoPreview": "Audio and video files cannot be previewed", "zipNoPreview": "Compressed package cannot be previewed", "image": "image", "video": "video", "audio": "audio"}, "verify": {"error": "verification failed！", "time": "The verification is successful and it takes {time} seconds！", "redoTip": "Click the picture to refresh", "dragText": "Hold down the slider and drag", "successText": "Verified"}, "xunda": {"common": {"allData": "All data", "selected": "Selected", "clearAll": "Clear all", "autoGenerate": "Automatically generated by the system"}, "areaSelect": {"modalTitle": "Select area"}, "calculate": {"storage": "The data will also be saved and stored in the database", "unStorage": "The data will not be saved"}, "dateRange": {"startPlaceholder": "Start date", "endPlaceholder": "End date"}, "timeRange": {"startPlaceholder": "Start time", "endPlaceholder": "End time"}, "iconPicker": {"select": "Select", "modalTitle": "Select icon", "searchPlaceholder": "Please Enter Keyword", "ymIcon": "ymIcon icon", "ymCustom": "ymCustom icon"}, "location": {"modalTitle": "Select position", "searchPlaceholder": "Enter or click to select on the map", "relocation": "Relocation", "location": "Location"}, "numberRange": {"min": "Min", "max": "Max"}, "organizeSelect": {"modalTitle": "Select organize"}, "depSelect": {"modalTitle": "Select department"}, "posSelect": {"modalTitle": "Select position"}, "roleSelect": {"modalTitle": "Select role"}, "groupSelect": {"modalTitle": "Select group"}, "userSelect": {"modalTitle": "Select user"}, "popupAttr": {"storage": "The data will also be saved and stored in the database", "unStorage": "The data will not be saved"}, "popupSelect": {"modalTitle": "Select data"}, "relationFormAttr": {"storage": "The data will also be saved and stored in the database", "unStorage": "The data will not be saved"}, "sign": {"signTip": "signature", "signPlaceholder": "Please signature", "operateTip": "Please use the mouse to handwrite your signature in this area"}}}, "layout": {"footer": {"onlinePreview": "Preview", "onlineDocument": "Document"}, "header": {"dropdownItemDoc": "Document", "dropdownItemLoginOut": "Login Out", "systemChange": "Toggle App", "standingChange": "Toggle Standing", "profile": "Profile", "feedback": "<PERSON><PERSON><PERSON>", "about": "About", "statement": "Statement", "commonMenus": "Common Menus", "tooltipErrorLog": "Error log", "tooltipLock": "Lock screen", "tooltipNotify": "Notification", "tooltipChat": "Cha<PERSON>", "tooltipEntryFull": "Full Screen", "tooltipExitFull": "Exit Full Screen", "setting": "Setting", "lockScreenPassword": "Lock screen password", "lockScreen": "Lock screen", "lockScreenBtn": "Locking", "home": "Home"}, "multipleTab": {"setCommon": "Set Common", "reload": "Refresh current", "close": "Close current", "closeLeft": "Close Left", "closeRight": "Close Right", "closeOther": "Close Other", "closeAll": "Close All"}, "setting": {"contentModeFull": "Full", "contentModeFixed": "Fixed width", "topMenuAlignLeft": "Left", "topMenuAlignRight": "Center", "topMenuAlignCenter": "Right", "menuTriggerNone": "Not Show", "menuTriggerBottom": "Bottom", "menuTriggerTop": "Top", "menuTypeSidebar": "Left menu mode", "menuTypeMixSidebar": "Left mixed mode", "menuTypeMix": "Top Mix mode", "menuTypeTopMenu": "Top menu mode", "defaultBg": "<PERSON><PERSON><PERSON>", "blueBg": "Blue", "purpleBg": "Purple", "greenBg": "Green", "on": "On", "off": "Off", "minute": "Minute", "operatingTitle": "Successful!", "operatingContent": "The copy is successful, please go to src/settings/projectSetting.ts to modify the configuration!", "resetSuccess": "Successfully reset!", "copyBtn": "Copy", "clearBtn": "Clear cache and to the login page", "drawerTitle": "Configuration", "darkMode": "Dark mode", "navMode": "Navigation mode", "systemBackground": "System background", "interfaceFunction": "Interface function", "interfaceDisplay": "Interface display", "animation": "Animation", "splitMenu": "Split menu", "closeMixSidebarOnChange": "Switch page to close menu", "sysTheme": "System theme", "headerTheme": "Header theme", "sidebarTheme": "Menu theme", "menuDrag": "Drag Sidebar", "menuSearch": "Menu search", "toggleLocale": "Locale Toggle", "menuAccordion": "Sidebar accordion", "menuCollapse": "Collapse menu", "collapseMenuDisplayName": "Collapse menu display name", "topMenuLayout": "Top menu layout", "menuCollapseButton": "<PERSON>u collapse button", "contentMode": "Content area width", "expandedMenuWidth": "Expanded menu width", "breadcrumb": "Breadcrumbs", "breadcrumbIcon": "Breadcrumbs Icon", "tabs": "Tabs", "tabsIcon": "Tabs Icon", "tabDetail": "<PERSON><PERSON>", "tabsQuickBtn": "Tabs quick button", "tabsRedoBtn": "Tabs redo button", "tabsFoldBtn": "Tabs flod button", "sidebar": "Sidebar", "header": "Header", "footer": "Footer", "fullContent": "Full content", "grayMode": "Gray mode", "colorWeak": "Color Weak Mode", "cachePage": "<PERSON><PERSON>", "progress": "Progress", "switchLoading": "Switch Loading", "switchAnimation": "Switch animation", "animationType": "Animation type", "autoScreenLock": "Auto screen lock", "notAutoScreenLock": "Not auto lock", "fixedHeader": "Fixed header", "fixedSideBar": "Fixed Sidebar", "mixSidebarTrigger": "Mixed menu Trigger", "triggerHover": "Hover", "triggerClick": "Click", "mixSidebarFixed": "Fixed expanded menu"}}, "routes": {"basic": {"login": "<PERSON><PERSON>", "errorLogList": "<PERSON><PERSON><PERSON>", "home": "Home", "externalLink": "ExternalLink", "workFlowDetail": "WorkFlow Detail", "emailDetail": "Email Detail", "previewModel": "Model Preview", "dataManage": "Data Management"}, "mainSystem": "MainSystem", "workSystem": "WorkSystem", "onlineDev": "OnlineDev", "onlineDev-webDesign": "FormDesign", "onlineDev-appDesign": "APPDesign", "onlineDev-report": "Report", "onlineDev-dataReport": "DataReport(past)", "onlineDev-dataScreen": "DataScreen", "onlineDev-visualPortal": "VisualPortal", "onlineDev-integration": "Integration", "onlineDev-printDev": "PrintDesign", "generator": "Generator", "generator-webForm": "WebForm", "generator-appForm": "AppForm", "generator-flowForm": "FlowForm", "system": "System", "system-sysConfig": "SysConfig", "system-notice": "Notice", "system-task": "Task", "system-cache": "CacheManage", "system-log": "Log", "system-monitor": "Monitor", "system-icons": "Icons", "system-language": "Language", "system-menu": "<PERSON><PERSON>", "system-area": "Area", "system-billRule": "BillRule", "system-systemTemplate": "SystemTemplate", "system-smsTemplate": "SmsTemplate", "system-messageTemplate": "MessageTemplate", "system-signature": "Signature", "system-kit": "<PERSON>", "systemData": "SystemData", "systemData-dataSource": "DataSource", "systemData-dataModel": "DataModel", "systemData-dataSync": "DataSync", "systemData-dataBackup": "DataBackup", "systemData-dataInterface": "DataInterface", "systemData-interfaceAuth": "InterfaceAuth", "systemData-dictionary": "Dictionary", "systemData-map": "Map", "commonWords": "CommonWords", "weChat": "WeChat", "weChat-mpConfig": "MPConfig", "weChat-mpMenu": "MPMenu", "weChat-mpUser": "MPUser", "weChat-mpMessage": "MPM<PERSON>age", "weChat-mpMaterial": "MPMaterial", "weChat-qyhConfig": "QYHConfig", "weChat-qyDepartment": "QYDepartment", "weChat-qyUser": "QYUser", "weChat-qyMessage": "QYMessage", "permission": "Permission", "permission-grade": "Grade", "permission-organize": "Organize", "permission-department": "Department", "permission-position": "Position", "permission-user": "User", "permission-role": "Role", "permission-group": "Group", "permission-authorize": "AuthGroup", "permission-auth": "Authorize", "permission-userOnline": "UserOnline", "flowEngine": "FlowEngine", "formDesign": "SystemForm", "workFlow": "WorkFlow", "workFlow-form": "Form", "workFlow-flowEngine": "FlowDesign", "workFlow-addFlow": "AddFlow", "workFlow-flowLaunch": "FlowLaunch", "workFlow-flowToSign": "FlowToSign", "workFlow-flowTodo": "FlowTodo", "workFlow-flowDoing": "FlowDoing", "workFlow-flowDone": "FlowDone", "workFlow-flowCirculate": "FlowCirculate", "workFlow-entrust": "Entrust", "workFlow-flowMonitor": "FlowMonitor", "workFlow-schedule": "Schedule", "workFlow-printTemplate": "PrintTemplate", "workFlow-document": "Document", "msgCenter": "MsgCenter", "msgCenter-accountConfig": "AccountConfig", "msgCenter-accountConfig-mail": "Mail", "msgCenter-accountConfig-shortMsg": "ShortMsg", "msgCenter-accountConfig-weCom": "WeCom", "msgCenter-accountConfig-ding": "DingTalk", "msgCenter-accountConfig-webhook": "Webhook", "msgCenter-accountConfig-mp": "MP", "msgCenter-msgTemplate": "MsgTemplate", "msgCenter-sendConfig": "SendConfig", "msgCenter-msgMonitor": "MsgMonitor", "extend-graphDemo": "Graph Demo", "extend-graphDemo-echartsBar": "E-Bar", "extend-graphDemo-echartsPie": "E-Pie", "extend-graphDemo-echartsBarAcross": "E-BarAcross", "extend-graphDemo-echartsGauge": "E-Gauge", "extend-graphDemo-echartsLineArea": "E-LineArea", "extend-graphDemo-echartsScatter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "extend-graphDemo-echartsCandlestick": "E-Candlestick", "extend-graphDemo-echartsLineBar": "E-LineBar", "extend-graphDemo-echartsTree": "E-Tree", "extend-graphDemo-echartsFunnel": "E-Funnel", "extend-graphDemo-highchartsScatter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "extend-graphDemo-highchartsWordcloud": "H-Wordcloud", "extend-graphDemo-highchartsLine": "H-Line", "extend-graphDemo-highchartsArea": "H-Area", "extend-graphDemo-highchartsGauge": "H-Gauge", "extend-graphDemo-highchartsBellcurve": "H-Bellcurve", "extend-graphDemo-highchartsFunnel": "H-Funnel", "extend-graphDemo-highchartsBullet": "H-Bullet", "extend-graphDemo-highchartsColumn": "H-Column", "extend-graphDemo-highchartsPie": "H-Pie", "extend-tableDemo": "Table Demo", "extend-tableDemo-commonTable": "CommonTable", "extend-tableDemo-statisticsTable": "StatisticsTable", "extend-tableDemo-lockTable": "LockTable", "extend-tableDemo-groupingTable": "GroupingTable", "extend-tableDemo-redactTable": "RedactTable", "extend-tableDemo-complexHeader": "ComplexHeader", "extend-tableDemo-mergeTable": "MergeTable", "extend-tableDemo-printTable": "PrintTable", "extend-tableDemo-extension": "Extension", "extend-tableDemo-treeTable": "TreeTable", "extend-tableDemo-postilTable": "PostilTable", "extend-tableDemo-tableTree": "TableTree", "extend-tableDemo-signTable": "SignTable", "extend-formDemo": "Form Demo", "extend-formDemo-verifyForm": "VerifyForm", "extend-formDemo-verifyForm1": "VerifyForm1", "extend-formDemo-fieldForm1": "FieldForm1", "extend-formDemo-fieldForm2": "FieldForm2", "extend-formDemo-fieldForm3": "FieldForm3", "extend-formDemo-fieldForm4": "FieldForm4", "extend-formDemo-fieldForm5": "FieldForm5", "extend-formDemo-fieldForm6": "FieldForm6", "extend": "Examples", "extend-functionDemo": "Function Demo", "extend-portalDemo": "Portal Demo", "extend-orderDemo": "Order Demo", "extend-bigData": "BigData", "extend-importAndExport": "ImportAndExport", "extend-signet": "Signet", "extend-signature": "Signature", "extend-schedule": "Schedule", "extend-email": "Email", "extend-documentPreview": "Document Demo", "extend-barCode": "BarCode", "extend-printData": "PrintData", "extend-map": "Map", "extend-order": "Order", "extend-projectGantt": "ProjectGantt", "moreMenu": "More...", "dataReport": "DataReport Demo(past)", "report": "DataReport Demo", "printDemo": "Print Demo", "lioui": "WorkFlow Demo", "reportBI": "ReportBI Demo"}, "sys": {"api": {"operationFailed": "Operation failed", "errorTip": "<PERSON><PERSON><PERSON>", "errorMessage": "The operation failed, the system is abnormal!", "timeoutMessage": "<PERSON><PERSON> timed out, please log in again!", "apiTimeoutMessage": "The interface request timed out, please refresh the page and try again!", "apiRequestFailed": "The interface request failed, please try again later!", "networkException": "network anomaly", "networkExceptionMsg": "Please check if your network connection is normal! The network is abnormal", "errMsg401": "The user does not have permission (token, user name, password error)!", "errMsg403": "The user is authorized, but access is forbidden!", "errMsg404": "Network request error, the resource was not found!", "errMsg405": "Network request error, request method not allowed!", "errMsg408": "Network request timed out!", "errMsg500": "Server error, please contact the administrator!", "errMsg501": "The network is not implemented!", "errMsg502": "Network Error!", "errMsg503": "The service is unavailable, the server is temporarily overloaded or maintained!", "errMsg504": "Network timeout!", "errMsg505": "The http version does not support the request!"}, "app": {"logoutTip": "Reminder", "logoutMessage": "Confirm to exit the system?", "menuLoading": "Menu loading..."}, "validate": {"textRequiredSuffix": " cannot be empty", "arrayRequiredPrefix": "Please select at least one ", "number": "Please enter the correct number", "money": "Please enter the correct amount", "telephone": "Please enter the correct telephone number", "mobilePhone": "Please enter the correct mobile phone number", "phone": "Please enter the correct phone number", "email": "Please enter the correct email address", "url": "Please enter the correct website address", "date": "Please enter the correct date", "idCard": "Please enter the correct ID number"}, "errorLog": {"tableTitle": "Error log list", "tableColumnType": "Type", "tableColumnDate": "Time", "tableColumnFile": "File", "tableColumnMsg": "Error message", "tableColumnStackMsg": "Stack info", "tableActionDesc": "Details", "modalTitle": "Error details", "fireVueError": "Fire vue error", "fireResourceError": "Fire resource error", "fireAjaxError": "Fire ajax error", "enableMessage": "Only effective when useErrorHandle=true in `/src/settings/projectSetting.ts`."}, "exception": {"backLogin": "<PERSON> Login", "backHome": "Back Home", "subTitle403": "Sorry, you don't have access to this page.", "subTitle404": "Sorry, the page you visited does not exist.", "subTitle500": "Sorry, the server is reporting an error.", "noDataTitle": "No data on the current page.", "networkErrorTitle": "Network Error", "networkErrorSubTitle": "Sorry，Your network connection has been disconnected, please check your network!"}, "lock": {"unlock": "Click to unlock", "alert": "Lock screen password error", "backToLogin": "Back to login", "entry": "Enter the system", "placeholder": "Please enter the user password"}, "login": {"welcome": "Welcome", "subTitle": "Login with account password", "subTitle1": "Login with mobile verify code, or switch to ", "subTitle2": "Login with account, or switch to ", "subTitle3": "Login with scan code, or switch to ", "otherLogin": "Other login", "backSignIn": "Back sign in", "signInFormTitle": "Account <PERSON>gin", "mobileSignInFormTitle": "Verify Code Login", "qrSignInFormTitle": "APP <PERSON><PERSON>", "signUpFormTitle": "Sign up", "forgetFormTitle": "Reset password", "qrCodeTip": "Please use the app to scan the code to login. The code will expire after 180 seconds.", "signInTitle": "Backstage management system", "signInDesc": "Enter your personal details and get started!", "policy": "I agree to the xxx Privacy Policy", "scanSign": "scanning the code to complete the login", "loginButton": "<PERSON><PERSON>", "registerButton": "Sign up", "rememberMe": "Remember me", "forgetPassword": "Forget Password?", "otherSignIn": "Sign in with", "lastLoginInfo": "Last login information", "accountPlaceholder": "Please input username", "passwordPlaceholder": "Please input password", "smsPlaceholder": "Please input sms code", "mobilePlaceholder": "Please input mobile", "policyPlaceholder": "Register after checking", "diffPwd": "The two passwords are inconsistent", "confirmPassword": "Confirm Password", "email": "Email", "title": "Account <PERSON>gin", "scanTitle": "APP <PERSON><PERSON>", "codeTitle": "Verify Code Login", "logIn": "<PERSON><PERSON>", "username": "Username", "password": "Password", "version": "V", "upper": "Caps locked", "scanTip": "APP Scan code login", "accountTip": "Please enter the account number", "passwordTip": "Please enter your password", "codeTip": "Please enter your verification code", "changeCode": "Click to switch verification code", "mobile": "Please enter mobile number", "rightMobile": "Please enter the correct mobile number", "smsCode": "Please enter the verification code", "getCode": "Get code", "reSend": "Resend", "company": "Please enter company name", "contacts": "Please enter contact", "rule": "Sub Account: mobile{'@'}account example:***********{'@'}101001", "scanSuccessful": "Scanned", "confirmLogin": "Confirm login on phone", "refreshCode": "Refresh", "recoverCode": "Cancel", "expired": "Qrcode has expired"}}, "formGenerator": {"component": {"input": "Input", "textarea": "Textarea", "inputNumber": "InputNumber", "switch": "Switch", "radio": "Radio", "checkbox": "Checkbox", "select": "Select", "cascader": "<PERSON>r", "datePicker": "DatePicker", "timePicker": "TimePicker", "uploadFile": "UploadFile", "uploadImg": "UploadImage", "colorPicker": "ColorPicker", "rate": "Rate", "slider": "Slide<PERSON>", "editor": "Editor", "link": "Link", "button": "<PERSON><PERSON>", "text": "Text", "alert": "<PERSON><PERSON>", "iframe": "<PERSON><PERSON><PERSON>", "qrcode": "Qrcode", "barcode": "Barcode", "organizeSelect": "OrganizeSelect", "depSelect": "DepartmentSelect", "posSelect": "PositionSelect", "userSelect": "UserSelect", "roleSelect": "RoleSelect", "groupSelect": "GroupSelect", "usersSelect": "UsersSelect", "table": "Table", "treeSelect": "TreeSelect", "popupTableSelect": "PopupTableSelect", "autoComplete": "AutoComplete", "areaSelect": "AreaSelect", "billRule": "BillRule", "relationForm": "RelationForm", "popupSelect": "PopupSelect", "relationFormAttr": "RelationFormAttr", "popupAttr": "PopupAttr", "sign": "Sign", "location": "Location", "calculate": "Calculate", "createUser": "CreateUser", "createTime": "CreateTime", "modifyUser": "ModifyUser", "modifyTime": "ModifyTime", "currOrganize": "CurrentOrganize", "currPosition": "CurrentPosition", "groupTitle": "GroupTitle", "divider": "Divider", "collapse": "Collapse", "tab": "Tab", "row": "Row", "card": "Card", "tableGrid": "TableGrid"}, "delComponentTip": "Delete this component?", "cleanComponentTip": "Clear all components?", "copyComponentTip": "Copy this component?"}, "views": {"http404": {"tips": "Sorry, the page you visited does not exist or you do not have permission to access it!", "subTips": "Please check if the URL you entered is correct, or click the button to return to the homepage.", "goBackBtn": "Go homepage"}, "dynamicModel": {"passwordPlaceholder": "Please enter your password", "scanAndShare": "Scan & Share", "showMore": "Show more", "hideSome": "Hide some"}}, "common.fields": {"id": "Identify", "fCreatorTime": "Created on", "fCreatorUserId": "Creator", "fLastModifyTime": "Last modified on", "fLastModifyUserId": "Last modifier", "fDeleteTime": "Deleted on", "fDeleteUserId": "Deleter", "fDeleteMark": "Is Deleted", "fTenantId": "Tenant", "flowId": "Flow", "flowTaskId": "Flow task"}, "ea": {"teacher": {"fId": "F id", "name": "Name", "nickname": "Nickname", "no": "No", "graduatedAt": "Graduated at", "idCardType": "Id card type", "idCardNo": "Id card no", "workAt": "Work at", "highestEducation": "Highest education", "profession": "Profession", "jobTitle": "Job title", "note": "Note", "extraInfo": "Extra info"}, "student": {"fId": "F id", "no": "No"}}}