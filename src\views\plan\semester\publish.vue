<template>
  <BasicModal
    v-bind="$attrs"
    :continueLoading="currentStep < getStepList.length - 1"
    @register="registerModal"
    :show-ok-btn="false"
    :cancel-text="t('退出')"
    @cancel="afterClose"
    :default-fullscreen="true"
    destroyOnClose>
    <template #title>
      <div class="steps-wrapper">
        <a-steps v-model:current="currentStep" type="navigation" size="small" :key="key">
          <a-step v-for="(item, index) in getStepList" :key="item" :title="item" :disabled="!stepClickFlag" />
        </a-steps>
      </div>
    </template>
    <template #footer>
      <a-space :size="10">
        <a-button type="primary" @click="handleReload" v-if="!isCopy">
          <template #icon><ReloadOutlined /></template>
          {{ t('重置') }}
        </a-button>
        <a-button type="warning" @click="handlePrevFun" :disabled="currentStep <= 0">
          <template #icon><LeftOutlined /></template>
          {{ t('common.prev') }}
        </a-button>
        <a-button type="success" @click="handleSave">
          <template #icon><SaveOutlined /></template>
          {{ t('暂存') }}
        </a-button>
        <a-button type="warning" @click="handleNextFun" :disabled="currentStep >= getStepList.length - 1">
          <template #icon><RightOutlined /></template>
          {{ t('common.next') }}
        </a-button>
        <a-button type="error" @click="handleClose">
          <template #icon><CloseOutlined /></template>
          {{ t('common.closeText') }}
        </a-button>
      </a-space>
    </template>

    <!-- 显示计划名称 -->
    <div>
      <div v-if="semesterName">
        <span>计划名称：</span>
        <span>{{ semesterName }}</span>
      </div>
    </div>

    <!-- 使用动态组件替换原有的条件渲染 -->
    <component
      :is="currentComponent?.component"
      ref="currentFormRef"
      :disabled="publishFlag"
      :category="category"
      v-model:semesterId="semesterId"
      v-model:semesterName="semesterName"
      v-model:publishFlag="publishFlag"
      v-model:isCopy="isCopy"
      :semesterInfo="semesterInfo"
      @changeLoading="changeLoading"
      @handleStep="handleStep"
      @getBasicData="getBasicData"
      @afterPublish="getBasicData">
    </component>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, computed, reactive, toRefs, watch, nextTick } from 'vue';
  import { ReloadOutlined, LeftOutlined, RightOutlined, SaveOutlined, CloseOutlined } from '@ant-design/icons-vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { getInfo, updateSemesterStep } from './index';
  import BasicDataForm from './components/basicDataForm/index.vue';
  import TeacherSelect from './components/teacherSelect/index.vue';
  import NoticeData from './components/noticeData/form.vue';
  import FilingForm from './components/filingForm/index.vue';
  import FeeConfig from './components/feeConfig/index.vue';
  import PolicyConfig from './components/policyConfig/index.vue';
  import TimeConfig from './components/timeConfig/index.vue';
  import CourseConfig from './components/courseConfig/index.vue';
  import ResultPage from './components/result/index.vue';
  import AttachmentConfig from './components/attachmentConfig/index.vue';
  import CourseFile from './components/courseConfig/courseFile.vue';
  import Notice from './components/notice/index.vue';

  // 组件配置映射
  const componentMap = {
    基本信息: {
      component: BasicDataForm,
      span: 14,
      offset: 5,
      overflowAuto: true,
    },
    公告: {
      component: NoticeData,
      span: 14,
      offset: 5,
      overflowAuto: true,
    },
    附件配置: {
      component: AttachmentConfig,
      span: 16,
      offset: 4,
      overflowAuto: true,
    },
    报备表: {
      component: FilingForm,
      span: 16,
      offset: 4,
      overflowAuto: true,
    },
    考评员: {
      component: TeacherSelect,
      span: 22,
      offset: 0,
    },
    原始费用: {
      component: FeeConfig,
      span: 22,
      offset: 1,
    },
    优惠政策: {
      component: PolicyConfig,
      span: 22,
      offset: 1,
    },
    日期: {
      component: TimeConfig,
      span: 22,
      offset: 1,
    },
    课程配置: {
      component: CourseConfig,
      span: 22,
      offset: 1,
    },
    课表附件: {
      component: CourseFile,
      span: 22,
      offset: 1,
    },
    通知配置: {
      component: Notice,
      span: 22,
      offset: 1,
    },
    发布计划: {
      component: ResultPage,
      span: 22,
      offset: 1,
    },
  } as const;

  interface State {
    currentStep: number;
    dataForm: any;
    key: number;
  }

  const { t } = useI18n();
  const state = reactive<State>({
    currentStep: 0,
    dataForm: {
      fullName: '',
      enCode: '',
      category: '',
      type: 2,
      hasPage: 0,
      sortCode: 0,
      enabledMark: 1,
      description: '',
    },
    key: Date.now(),
  });
  const { currentStep, dataForm, key } = toRefs(state);

  const emit = defineEmits(['register', 'reload']);
  const { createMessage, createConfirm } = useMessage();
  const [registerModal, { closeModal, changeLoading }] = useModalInner(init);

  const currentFormRef = ref();
  const semesterId = ref<string>('');
  const semesterName = ref<string>('');
  const category = ref<string>('');
  const publishFlag = ref<boolean>(true);
  const isCopy = ref<boolean>(false);
  const semesterInfo = ref<any>({});

  // 当前显示的组件
  const currentComponent = computed(() => {
    const stepName = getStepList.value[currentStep.value];
    return componentMap[stepName as keyof typeof componentMap];
  });

  function init(data: any) {
    semesterInfo.value = data;
    isCopy.value = data.isCopy;
    initData();
    if (data.id) {
      getBasicData(data.id);
    } else {
      nextTick(() => {
        category.value = data.category;
        currentFormRef.value?.init(data);
      });
    }
  }

  function initData() {
    category.value = '';
    state.currentStep = 0;
    publishFlag.value = false;
    semesterId.value = '';
  }

  const stepClickFlag = computed(() => {
    if (publishFlag.value) return true;
    if (!semesterId.value) return false;
    return false;
  });

  const getStepList = computed(() => {
    state.key = Date.now();
    let base = ['基本信息'];
    if (category.value === '0001') {
      base = [...base, '公告', '报备表', '原始费用', '优惠政策', '日期', '课程配置', '课表附件'];
    }
    if (category.value === '0002') {
      base = [...base, '原始费用', '优惠政策', '日期', '课程配置', '课表附件'];
    }
    if (category.value === '0003' || category.value === '0004') {
      base = [...base, '日期', '附件配置', '原始费用', '优惠政策', '课程配置', '课表附件'];
    }
    return [...base, '通知配置', '发布计划'];
  });

  watch(
    () => state.currentStep,
    newVal => {
      handleStepChange(newVal);
    },
  );

  const isLastStep = computed(() => {
    return state.currentStep === getStepList.value.length - 1;
  });

  // 根据currentStep的值进行初始化
  function handleStepChange(nextStep: number) {
    nextTick(() => {
      if (semesterId.value && !publishFlag.value) {
        updateSemesterStep({ id: semesterId.value, step: getStepList.value[nextStep] }).then(() => {
          getBasicData(semesterId.value);
        });
      }
      if (currentFormRef.value?.init) {
        currentFormRef.value.init({
          id: semesterId.value,
          category: category.value,
        });
      }
      if (currentFormRef.value?.initData) {
        currentFormRef.value.initData();
      }
    });
  }

  // 获取基础数据
  async function getBasicData(id: string) {
    changeLoading(true);
    try {
      const res = await getInfo(id);
      state.dataForm = res.data || {};
      semesterInfo.value = res.data;
      category.value = res.data.category;

      if (isCopy.value) {
        semesterId.value = '';
        publishFlag.value = false;
        const copyData = JSON.parse(JSON.stringify(res.data));
        copyData.id = '';
        copyData.copyId = res.data.id;
        nextTick(() => {
          currentFormRef.value?.setFormData(copyData);
        });
        changeLoading(false);
        return;
      } else {
        semesterId.value = res.data.id;
        semesterName.value = res.data.name;
        publishFlag.value = state.dataForm.publishFlag;
      }

      const step = getStepList.value.findIndex(item => {
        return item === state.dataForm.step;
      });

      const actualStep = step === -1 ? 0 : step;

      if (publishFlag.value) {
        state.currentStep = getStepList.value.length - 1;
      } else {
        state.currentStep = actualStep;
      }

      if (getStepList.value[state.currentStep] === '基本信息') {
        nextTick(() => {
          currentFormRef.value?.setFormData(res.data);
        });
      }
    } catch (error) {
      console.error(error);
    } finally {
      changeLoading(false);
    }
  }

  function handleReload() {
    createConfirm({
      iconType: 'warning',
      title: t('提示'),
      content: t('确定要重置当前步骤吗？'),
      onOk: () => {
        handleStepChange(currentStep.value);
      },
    });
  }

  function handleStep(type: 'next' | 'prev') {
    if (type === 'next') {
      state.currentStep += 1;
    }
    if (type === 'prev') {
      state.currentStep -= 1;
    }
  }

  async function handleSave(type: 'next' | 'prev') {
    if (publishFlag.value) {
      handleStep(type);
      return;
    }

    if (currentFormRef.value?.handleSubmit) {
      await currentFormRef.value.handleSubmit(type);
    } else {
      handleStep(type);
    }
  }

  function handleNextFun() {
    handleSave('next');
  }

  function handlePrevFun() {
    handleSave('prev');
  }

  function handleClose() {
    closeModal();
    emit('reload');
  }

  function afterClose() {
    emit('reload');
  }
</script>
<style lang="less">
  .data-interface-popup {
    .steps {
      overflow: auto;
      .ant-steps-item {
        width: 100px;
      }
    }

    .page-explain {
      cursor: pointer;
      float: right;
      color: @text-color-label;

      &:hover {
        color: @primary-color;
      }
    }

    .config {
      flex: 1;
      padding: 10px;
      display: flex;
      justify-content: space-between;
      overflow: hidden;

      .left-pane {
        flex-shrink: 0;
        width: 350px;
        margin-right: 10px;
        .box {
          margin-top: 8px;
          border-radius: 4px;
          height: calc(100% - 40px);
          border: 1px solid @border-color-base;
          overflow: hidden;

          .search-box {
            padding: 10px;
          }
          & > .scroll-container {
            height: calc(100% - 52px) !important;
          }
          .tree-box {
            overflow: hidden;
            overflow-x: hidden;
          }
        }
      }

      .middle-pane {
        border: 1px solid @border-color-base;
        border-radius: 4px;
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .title {
          border-top: 1px solid @border-color-base;
        }

        .title-box {
          height: 36px;
          line-height: 36px;
          display: flex;
          justify-content: space-between;
          color: @text-color-label;
          font-size: 14px;
          padding: 0 10px;
          flex-shrink: 0;
          border-bottom: 1px solid @border-color-base;
        }

        .tabs-box {
          overflow: unset;

          :deep(.ant-tabs-tab:first-child) {
            margin-left: 20px;
          }
        }

        .table-actions {
          flex-shrink: 0;
          border-top: 1px dashed @border-color-base;
          text-align: center;
        }

        .top-box {
          display: flex;

          .main-box {
            flex: 1;
            margin-right: 18px;
          }
        }
      }

      .right-pane {
        width: 350px;
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        height: calc(100% + 9px);
        overflow: hidden;
        margin-left: 10px;

        .right-pane-btn {
          flex-shrink: 0;
        }
      }

      .static-right-pane {
        flex: unset;
        width: 350px;
        margin-left: 10px;
        margin-bottom: unset;
      }
    }

    .jsStaticData {
      flex: 1;
      display: flex;
      overflow: hidden;
      flex-direction: column;
      padding: 10px;
      height: 100%;

      .json-box {
        flex: 1;
      }

      .jsTips {
        flex-shrink: 0;
        padding: 8px 16px;
        background-color: @primary-1;
        border-radius: 4px;
        border-left: 5px solid @primary-color;
        margin-top: 10px;

        p {
          line-height: 24px;
          color: @text-color-help-dark;
        }
      }
    }

    .icon-ym-btn-edit {
      color: @primary-color;
      cursor: pointer;
      font-size: 16px;
    }

    .icon-ym-delete {
      color: @error-color;
      cursor: pointer;
      font-size: 16px;
    }

    .ant-select {
      width: 100% !important;
    }

    .component-container {
      height: 100%;
      overflow: auto;
      padding: 20px 0;
    }
  }
</style>
