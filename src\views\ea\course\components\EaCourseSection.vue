<template>
  <a-modal v-model:visible="visible" title="章节管理" width="95%" :destroyOnClose="true" @cancel="handleClose" :footer="null" style="top: 20px">
    ><div class="xunda-content-wrapper">
      <div class="xunda-content-wrapper-left">
        <div class="xunda-content-wrapper-search-box">
          <BasicForm
            @register="registerSearchForm"
            :schemas="searchSchemas"
            @advanced-change="redoHeight"
            @submit="handleSearchSubmit"
            @reset="handleSearchReset"
            class="search-form">
          </BasicForm>
        </div>
        <div class="xunda-content-wrapper-content bg-white">
          <BasicTable @register="registerTable" ref="tableRef">
            <template #tableTitle>
              <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="HandleAdd()"> {{ t('common.add2Text', '新增') }}</a-button>
              <a-button
                type="link"
                preIcon="icon-ym icon-ym-btn-download"
                @click="openExportModal(true, { columnList: exportColumnList, selectIds: getSelectRowKeys() })">
                {{ t('common.exportText', '导出') }}</a-button
              >
              <a-button
                type="link"
                preIcon="icon-ym icon-ym-btn-upload"
                @click="openImportModal(true, { url: '/ea/courseSection', menuId: searchInfo.menuId })">
                {{ t('common.importText', '导入') }}</a-button
              >
              <a-button type="link" preIcon="icon-ym icon-ym-btn-clearn" @click="handelBatchRemove()"> {{ t('common.batchDelText', '批量删除') }}</a-button>
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'id'">
                <xunda-input v-model:value="record.id" :useMask="column.useMask" :maskConfig="column.maskConfig" :showOverflow="true" detailed />
              </template>
              <template v-if="column.dataIndex === 'courseId'">
                <xunda-input v-model:value="record.courseId" :useMask="column.useMask" :maskConfig="column.maskConfig" :showOverflow="true" detailed />
              </template>
              <template v-if="column.dataIndex === 'parentId'">
                <xunda-input v-model:value="record.parentId" :useMask="column.useMask" :maskConfig="column.maskConfig" :showOverflow="true" detailed />
              </template>
              <template v-if="column.dataIndex === 'displayOrder'">
                <xunda-input v-model:value="record.displayOrder" :useMask="column.useMask" :maskConfig="column.maskConfig" :showOverflow="true" detailed />
              </template>
              <template v-if="column.dataIndex === 'no'">
                <xunda-input v-model:value="record.no" :useMask="column.useMask" :maskConfig="column.maskConfig" :showOverflow="true" detailed />
              </template>
              <template v-if="column.dataIndex === 'description'">
                <p>{{ record.description }}</p>
              </template>
              <template v-if="column.dataIndex === 'version'">
                <xunda-input v-model:value="record.version" :useMask="column.useMask" :maskConfig="column.maskConfig" :showOverflow="true" detailed />
              </template>
              <template v-if="column.dataIndex === 'mode'">
                <xunda-input v-model:value="record.mode" :useMask="column.useMask" :maskConfig="column.maskConfig" :showOverflow="true" detailed />
              </template>
              <template v-if="column.dataIndex === 'extraConfig'">
                <p>{{ record.extraConfig }}</p>
              </template>
              <template v-if="column.key === 'action'">
                <TableAction
                  :actions="[
                    {
                      label: t('common.editText', '编辑'),
                      onClick: handleEdit.bind(null, record),
                    },
                    {
                      label: t('common.delText', '删除'),
                      color: 'error',
                      modelConfirm: {
                        onOk: handleDelete.bind(null, record.id),
                      },
                    },
                  ]" />
              </template>
            </template>
          </BasicTable>
        </div>
      </div>
      <!-- 右侧面板 -->
      <div class="xunda-content-wrapper-center">
        <div class="right-panel" v-if="selectedSection">
          <div class="right-panel-header">
            <span class="section-title">当前章节：</span>
            <a-button type="primary" @click="handleSaveContent">保存内容</a-button>
            <a-button type="warning" @click="handleResetContent">撤销修改</a-button>
          </div>
          <a-tabs v-model:activeKey="activeTabKey" class="editor-tabs">
            <a-tab-pane key="content" tab="内容编辑">
              <div class="editor-container">
                <xunda-editor v-model:value="editValue.content" :height="height" />
              </div>
            </a-tab-pane>
          </a-tabs>
        </div>
        <div class="right-panel-empty" v-else>
          <a-empty description="请选择左侧章节进行编辑" />
        </div>
      </div>
      <Form ref="formRef" @reload="reload" />
      <Detail ref="detailRef" @reload="reload" />
      <ExportModal @register="registerExportModal" @download="handleExport" />
      <ImportModal @register="registerImportModal" @reload="reload" />
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { courseSectionApi, getTree, batchDelete, exportData, columns, searchSchemas } from '@/views/ea/courseSection';
  import { LeftOutlined } from '@ant-design/icons-vue';
  import { getConfigData } from '@/api/onlineDev/visualDev';
  import { ref, reactive, toRefs, onMounted, computed } from 'vue';
  import { useModal } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { BasicForm, useForm } from '@/components/Form';
  import { BasicTable, useTable, TableAction, TableActionType } from '@/components/Table';
  import Form from '@/views/ea/courseSection/Form.vue';
  import Detail from '@/views/ea/courseSection/Detail.vue';
  import { useRoute } from 'vue-router';
  import { cloneDeep } from 'lodash-es';
  import { ExportModal } from '@/components/CommonModal';
  import { downloadByUrl } from '@/utils/file/download';
  import { ImportModal } from '@/components/CommonModal';
  import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
  import { useUserStore } from '@/store/modules/user';
  import { usePermission } from '@/hooks/web/usePermission';
  import { getInfo as getSectionInfo, update as updateSection } from '@/views/ea/courseSection';
  import { isEmpty, isNullOrUnDef } from '@/utils/is';

  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;
  const { hasFormP } = usePermission();
  const route = useRoute();

  defineOptions({ name: 'ea-course-section' });

  const emit = defineEmits(['update:visible']);
  const height = ref(0);

  const props = defineProps({
    courseId: {
      type: String,
      required: true,
    },
    courseName: {
      type: String,
      required: true,
    },
    visible: {
      type: Boolean,
      default: false,
    },
  });

  const { visible } = toRefs(props);
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const activeTabKey = ref('content');
  const selectedSection = ref<any>(null);
  const editValue = ref<any>(null);

  function getEditValue(id) {
    getSectionInfo(id).then((res: any) => {
      selectedSection.value = res.data;
      editValue.value = {
        record: res.data,
        content: res.data.description || '',
      };
    });
  }

  // 添加行点击事件处理
  const handleRowClick = (record: any) => {
    if (!isNullOrUnDef(editValue.value) && !isEmpty(editValue.value) && !isNullOrUnDef(selectedSection.value) && !isEmpty(selectedSection.value)) {
      if (selectedSection.value.description !== editValue.value.content) {
        if (record.id !== selectedSection.value.id) {
          createConfirm({
            iconType: 'warning',
            title: '提示',
            content: '当前章节内容已修改，是否保存？',
            onOk: () => {
              console.log('record.id', record.id);
              handleSaveContent();
            },
          });
        }
      } else {
        if (record.id !== selectedSection.value.id) {
          getEditValue(record.id);
        }
      }
    } else {
      getEditValue(record.id);
    }
  };
  function handleResetContent() {
    if (!isNullOrUnDef(editValue.value) && !isEmpty(editValue.value) && !isNullOrUnDef(selectedSection.value) && !isEmpty(selectedSection.value)) {
      createConfirm({
        iconType: 'warning',
        title: '提示',
        content: '是否撤销本次修改？',
        onOk: () => {
          editValue.value.content = selectedSection.value.description;
        },
      });
    }
  }

  // 添加保存内容方法
  async function handleSaveContent() {
    try {
      var updateParams = { ...editValue.value.record, description: editValue.value.content };
      updateSection(updateParams).then(() => {
        createMessage.success('保存成功');
        getEditValue(updateParams.id);
      });
    } catch (error) {
      createMessage.error('保存失败');
    }
  }
  const [registerExportModal, { openModal: openExportModal, closeModal: closeExportModal, setModalProps: setExportModalProps }] = useModal();
  const [registerImportModal, { openModal: openImportModal }] = useModal();
  const formRef = ref<any>(null);
  const tableRef = ref<Nullable<TableActionType>>(null);
  const detailRef = ref<any>(null);
  const cacheList = ref<any>([]);
  const showKnowledgeModal = ref(false);
  const defaultSearchInfo = {
    menuId: route.meta.modelId as string,
    moduleId: '***********', //模块ID
    superQueryJson: '',
    courseId: props.courseId,
    dataType: 0,
  };
  const state = reactive({
    loading: false,
    key: 0,
    listQuery: { parentId: '0', ...defaultSearchInfo },
  });
  const exportColumnList = computed(() => {
    return columns.map(o => {
      return {
        id: o.dataIndex,
        key: o.dataIndex,
        value: o.dataIndex,
        fullName: o.title,
        __config__: {
          xundakey: 'text',
        },
      };
    });
  });
  const searchInfo = reactive({
    ...cloneDeep(defaultSearchInfo),
  });
  const [registerSearchForm] = useForm({
    baseColProps: { span: 6 },
    showActionButtonGroup: true,
    showAdvancedButton: false,
    actionColOptions: {
      span: 24,
    },
    compact: true,
  });
  const [registerTable, { reload, setLoading, getFetchParams, redoHeight, getSelectRowKeys, clearSelectedRowKeys }] = useTable({
    customRow: (record: any) => {
      return {
        onClick: () => {
          handleRowClick(record);
        },
      };
    },
    api: getTree,
    columns: columns,
    searchInfo: searchInfo,
    clickToRowSelect: false,
    isTreeTable: true,
    defaultExpandAllRows: false,
    pagination: false,
    afterFetch: data => {
      const list = data.map(o => {
        const obj = o;
        if (obj.hasChildren) obj.children = [];
        return obj;
      });
      cacheList.value = cloneDeep(list);
      return list;
    },
    onExpand: async (expanded, record) => {
      if (expanded) {
        if (record.children?.length) return;
        state.loading = true;
        const params = {
          ...searchInfo,
          parentId: record.id,
        };
        const res = await getTree(params);
        const list = res.items.map(o => {
          const obj = o;
          if (obj.hasChildren) obj.children = [];
          return obj;
        });
        state.loading = false;
        record.children = list;
      }
    },
    actionColumn: {
      width: 100,
      title: t('common.actionText', '操作'),
      dataIndex: 'action',
      fixed: 'right',
    },
    rowSelection: {
      type: 'checkbox',
      getCheckboxProps: record => ({
        disabled: record.top,
      }),
    },
  });

  function handleSearchReset() {
    clearSelectedRowKeys();
    searchInfo['parentId'] = '0';
    state.key = +new Date();
    reload();
  }

  function handleSearchSubmit(data) {
    clearSelectedRowKeys();
    let obj = {
      ...defaultSearchInfo,
      superQueryJson: searchInfo.superQueryJson,
      ...data,
    };
    Object.keys(searchInfo).map(key => {
      delete searchInfo[key];
    });
    for (let [key, value] of Object.entries(obj)) {
      searchInfo[key.replaceAll('-', '_')] = value;
    }
    if (!searchInfo.superQueryJson) {
      searchInfo['parentId'] = '0';
    }
    reload({ page: 1 });
  }

  // 编辑
  function handleEdit(record) {
    // 不带工作流
    const data = {
      id: record.id,
      menuId: searchInfo.menuId,
      allList: cacheList.value,
    };
    formRef.value?.init(data);
  }

  // 删除
  function handleDelete(id) {
    const query = { ids: [id] };
    batchDelete(query).then(res => {
      createMessage.success(res.msg);
      clearSelectedRowKeys();
      reload();
    });
  }

  // 查看详情
  function HandleDetail(record) {
    // 不带流程
    const data = {
      id: record.id,
    };
    detailRef.value?.init(data);
  }

  // 新增
  function HandleAdd() {
    // 不带流程新增
    const data = {
      id: '',
      courseId: searchInfo['courseId'],
      menuId: searchInfo.menuId,
      allList: cacheList.value,
    };
    formRef.value?.init(data);
  }

  // 关闭弹窗
  function handleClose() {
    emit('update:visible', false);
  }

  // 导出
  function handleExport(data) {
    let query = { ...getFetchParams(), ...data };
    exportData(query)
      .then(res => {
        setExportModalProps({ confirmLoading: false });
        if (!res.data.url) return;
        downloadByUrl({ url: res.data.url });
        closeExportModal();
      })
      .catch(() => {
        setExportModalProps({ confirmLoading: false });
      });
  }

  // 批量删除
  function handelBatchRemove() {
    const ids = getSelectRowKeys();
    if (!ids.length) return createMessage.error('请选择一条数据');
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要删除这些数据吗, 是否继续?',
      onOk: () => {
        const query = { ids: ids };
        setLoading(true);
        batchDelete(query).then(res => {
          createMessage.success(res.msg);
          clearSelectedRowKeys();
          reload();
        });
      },
    });
  }

  // 设置查询表单
  function setSearchSchema() {}

  onMounted(() => {
    setSearchSchema();
    const viewportHeight = window.innerHeight;
    height.value = viewportHeight * 0.8;
  });
</script>

<style lang="less" scoped>
  .right-panel {
    flex: 0 0 100%;
    overflow: auto;
    padding: 0 20px;
    .right-panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .section-title {
        font-size: 16px;
        font-weight: 500;
      }
    }
  }

  .editor-container {
    margin-top: 16px;
  }

  :deep(.ant-tabs-nav) {
    margin-bottom: 16px;
  }

  .xunda-content-wrapper {
    height: 100%;
    &-left {
      width: 500px;
      margin-right: 16px;
      background-color: #fff;
      border-radius: 2px;
    }

    &-center {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }

    &-search-box {
      padding: 16px;
      background-color: #fff;
      border-radius: 2px;
      margin-bottom: 16px;
    }

    &-content {
      flex: 1;
      overflow: hidden;
      padding: 16px;
      background: #fff;
      border-radius: 2px;
    }
  }

  :deep(.ant-modal-body) {
    padding: 0;
    height: calc(100vh - 120px);
    overflow: auto;
  }

  :deep(.ant-modal-content) {
    height: calc(100vh - 40px);
  }
</style>
