// 工具函数：去除HTML标签
export function stripHtml(html: string): string {
  if (!html) return '';
  return html.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ').trim();
}

// 工具函数：解析选项JSON并转换为可读格式
export function parseOptions(optionStr: string): string {
  if (!optionStr) return '';
  
  try {
    const options = JSON.parse(optionStr);
    if (Array.isArray(options)) {
      return options.map((opt, index) => {
        const label = String.fromCharCode(65 + index); // A, B, C, D...
        return `${label}. ${opt.text || opt.label || opt}`;
      }).join('\n');
    } else if (typeof options === 'object') {
      // 如果是对象格式，尝试提取选项
      const optionList = options.options || options.choices || Object.values(options);
      if (Array.isArray(optionList)) {
        return optionList.map((opt, index) => {
          const label = String.fromCharCode(65 + index);
          return `${label}. ${opt.text || opt.label || opt}`;
        }).join('\n');
      }
    }
    return JSON.stringify(options);
  } catch (error) {
    // 如果不是JSON格式，直接返回原字符串
    return optionStr;
  }
}

// 新增：工具函数：解析填空题答案JSON
export function parseFillBlankAnswer(answerStr: string): string {
  if (!answerStr) return '';
  try {
    const answers = JSON.parse(answerStr);
    if (Array.isArray(answers)) {
      return answers.map(item => `空${item.index || ''}: ${item.answer || ''}`).join('; ');
    }
  } catch (e) {
    // 不是JSON格式，返回原样
  }
  return answerStr;
} 