import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';

// 基础Api
export const studentPaperApi = '/api/exam/studentPaper';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: studentPaperApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: studentPaperApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: studentPaperApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: student<PERSON>aper<PERSON>pi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: studentPaperApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: studentPaperApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: studentPaperApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: studentPaperApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: studentPaperApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: studentPaperApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: studentPaperApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: studentPaperApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: studentPaperApi + `/getForSelect`, data });
}

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('学生'),
    dataIndex: 'registrationName',
    width: 120,
  },
  {
    title: t('考试名'),
    dataIndex: 'name',
    width: 120,
  },
  {
    title: t('考试状态'),
    dataIndex: 'examState',
    width: 120,
  },
  {
    title: t('开始时间'),
    dataIndex: 'beginTime',
    width: 120,
  },
  {
    title: t('结束时间'),
    dataIndex: 'endTime',
    width: 120,
  },
  {
    title: t('试卷配置'),
    dataIndex: 'examConfig',
    width: 120,
  },
  {
    title: t('成绩'),
    dataIndex: 'score',
    width: 120,
  },
  {
    title: t('备注'),
    dataIndex: 'note',
    width: 120,
  },
];
