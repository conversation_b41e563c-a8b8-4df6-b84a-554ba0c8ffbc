import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';

import { defHttp } from '@/utils/http/axios';
const { t } = useI18n();
import { xundaUtils } from '@/utils/xunda';
import { ref } from 'vue';

// 基础Api
export const planTimeConfigApi = '/api/plan/timeConfig';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: planTimeConfigApi + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: planTimeConfigApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: planTimeConfigApi + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: planTimeConfigApi + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: planTimeConfigApi + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: planTimeConfigApi + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: planTimeConfigApi + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: planTimeConfigApi + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: planTimeConfigApi + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: planTimeConfigApi + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: planTimeConfigApi + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: planTimeConfigApi + `/exportExceptionData`, data });
}
// 获取下拉列表
export function getForSelect(data) {
  return defHttp.post({ url: planTimeConfigApi + `/getForSelect`, data });
}

const timeOptionsObj = ref([]);
const planOptionsObj = ref([]);

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'semesterName',
    label: t('计划名称'),
    component: 'Select',
    componentProps: {
      submitOnPressEnter: true,
      showSearch: true,
      options: planOptionsObj.value,
      fieldNames: {
        label: 'name',
        value: 'id',
      },
    },
  },
  {
    field: 'timeType',
    label: t('时间类型'),
    component: 'Select',
    componentProps: {
      submitOnPressEnter: true,
      options: timeOptionsObj.value,
      fieldNames: {
        label: 'fullName',
        value: 'enCode',
      },
    },
  },
  {
    field: 'beginTime',
    label: t('开始日期'),
    component: 'DateRange',
    componentProps: {
      submitOnPressEnter: true,
      format: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    field: 'endTime',
    label: t('开始日期'),
    component: 'DateRange',
    componentProps: {
      submitOnPressEnter: true,
      format: 'YYYY-MM-DD HH:mm:ss',
    },
  },
];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  // {
  //   title: t('计划名称'),
  //   dataIndex: 'semesterName',
  // },
  {
    title: t('时间类型'),
    dataIndex: 'timeType',
    width: 120,
  },
  {
    title: t('开始时间'),
    dataIndex: 'beginTime',
    width: 120,
    customRender: ({ record }) => {
      return xundaUtils.toDateString(record.beginTime);
    },
  },
  {
    title: t('结束时间'),
    dataIndex: 'endTime',
    width: 120,
    customRender: ({ record }) => {
      return xundaUtils.toDateString(record.endTime);
    },
  },
];

export function getPlanTimeConfig(callBack) {
  getDictionaryDataSelector('planTimeConfig').then(res => {
    callBack && callBack(res);
  });
}
