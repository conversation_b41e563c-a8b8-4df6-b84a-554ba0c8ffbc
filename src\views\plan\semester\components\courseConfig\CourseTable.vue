<template>
  <div>
    <BasicTable @register="registerTable" ref="tableRef">
      <template #tableTitle>
        <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="HandleAdd()"> {{ t('common.add2Text', '新增') }}</a-button>
        <a-button type="link" preIcon="icon-ym icon-ym-btn-clearn" @click="handelBatchRemove()"> {{ t('common.batchDelText', '批量删除') }}</a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                label: t('common.delText', '删除'),
                color: 'error',
                modelConfirm: {
                  onOk: handleDelete.bind(null, record.id),
                },
              },
              {
                label: t('common.detailText', '详情'),
                onClick: HandleDetail.bind(null, record),
              },
            ]" />
        </template>
      </template>
    </BasicTable>
    <CourseSelectModal ref="formRef" @success="selectCourse" />
  </div>
</template>

<script setup lang="ts">
  import { courseColumns } from '.';
  import { ref, onMounted } from 'vue';
  import { useModal } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useForm } from '@/components/Form';
  import { BasicTable, useTable, TableAction, TableActionType } from '@/components/Table';
  import { useRoute } from 'vue-router';
  import { cloneDeep } from 'lodash-es';
  import { downloadByUrl } from '@/utils/file/download';
  import { useUserStore } from '@/store/modules/user';
  import CourseSelectModal from './CourseSelectModal.vue';
  const userStore = useUserStore();
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const formRef = ref<any>(null);
  const tableRef = ref<Nullable<TableActionType>>(null);
  const detailRef = ref<any>(null);
  const cacheList = ref<any>([]);
  const optionsObj = ref<any>({});
  const props = defineProps({
    dataSource: {
      type: Array,
      default: [],
    },
  });
  const [registerSearchForm] = useForm({
    baseColProps: { span: 6 },
    showActionButtonGroup: true,
    showAdvancedButton: true,
    compact: true,
  });
  const dataSource = ref<any>([]);
  const [registerTable, { reload, setLoading, setTableData, redoHeight, getSelectRowKeys, clearSelectedRowKeys }] = useTable({
    columns: courseColumns,
    canResize: true,
    resizeHeightOffset: 150,
    immediate: false,
    clickToRowSelect: false,
    afterFetch: data => {
      const list = data.map(o => ({
        ...o,
      }));
      cacheList.value = cloneDeep(list);
      return list;
    },
    actionColumn: {
      width: 100,
      title: t('common.actionText', '操作'),
      dataIndex: 'action',
      fixed: 'right',
    },
    rowSelection: {
      type: 'checkbox',
      getCheckboxProps: record => ({
        disabled: record.top,
      }),
    },
  });

  // 删除
  function handleDelete(id) {
    dataSource.value = dataSource.value.filter(o => o.id !== id); // 删除当前行数据
    setTableData(dataSource.value);
  }

  // 查看详情
  function HandleDetail(record) {
    // 不带流程
    const data = {
      id: record.id,
    };
    detailRef.value?.init(data);
  }

  // 新增
  function HandleAdd() {
    const data = { selectedData: dataSource.value };
    formRef.value?.init(data);
  }

  function selectCourse(data) {
    console.log(data);
    if (!dataSource.value) dataSource.value = [];
    dataSource.value.push(...data);
    setTableData(dataSource.value);
  }

  // 批量删除
  function handelBatchRemove() {
    const ids = getSelectRowKeys();
    if (!ids.length) return createMessage.error('请选择一条数据');
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要删除这些数据吗, 是否继续?',
      onOk: () => {
        const query = { ids: ids };
        setLoading(true);
        // 批量删除
        dataSource.value = dataSource.value.filter(o => !ids.includes(o.id));
        setTableData(dataSource.value);
      },
    });
  }
  function getCourses() {
    return dataSource.value;
  }

  function init(data) {
    dataSource.value = data;
    console.log(dataSource.value);
    setTableData(dataSource.value);
  }

  defineExpose({
    getCourses,
    init,
  });
</script>

<style scoped lang="scss"></style>
