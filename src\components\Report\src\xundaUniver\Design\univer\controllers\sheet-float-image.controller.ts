import { Disposable, ICommandService, Inject } from '@univerjs/core';
import { ComponentManager, IMenuManagerService, RibbonStartGroup } from '@univerjs/ui';
import { SHEETS_IMAGE_MENU_ID } from '@univerjs/sheets-drawing-ui';

import {
  XundaSheetsInsertFloatImageOperation,
  XundaSheetsInsertedFloatImageOperation,
  XundaSheetsFocusFloatImageOperation,
} from '../commands/operations/sheet-float-image.operation';
import { XundaSheetsInsertFloatImageMenuFactory } from './menus/sheet-float-image.menu';

import univerFloatImageComponent from '../components/Image/float.vue';

import { XundaUniverFloatImageKey } from '../utils/define';

export class XundaSheetsFloatImageController extends Disposable {
  constructor(
    @ICommandService private readonly _commandService: ICommandService,
    @IMenuManagerService private readonly _menuManagerService: IMenuManagerService,
    @Inject(ComponentManager) private readonly _componentManager: ComponentManager,
  ) {
    super();

    this._initCommands();
    this._registerComponents();
    this._initMenus();
  }

  private _initCommands(): void {
    [XundaSheetsInsertFloatImageOperation, XundaSheetsInsertedFloatImageOperation, XundaSheetsFocusFloatImageOperation].forEach(command => {
      this.disposeWithMe(this._commandService.registerCommand(command));
    });
  }

  private _registerComponents(): void {
    // 注册图片组件进来
    this.disposeWithMe(this._componentManager.register(XundaUniverFloatImageKey, univerFloatImageComponent, { framework: 'vue3' }));
  }

  private _initMenus(): void {
    this._menuManagerService.mergeMenu({
      [RibbonStartGroup.FORMULAS_INSERT]: {
        [SHEETS_IMAGE_MENU_ID]: {
          [XundaSheetsInsertFloatImageOperation.id]: {
            order: 10,
            menuItemFactory: XundaSheetsInsertFloatImageMenuFactory,
          },
        },
      },
    });
  }
}
